//check if browser supports WEBP
export function isWebpSupported() {
  const elem = document.createElement('canvas');
  if (!!(elem.getContext && elem.getContext('2d'))) {
    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }
  return false;
}
// Observe sections/divs for lazy loading
export function observeSections(callback) {
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        callback(entry.target.id);
        // observer.unobserve(entry.target); // Stop observing once the section is intersecting
      }
    });
  });
  document.querySelectorAll('.lazy-section').forEach(section => {
    observer.observe(section);
  });
  return () => {
    observer.disconnect();
  };
}

// Observe videos for lazy loading
export function observeVideos(callback) {
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const video = entry.target.querySelector('video');
        if (video) {
          const source = video.querySelector('source');
          if (source && source.getAttribute('data-src')) {
            source.setAttribute('src', source.getAttribute('data-src'));
            video.load();
            callback(entry.target.id);
          }
        }
        observer.unobserve(entry.target);
      }
    });
  });

  document.querySelectorAll('.lazy-video').forEach(video => {
    observer.observe(video);
  });

  return () => {
    observer.disconnect();
  };
}

// Hash pp_ctaclr
export function hexHash(hex) {
  const hexRegex = /^[0-9a-f]*$/i;
  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {
    return `#2872FA`;
  }

  hex = hex.slice(0, 6);

  return `#${hex}`;
}

// Darken color when hovering
export function hoverDarken(hex) {
  const hexRegex = /^[0-9a-f]*$/i;
  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {
    hex = '2872FA';
  }

  let r = parseInt(hex.slice(0, 2), 16);
  let g = parseInt(hex.slice(2, 4), 16);
  let b = parseInt(hex.slice(4, 6), 16);

  r = Math.max(0, Math.min(255, r - (r * 0.15)));
  g = Math.max(0, Math.min(255, g - (g * 0.15)));
  b = Math.max(0, Math.min(255, b - (b * 0.15)));

  const toHex = (value) => {
    const hexValue = Math.round(value).toString(16);
    return hexValue.padStart(2, '0');
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

// Lighten color and set opacity to 0.9 when hovering
// Lighten color and set slightly reduced opacity when hovering
export function hoverLighten(hex) {
  const hexRegex = /^[0-9a-f]*$/i;
  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {
    hex = '2872FA';
  }

  let r = parseInt(hex.slice(0, 2), 16);
  let g = parseInt(hex.slice(2, 4), 16);
  let b = parseInt(hex.slice(4, 6), 16);

  // Lighten by increasing each channel by 15%
  r = Math.max(0, Math.min(255, r + (255 - r) * 0.25));
  g = Math.max(0, Math.min(255, g + (255 - g) * 0.25));
  b = Math.max(0, Math.min(255, b + (255 - b) * 0.25));

  return `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, 0.9)`;
}
