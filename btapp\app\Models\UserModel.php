<?php
namespace App\Models;

use CodeIgniter\Model;

class UserModel extends BTModel
{
    protected $table = 'user';
    protected $primaryKey = 'user_id';

    protected $allowedFields = [
        'user_pid',
        'access_token',
        'login_token',
        'orig_email',
        'email',
        'password',
        'first_name',
        'last_name',
        'status',
        'ip_address',
        'mode',
        'website',
        'flags',
        'survey_data',
        'ent_parent_user_id',
        'social_login',
        'is_fraud',
        'country'
    ];

    protected $returnType = 'App\Entities\User';

    protected $skipValidation = false;

    protected $validationRules = [
        'email' => 'required|valid_email',
        'password' => 'required|min_length[8]'
    ];

    protected $validationMessages = [
    ];

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function checkIfEmailExist($email, $user_id = null)
    {
        $query = $this->db->table($this->table)
            ->groupStart()
                ->where('email', $email)
            ->groupEnd();

        if($user_id) $query = $query->where('user_id !=', $user_id);
        $query = $query->where('deleted_at', "0000-00-00 00:00:00")
            ->get();
        $results = $query->getResult();

        return $results;
    }

    public function getEnterpriseMembers($user_id) {
		if (is_null($this->table)) {
			return ['success' => 0, 'res' => []];
		}

		$data = $this->builder($this->table)
        ->select('user_pid, user_id, first_name, last_name, email, status, created_at')
        ->where('ent_parent_user_id', $user_id)
        ->where('deleted_at', "0000-00-00 00:00:00")
        ->get();

		if ($this->errors()) {
			$res = $this->errors();
			return ['success' => 0, 'res' => $res];
		}

		return ['success' => 1, 'res' => $data->getResultObject()];
    }
    
}