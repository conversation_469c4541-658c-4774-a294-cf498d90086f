<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* includes/head_bing.twig */
class __TwigTemplate_943ae029764beea38ddd8f32d5f39f46 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if ((twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_bing", [], "any", false, false, false, 1) == true)) {
            // line 2
            echo "<script> (function(w,d,t,r,u) { var f,n,i; w[u]=w[u]||[],f=function() { var o={ti:\"97041776\", enableAutoSpaTracking: true}; o.q=w[u],w[u]=new UET(o),w[u].push(\"pageLoad\") }, n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function() { var s=this.readyState; s&&s!==\"loaded\"&&s!==\"complete\"||(f(),n.onload=n.onreadystatechange=null) }, i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i) }) (window,document,\"script\",\"https://bat.bing.com/bat.js\",\"uetq\"); </script>
";
        }
    }

    public function getTemplateName()
    {
        return "includes/head_bing.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "includes/head_bing.twig", "C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\twig\\includes\\head_bing.twig");
    }
}
