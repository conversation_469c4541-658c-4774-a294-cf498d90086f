import React, { useState, useEffect, useRef } from 'react';
import './style.css';
import { motion } from "framer-motion";
import { useQuery } from "react-query";
import axios from 'axios';
import { GetCookie, SetCookie } from '../core/utils/cookies';
import { hexHash, hoverDarken } from '../core/utils/helper';
import { displayTextFormatted, PriceFormatted, displayFormattedLabel } from '../core/utils/main';
import { Helmet } from 'react-helmet';
import _ from 'underscore';
import { useTranslation } from 'react-i18next';
import BackToTop from './components/BackToTop';

import { Auth } from '../core/utils/auth';

import TpReviews from '../../../basilisk/src/features/tpreviews';

var plan = null;
var showToggle = false;
var hasAnnual = false;

async function getPPG() {
  var ppg = GetCookie("ppg");
  if (ppg==='') {
    ppg =  process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : '14';
  }
  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-pricing`, { ppg }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });
  const output = response.data;
 
  if(output.success) {
    plan = output.data;
    plan = output.data.filter((value) => {
      if (parseInt(ppg) === 59) return value.plan_id !== process.env.REACT_APP_ENTERPRISE_ID
      return true
    });
    hasAnnual = _.some(plan, function(o) { return o.payment_interval.toLowerCase() === "yearly"; });
    showToggle = _.some(plan, function(o) { return o.payment_interval.toLowerCase() === "monthly"; }) && hasAnnual;
    return plan;
  } else {
    return [];
  }
}

function Pricing() {
  const { t } = useTranslation();
  const headerRef = useRef(null);
  // const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : "";
  var ppg = GetCookie("ppg") ? GetCookie("ppg") : process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : "14";
  var pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "2872FA";
  var ptoggle = GetCookie("p_toggle") ? GetCookie("p_toggle") : "";
  const ppgArrayWithToggle = ['97', '109', '101'];

  const auth = Auth();
  
  showToggle = (ppgArrayWithToggle.includes(ppg) && showToggle) ? showToggle : false;
  var billedAnnualDisplay = false;

  useEffect(() => {
    const handleScroll = () => {
      // const headerBottom = headerRef.current.getBoundingClientRect().bottom;
      // setIsHeaderVisible(window.pageYOffset < headerBottom);
    };
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  },);

  if (ppg==='48'){
    billedAnnualDisplay = true;
  }
  if(ptoggle === "01" || ptoggle === "03" || ppg === 109 || ppg === 101){
    billedAnnualDisplay = true;
  }

  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? "yearly" : "monthly");
  const intervalChange = function() {
    if(planInterval === "monthly") {
      setPlanInterval("yearly");
    } else {
      setPlanInterval("monthly");
    }
  };

  const checkPlanInterval = function(plan) {
    if(!showToggle) return true;
    if(plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }

  const { data } = useQuery("users", getPPG);
  if(data === undefined) return;
  // const tk = GetCookie("access");

  const setPricing = function(id) {
    SetCookie('pricing', id, { path: '/' });
    window.location.href = "/pay";
  };

  return (
    <>
      <Helmet>
        <title>{t('basilisk.pricing.index.plans_and_pricing')}</title>
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      </Helmet>
        <div ref={headerRef}></div>
        <BackToTop/>
        {!['97', '101', '109'].includes(ppg) && (
          <div className="fixed top-0 left-0 w-full h-full z-[-1] flow04-bg"></div>
        )}
        <section className={`${!['97', '101', '109'].includes(ppg) ? 'pricing' : 'pt-[20px]'} ${ptoggle === '03' ? '!pt-0' : ''} max-h-900:pt-[0px]`}>
          <div className={`pricing_columns mx-auto pb-6 xl:max-w-[1140px]`}>
            <div className={`${!['97', '101', '109'].includes(ppg) ? 'pb-6' : 'py-6'} flex flex-col pricing-page items-center lg:pb-6 lg:pt-0 bg-white rounded-[10px] w-full max-w-[87rem] mx-auto`}>
              {(ppg === '97' || ppg === '109' || ppg === '101') ? (
                  <h1 className={`text-[36px] text-[#192A3D] !font-semibold text-center poppins-font ${showToggle ? 'md:max-h-768:text-[25px] md:h-769-800:text-[28px] md:h-801-900:text-[32px] md:max-h-768:mt-[4px] md:h-769-800:mt-[6px] md:h-801-900:mt-[10px]' : ''}`}>
                    {t('basilisk.pricing.index.get_full_access_01')}
                  </h1>
                ) : (ppg === '14') ? (
                  <h1 className="text-[30px] text-[#192A3D] !font-bold text-center poppin my-[20px]">
                    {t('basilisk.pricing.index.get_full_access_01')}
                  </h1>
                ) : (ppg === '14') ? (
                  <h1 className="text-[40px] text-[#192A3D] !font-bold text-center poppin mb-[20px]">
                    {t('basilisk.pricing.index.get_full_access_01')}
                  </h1>
                ) : (
                  <h1 className="text-[30px] text-[#192A3D] !font-bold text-center poppin my-[20px]">
                    {t('basilisk.pricing.index.get_full_access_01')}
                  </h1>
              )}
              { showToggle ? (
                <div className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "poppins-font" : "system"} p-1`}>
                    <div className={`text-1xl lg:text-1xl font-bold text-center ${showToggle ? 'mb-[30px] md:max-h-768:!mb-[20px] md:h-769-800:!mb-[24px] md:h-801-900:!mb-[30px]' : 'mb-8'}`}>
                    <div>{t('basilisk.pricing.index.choose_options')}</div>
                    </div>
                    <div className={`flex items-center justify-center w-full mb-8 ${showToggle ? 'md:max-h-768:!mb-[16px] md:h-769-800:!mb-[20px] md:h-801-900:!mb-[24px]' : ''}`}>
                    <label for="toggleB" className="flex items-center cursor-pointer">
                        <div className={`${planInterval === 'monthly' ? "text-[#2872fa] font-bold" : "text-gray-700"} mr-3 uppercase`}>
                            {t('basilisk.pricing.index.monthly')}
                        </div>
                        <div className="relative">
                        <input type="checkbox" id="toggleB" className="sr-only toggle" onChange={intervalChange} defaultChecked={billedAnnualDisplay}/>
                        <div className="block bg-gray-400 w-12 h-6 rounded-full"></div>
                        <div className="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                        </div>
                        <div className={`${planInterval === 'yearly' ? "text-[#2872fa] font-bold" : "text-gray-700"} ml-3 uppercase`}>
                            {t('basilisk.pricing.index.yearly')}
                        </div>
                    </label>
                    </div>
                </div>
                ) : ""}
            { showToggle ? (
              <div className="pricing-toggle md:flex flex-col md:flex-row justify-center h-full">
                {data?.map((plan, index) => (
                    checkPlanInterval(plan) ? (
                        <div key={index} className={`price_col text-center mb-8 mx-auto px-[15px] sm:mx-3 ${ index === 1 ? "relative" : "" }`}>
                            <div className="bg-white rounded-md shadow-lg hover:shadow-xl overflow-hidden mx-auto lg:w-[328px] hover:transform hover:translate-y-[-6px] h-full flex flex-col">
                                <div className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "py-6" : "pb-6"} price-content`}>
                                    <div>
                                        { plan.payment_interval.toLowerCase() === "yearly" && (
                                            <div className="block bg-blue-100 text-blue-500 px-2 py-1 text-[11px] mb-[10px] font-semibold mt-[-28px]">
                                                Up to 20% OFF on an annual subscription
                                            </div>
                                        )}
                                        <h3 className={`${ppg === '97' || ppg === '109' || ppg === '101' ? `poppins-font text-[25px] mb-4 ${showToggle ? 'md:h-801-900:!mb-3' : ''}` : 'oswald-font text-[22px] my-[10px]'} text-[#2872FA]`}>
                                            {["03", "05", "72", "111"].includes(ppg) ? t('basilisk.pricing.index.trial_days') : displayFormattedLabel(plan)}
                                        </h3>
                                        <div className={`bg-[#2872FA] content-center ${ billedAnnualDisplay ? "min-h-[90px]" : "" }`}>
                                            { 
                                              plan.label.toLowerCase()==="enterprise" ? <p className="text-[25px] text-white lg:h-[50px] p-[6px] poppin">{t('basilisk.pricing.index.custom_plan')}</p> :
                                              <PriceFormatted plan={plan}/>
                                            }
                                        </div>
                                    </div>
                                    <div className="flex-grow flex flex-col justify-between h-full">
                                        <div className={`px-8 pt-[10px] ${showToggle ? 'md:max-h-768:!min-h-[340px] md:h-769-800:!min-h-[370px] md:h-801-900:!min-h-[380px]' : ''}`}>
                                            <ul className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "poppins-font" : "system"} text-sm text-[#2872FA] text-left`}>
                                                { plan.display_txt2 ? <li className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "leading-[24px]" : "leading-[19px]"} mb-2 ${showToggle ? 'md:max-h-768:!leading-[17px] md:h-769-800:!leading-[19px] md:h-801-900:!leading-[20px]' : ''}`} dangerouslySetInnerHTML={{__html: displayTextFormatted(plan)}}></li> : null }
                                            </ul>
                                        </div>
                                        <div className="mt-auto pb-4">
                                            <motion.button
                                                className="arial-font bg-[#2872fa] text-[20px] text-white font-bold py-2 px-6 rounded-md w-[201px]"
                                                style={{backgroundColor: hexHash(pp_ctaclr)}}
                                                whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                                                whileTap={{ scale: 0.9 }}
                                                onClick={() => setPricing(plan.plan_id)}
                                            >
                                                {plan.label.toLowerCase()==="enterprise" ? t('basilisk.pricing.index.build_my_plan') : t('basilisk.pricing.index.continue')}
                                            </motion.button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : ""
                ))}
              </div>
              ) : (
                <div className={`md:flex flex-col md:flex-row justify-center`}>
                    {data?.map((plan, index) => (
                    <div key={index} className={`price_col text-center mb-8 mx-auto px-[15px] sm:mx-3 h-full flex flex-col ${ index === 1 ? "relative" : "" }`}>
                        <div className="bg-white rounded-md shadow-lg hover:shadow-xl overflow-hidden mx-auto lg:w-[328px] hover:transform hover:translate-y-[-6px] h-full flex flex-col">
                            <div className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "py-6" : "pb-6"} price-content flex flex-col h-full`}>
                                <div>
                                    <h3 className={`${ppg === ' 97' || ppg === '109' || ppg === '101' ? `poppins-font text-[25px] mb-4 ${showToggle ? 'md:h-801-900:!mb-3' : ''}` : 'oswald-font text-[22px] my-[10px]'} text-[#2872FA]`}>
                                        {["03", "05", "72", "111"].includes(ppg) ? t('basilisk.pricing.index.trial_days') : displayFormattedLabel(plan)}
                                    </h3>
                                    <div className={`bg-[#2872FA] content-center ${ billedAnnualDisplay ? "min-h-[90px]" : "" }`}>
                                        { 
                                        plan.label.toLowerCase()==="enterprise" ? <p className="text-[25px] text-white mb-6 poppin lg:h-[50px] p-[6px]">{t('basilisk.pricing.index.custom_plan')}</p> :
                                        <PriceFormatted plan={plan}/>
                                        }
                                        {
                                        (plan.ppg==='108' && plan.plan_type==='Pro') ? 
                                        <div className='discount-text poppins-font'>
                                          or $197 annual <span className='discount-bubble poppins-font'>30% off</span>
                                        </div>
                                        : 
                                        (plan.ppg==='108' && plan.plan_type==='ProMax') ? 
                                        <div className='discount-text poppins-font'>
                                          or $932 annual <span className='discount-bubble poppins-font'>20% off</span>
                                        </div>
                                        : ''
                                        }
                                    </div>
                                </div>
                                <div className="flex-grow flex flex-col justify-between h-full">
                                    <div className="mb-12 px-8 pt-[10px] lg:min-h-[360px] mt-[12px] poppin md:max-h-900:mb-0">
                                        <ul className="text-sm text-[#2872FA] text-left">
                                            { plan.display_txt2 ? <li className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "leading-[24px]" : "leading-[19px]"} mb-2 ${showToggle ? 'md:max-h-768:!leading-[17px] md:h-769-800:!leading-[19px] md:h-801-900:!leading-[20px]' : ''}`} dangerouslySetInnerHTML={{__html: displayTextFormatted(plan)}}></li> : null }
                                        </ul>
                                    </div>
                                    <div className="mt-auto pb-4">
                                        <motion.button
                                            className="arial-font bg-[#2872fa] text-[20px] text-white font-bold py-2 px-6 rounded-md w-[201px]"
                                            style={{backgroundColor: hexHash(pp_ctaclr)}}
                                            whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                                            whileTap={{ scale: 0.9 }}
                                            onClick={() => setPricing(plan.plan_id)}
                                        >
                                            {plan.label.toLowerCase()==="enterprise" ? t('basilisk.pricing.index.build_my_plan') : t('basilisk.pricing.index.continue')}
                                        </motion.button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ))}
                </div>
              )}

              {!auth && (ppg !== '97' && ppg !== '109' && ppg !== '101') && (
                <div className="py-4 text-sm text-[#3a4f66]">
                  Already have an account? <a href="https://start.ai-pro.org/login" className="text-blue-500">Login</a>
                </div>
              )}
            </div>
       
            <div className='bg-white rounded-[10px] pb-[63px] w-full max-w-[87rem] mx-auto'>
              {
                tpreviews === "on" ? 
                <>
                  <TpReviews/>
                </>
                : 
                <div className='mt-[30px]'>
                    {(ppg !== '97' && ppg !== '101' && ppg !== '109') ? (
                      <p className='text-center text-[20px] font-bold pt-[50px] pr-[0] pb-[20px] pl-[0]'>{t('basilisk.pricing.index.try_chatpro_today')}</p>
                    ) : (
                      <div className='pt-[50px] pr-[0] pb-[20px] pl-[0]'>
                        <span></span>
                      </div>
                    )}
                    <div className='mx-auto px-[16px] md:px-0'>
                      <img src='https://assets.ai-pro.org/assets/wp-content/uploads/2024/06/1.png' alt="chatgpt" className="lp_img mx-auto"/> 
                    </div>
                </div>
              }
            </div>
          </div>

          {/*<Faq />
          <div className="ask container mx-auto">
            <div className="flex flex-col items-center py-10 lg:py-16 border-2 border-gray-200 rounded-2xl">
              <h2 className="text-3xl font-bold text-center mb-6 lg:mb-8">
                Ready to take your website<br/>to the next level?
              </h2>
              <p className="text-md max-w-xl text-center leading-relaxed mb-10 lg:mb-12">
                Create your own AI chatbot with no code and watch it learn from your website content in real-time.
              </p>
              <button className="ctabtn gradient-hover-effect text-white font-bold py-3 px-6 rounded-lg">
                Create Your Chatbot Now&nbsp;
                 <i className="fas fa-sign-in-alt"></i>
              </button>
            </div>
            <div className="bgradient"></div>
          </div>*/}
        </section>
      {/*<Footer />*/}
    </>
  );
}

export default Pricing;