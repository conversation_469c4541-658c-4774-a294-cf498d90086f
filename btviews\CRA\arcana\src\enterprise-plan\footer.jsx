"use client"

import { useState, useEffect } from 'react';
import imgAIPROHeader from '../assets/images/AIPRO-200x45-1-1.svg';

export default function Footer() {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  useEffect(() => {
    const targetElement = document.getElementById("hero-section");

    if (!targetElement) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setScrollToTopVisible(!entry.isIntersecting); // show button when the element is out of view
      },
      { threshold: 0.1 }
    );

    observer.observe(targetElement);

    return () => {
      observer.disconnect(); // cleanup
    };
  }, []);

  return (
    <footer className="w-full bg-[#1a1a1a] text-white">
      <div className="ep_container px-4 py-8 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-6">
          <div className="flex items-center justify-center">
            <img src={imgAIPROHeader} alt="AI-PRO Logo" width={170} height={40} />
          </div>

          <nav className="flex flex-col md:flex-row flex-wrap justify-center gap-4 text-sm text-gray-300">
            <a className="hover:text-white text-center" href="/disclaimer">Disclaimer</a>
            <a className="hover:text-white text-center" href="/affiliates">Affiliates</a>
            <a className="hover:text-white text-center" href="/member-tos-page">Terms of Services</a>
            <a className="hover:text-white text-center" href="/privacy-policy">Privacy Policy</a>
            <a className="hover:text-white text-center" href="/fair-usage-policy">Usage Policy</a>
            <a className="hover:text-white text-center" href="/contact-us">Contact Us</a>
          </nav>

          <div className="text-center text-sm text-gray-300">Copyright © 2025 | ai-pro.org</div>

          <div className="max-w-[1000px] text-justify text-xs text-gray-400">
            Disclaimer: The information provided on this website is for general informational purposes only. While we
            strive to keep the information up to date and correct, we make no representations or warranties of any kind,
            express or implied, about the completeness, accuracy, reliability, suitability or availability with respect
            to the website or the information, products, services, or related graphics contained on the website for any
            purpose. Any reliance you place on such information is therefore strictly at your own risk. Furthermore, we
            are not in any way affiliated or related to OpenAI and Stability AI.
            <a href="/disclaimer" className="ml-1 text-[#2872FA] hover:underline">Read more here</a>.
          </div>
        </div>
      </div>

      {scrollToTopVisible && <button
        onClick={scrollToTop}
        className="fixed bottom-4 right-4 rounded-full bg-[#3A4F66] p-2 text-white shadow-lg transition-all hover:bg-gray-600"
        aria-label="Scroll to top"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          {/* <path d="m18 15-6-6-6 6" /> */}
          <line x1="12" y1="19" x2="12" y2="5" />
          <polyline points="5 12 12 5 19 12" />
        </svg>
      </button>}
    </footer>
  )
}
