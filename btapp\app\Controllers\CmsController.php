<?php

namespace App\Controllers;

use App\Services\StrapiResolverService;
use App\DTOs\CmsPostDTO;
use App\DTOs\CmsCollectionPageDTO;
use App\Services\CmsSeoService;
use CodeIgniter\HTTP\ResponseInterface;

class CmsController extends BaseController
{
    /**
     * @var \CodeIgniter\HTTP\RequestInterface
     */
    protected $request;

    /**
     * @var StrapiResolverService
     */
    protected $resolver;
    
    /**
     * @var CmsSeoService
     */
    protected $seoService;

    protected $is_ajax_request = false;
    protected $full_page_cache = false; // Enable full page cache by default
    private $nonce = '';

    const MIME_XML = 'application/xml';
    const MIME_XSL = 'application/xslt+xml';
    const MIME_TXT = 'text/plain';

    const PAGE_SIZE = 8; // Default page size for collections

    /**
     * Constructor with dependency injection
     */
    public function __construct()
    {
        $this->request = service('request');
        $this->nonce = base64_encode(random_bytes(16));
        
        $this->resolver = new StrapiResolverService();
        $this->seoService = new CmsSeoService();
    }

    /**
     * Main entry point for CMS routes
     *
     * @param string $slug The URL slug
     * @return ResponseInterface|string|null
     */
    public function index(string $slug = '')
    {
        // Handle special routes like sitemaps and robots.txt
        $specialResponse = $this->handleSpecialRoutes($slug);
        if ($specialResponse !== null) {
            return $specialResponse;
        }

        // Handle regular content routes
        return $this->handleContentRoutes($slug ?: service('uri')->getPath());
    }

    /**
     * Handle special routes like sitemaps and robots.txt
     *
     * @param string $slug
     * @return ResponseInterface|null
     */
    protected function handleSpecialRoutes(string $slug) : ?ResponseInterface
    {
        $response = service('response');

        if ($slug !== 'robots.txt') {
            header("Content-Security-Policy: default-src 'self'; style-src 'self' 'unsafe-inline'");
        }

        switch ($slug) {
            case 'sitemap.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getSitemapXml();
                exit();
            case 'sitemap.xsl':
                header('Content-Type: ' . self::MIME_XSL);
                echo $this->seoService->getSitemapXsl();
                exit();
            case 'sitemap-index.xsl':
                header('Content-Type: ' . self::MIME_XSL);
                echo $this->seoService->getSitemapXslIndex();
                exit();
            case 'sitemap-main.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getSitemapMainXml();
                exit();
            case 'sitemap-articles.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getSitemapArticlesXml();
                exit();
            case 'sitemap-pages.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getSitemapPagesXml();
                exit();
            case 'main-sitemap.xsl':
                header('Content-Type: ' . self::MIME_XSL);
                echo $this->seoService->getMainSitemapXsl();
                exit();
            case 'sitemap_index.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getSitemapIndexXml();
                exit();
            case 'page-sitemap.xml':
                header('Content-Type: ' . self::MIME_XML);
                echo $this->seoService->getPageSitemapXml();
                exit();
            case 'robots.txt':
                header('Content-Type: ' . self::MIME_TXT);
                echo $this->seoService->getRobotsTxt();
                exit();
            default:
                return null;
        }
    }

    /**
     * Handle content routes (posts, pages, categories, tags)
     *
     * @param string $uri
     * @return ResponseInterface|string|null
     */
    protected function handleContentRoutes(string $uri)
    {
        // set flags
        echo view('cms/flags', ['nonce' => $this->nonce]);

        // Check if redirection is needed
        $redirectResponse = $this->resolver->checkRedirect($uri);
        if ($redirectResponse) {
            return $redirectResponse;
        }

        // check if cms can resolve the slug
        $result = $this->resolver->resolveSlug($uri);
        if (!$result || !isset($result['type']) || isset($result['type']) && $result['type'] === 'not_found') {
            header_remove("Content-Security-Policy");
            return null;
        }

        if ($this->full_page_cache === true) {
            // Build cache key
            $queryString = !empty($_GET) ? '?' . http_build_query($_GET) : '';
            $key = $this->resolver->cacheKeyPrefix . md5(
                $uri . '-' . $queryString . '_' . date('Y-m-d')
            );
        
            // Try to get cached content
            $cached = $this->resolver->getCached($key);
        
            if ($cached && $cached !== 'refresh') {
                return $cached;
            }
        }
       
        // Render appropriate view based on content type
        $responseData = $this->renderContent($result);
        
        // Cache the result if needed
        if ($this->full_page_cache === true) {
            if ($responseData && ($cached === 'refresh' || $cached === true)) {
                $this->resolver->setCached($key, $responseData);
            }
        }
        
        return $responseData;
    }
    
    /**
     * Render content based on type
     *
     * @param array $result
     * @param string $uri
     * @return string|null
     */
    protected function renderContent(array $result): ?string
    {
        $this->setSecurityHeaders($this->nonce);

        // ajax request
        $this->is_ajax_request = $this->request->isAJAX();

        switch ($result['type']) {
            case 'post':
                return $this->renderPost($result);
            case 'page':
                return $this->renderPage($result);
            case 'category':
                return $this->renderCategoryCollection($result);
            case 'tag':
                return $this->renderTagCollection($result);
            default:
                return null;
        }
    }
    
    /**
     * Render a post
     *
     * @param array $result
     * @param string $uri
     * @return string
     */
    protected function renderPost(array $result): string
    {
        $main_post_dto = CmsPostDTO::fromStrapiResult($result);
        
        $prev_post_dto = !empty($result['data']['prev_post'])
            ? CmsPostDTO::fromStrapiResult(['data' => $result['data']['prev_post']])->toView()
            : null;
            
        $next_post_dto = !empty($result['data']['next_post'])
            ? CmsPostDTO::fromStrapiResult(['data' => $result['data']['next_post']])->toView()
            : null;
            
        $data = $main_post_dto->toArray();
        $data['prev_post'] = $prev_post_dto ? [$prev_post_dto] : [];
        $data['next_post'] = $next_post_dto ? [$next_post_dto] : [];

        $data['recent_posts'] = $this->getRelatedPosts('', '', 4);

        $data['nonce'] = $this->nonce; // Add nonce for CSP
        
        // this is for ld+json
        $data['type'] = 'Article';

        return view('cms/layouts/post', $data);
    }
    
    /**
     * Render a page
     *
     * @param array $result
     * @param string $uri
     * @return string
     */
    protected function renderPage(array $result): string
    {
        $basePath = APPPATH . 'Views/cms/pages/';

        //$main_post_dto = CmsPostDTO::fromStrapiResult($result)->toView();
        $data = CmsPostDTO::fromStrapiResult($result)->toArray();

        $data['nonce'] = $this->nonce; // Add nonce for CSP
        
        $template = $data['template'] ?? 'default';

        // landing page template
        if ($template === 'lp') {
            helper('btflag');
            echo $data['content'];
            echo view('cms/pages/lp/spa_flags', ['nonce' => $this->nonce]);
            echo view('cms/pages/lp/lp-analytics-settings-spa', ['nonce' => $this->nonce]);
            //echo view('cms/partials/scripts/cookieyes', ['nonce' => $this->nonce]);
            //include(APPPATH . 'Views/cms/pages/lp/spa_flags.php');
            //include(APPPATH . 'Views/cms/pages/lp/lp-analytics-settings-spa.php');
            die();
        }

        // this is for ld+json
        $data['type'] = 'WebPage';

        $filename = $data['template'] ? $basePath . $data['template'] . '.php' : null;
        if ($filename && is_file($filename)) {
            $data['content'] = view('cms/pages/' . $data['template'], $data);
        }
       
        return view('cms/layouts/page', $data);
    }
    
    /**
     * Render a category page
     *
     * @param array $result
     * @return string
     */
    protected function renderCategoryCollection(array $result): string
    {
        $main_tag_dto = CmsCollectionPageDTO::fromStrapiResult($result);
        $data = $main_tag_dto->toArray();

        $data['articles'] = $this->getRelatedPosts('categories', $result['data']['slug']);
        //$data['page'] = $data;
        $data['slug1'] = $data['slug'] ?? '';
        $data['description'] = ''; // to do, because description of the category exported from wordpress is a plugin

        $data['nonce'] = $this->nonce; // Add nonce for CSP
        
        // this is for ld+json
        $data['type'] = 'CollectionPage';

        // if ajax request, return only articles
        if ($this->is_ajax_request) {
            return view('cms/partials/content_collection', $data);
        }

        $data['current_page'] = $this->resolver->getCurrentPage();
        $data['total_pages'] = $this->resolver->getTotalPages();

        return view('cms/layouts/collection', $data);
    }
    
    /**
     * Render a tag page
     *
     * @param array $result
     * @param string $uri
     * @return string
     */
    protected function renderTagCollection(array $result): string
    {
        $main_tag_dto = CmsCollectionPageDTO::fromStrapiResult($result);
        $data = $main_tag_dto->toArray();
        
        $data['articles'] = $this->getRelatedPosts('tags', $result['data']['slug']);
        $data['page'] = $data;
        $data['slug1'] = $data['slug'] ?? '';

        $data['nonce'] = $this->nonce; // Add nonce for CSP
        
        // this is for ld+json
        $data['type'] = 'CollectionPage';

        // if ajax request, return only articles
        if ($this->is_ajax_request) {
            return view('cms/partials/content_collection', $data);
        }

        $data['current_page'] = $this->resolver->getCurrentPage();
        $data['total_pages'] = $this->resolver->getTotalPages();

        return view('cms/layouts/collection', $data);
    }
    
    /**
     * Get posts related to a taxonomy term
     *
     * @param string $taxonomy 'categories' or 'tags'
     * @param string $slug The taxonomy term slug
     * @return array
     */
    protected function getRelatedPosts(string $taxonomy, string $slug, int $page_size = self::PAGE_SIZE): array
    {
        // url parameter search
        $search_query = $this->request->getGet('q') ?? '';

        $params = [
            'sort' => ['0' => 'createdAt:desc'],
            'fields' => ['title', 'slug', 'excerpt', 'content', 'post_date','post_date_gmt','post_modified','post_modified_gmt','createdAt','updatedAt'],
            'pagination' => ['pageSize' => $page_size, 'page' => $this->resolver->getCurrentPage()],
            'filters' => [
                '$and' => [
                    ['frontend_status' => ['$eq' => 'published']],
                    (!empty($taxonomy) && !empty($slug)) ? [
                        $taxonomy => ['slug' => ['$eq' => $slug]]
                    ] : null,
                ],
                '$or' => [
                    ['title' => ['$containsi' => $search_query]],
                    ['excerpt' => ['$containsi' => $search_query]],
                    ['content' => ['$containsi' => $search_query]]
                ]
            ],
            'populate' => [
                /*'categories' => ['fields' => ['0' => 'name', '1' => 'slug']],*/
                'featured_image' => true,
                'categories' => $this->resolver->buildRecursiveParentPopulate(5, ['slug']),
            ]
        ];

        $post_list = $this->resolver->getStrapiData('posts', $params);

        // Set total pages for pagination
        if(isset($post_list['meta']['pagination']['pageCount']) && $post_list['meta']['pagination']['pageCount'] > 0) {
            $this->resolver->setTotalPages($post_list['meta']['pagination']['pageCount']);
        }

        $articles = [];
        
        if (!empty($post_list['data'])) {
            foreach ($post_list['data'] as $postData) {
                $articles[] = CmsPostDTO::fromStrapiResult(['data' => $postData])->toArray();
            }
        }

        return $articles;
    }

    protected function setSecurityHeaders(?string $nonce = null): void
    {
        $nonce = $nonce ?? $this->nonce;

        $basePath = APPPATH . 'Views/cms/csp/';

        $directiveFiles = [
            'script-src'  => 'allowed_scripts.txt',
            'style-src'   => 'allowed_styles.txt',
            'img-src'     => 'allowed_images.txt',
            'font-src'    => 'allowed_fonts.txt',
            'connect-src' => 'allowed_connect.txt',
            'frame-src'   => 'allowed_frames.txt',
            'media-src'   => 'allowed_media.txt',
            'object-src'  => 'allowed_objects.txt',
        ];

        // Keywords that must be quoted
        $cspKeywords = [
            'self', 'none', 'unsafe-inline', 'unsafe-eval',
            'strict-dynamic', 'report-sample', 'unsafe-hashes', 'wasm-unsafe-eval',
        ];

        $quoteIfNeeded = function (string $value) use ($cspKeywords): string {
            $trimmed = trim($value);
            $lower = strtolower($trimmed);
            // Already quoted?
            if (str_starts_with($trimmed, "'") && str_ends_with($trimmed, "'")) {
                return $trimmed;
            }
            // Needs quoting?
            if (in_array($lower, $cspKeywords, true)) {
                return "'$trimmed'";
            }
            return $trimmed;
        };

        $cspDirectives = [];

        foreach ($directiveFiles as $directive => $filename) {
            $path = $basePath . $filename;

            if (!is_file($path)) {
                continue; // skip missing files
            }

            $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

            $sources = array_filter(array_map(function ($line) use ($quoteIfNeeded) {
                $line = trim($line);
                if ($line === '' || str_starts_with($line, '#')) {
                    return null;
                }
                return $quoteIfNeeded($line);
            }, $lines));

            // Add nonce to script-src
            if ($directive === 'script-src') {
                array_unshift($sources, "'nonce-{$nonce}'");
            }

            $cspDirectives[] = "$directive " . implode(' ', $sources) . ";";
        }

        // Add essential directives
        $cspDirectives[] = "default-src 'self';";
        //$cspDirectives[] = "object-src 'none';";
        $cspDirectives[] = "frame-ancestors 'none';";
        $cspDirectives[] = "base-uri 'self';";
        
        $csp = implode(' ', $cspDirectives);

        // Remove previous CSP if any
        header_remove('Content-Security-Policy');

        // Send headers
        header("Content-Security-Policy: $csp");

        // Secure transport
        if (!empty($_SERVER['HTTPS'])) {
            header('Strict-Transport-Security: max-age=63072000; includeSubDomains; preload');
        }

        // Security headers
        header('Cross-Origin-Opener-Policy: same-origin');
        //header('Cross-Origin-Embedder-Policy: require-corp');
        header('Cross-Origin-Embedder-Policy: unsafe-none'); // use require-corp all resources are controlled by us
        header('X-Frame-Options: SAMEORIGIN');
        header('X-Content-Type-Options: nosniff');
        header('X-XSS-Protection: 1; mode=block');

        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');

        // Permissions
        header('Permissions-Policy: geolocation=(self), microphone=(self "https://*.ai-pro.org"), camera=()');

        // Optional: public cache for articles
        header('Cache-Control: public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800');

        // Attribution for analytics compliance
        header('Attribution-Reporting-Support: {"os": true}');

        // Optional: restrict API access if needed
        // header('Access-Control-Allow-Origin: https://ai-pro.org');
        header_remove('Content-Security-Policy'); // removing this header to avoid conflicts with other headers
        header_remove('X-Powered-By');
    }


}