DEBUG - 2025-06-09 06:00:31 --> REQ ----------->
INFO - 2025-06-09 06:00:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:00:36 --> REQ ----------->
INFO - 2025-06-09 06:00:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:00:37 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:00:38 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:01:41 --> REQ ----------->
INFO - 2025-06-09 06:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:01:42 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:01:42 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:02:14 --> REQ ----------->
INFO - 2025-06-09 06:02:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:02:15 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:02:15 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:02:19 --> REQ ----------->
INFO - 2025-06-09 06:02:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:02:19 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:02:19 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:02:23 --> REQ ----------->
INFO - 2025-06-09 06:02:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:03:03 --> REQ ----------->
INFO - 2025-06-09 06:03:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:03:13 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:03:13 --> <EMAIL>
DEBUG - 2025-06-09 06:03:13 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:03:14 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('496cf59606e34625', '<EMAIL>', 'live', '$P$BnD6KWy1xrWyKJQflVA0sYl5wBRP7M0', '', '', '$2y$09$l1LlZm3axQhlko7B/ZWGUeEI22DZX3OBgHxQNKAy6c0miv1S.5NLG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana_wp\"}', '2025-06-09 06:03:14', '2025-06-09 06:03:14')
DEBUG - 2025-06-09 06:03:14 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('496cf59606e34625', '<EMAIL>', 'live', '$P$BnD6KWy1xrWyKJQflVA0sYl5wBRP7M0', '', '', '$2y$09$l1LlZm3axQhlko7B/ZWGUeEI22DZX3OBgHxQNKAy6c0miv1S.5NLG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana_wp\"}', '2025-06-09 06:03:14', '2025-06-09 06:03:14')
INFO - 2025-06-09 06:03:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:03:14 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '117'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-09 06:03:14 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '79e11d7b564354657e22a1f1ba5315d4', '2025-06-09 06:03:14', '2025-06-09 06:03:14')
DEBUG - 2025-06-09 06:03:14 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '79e11d7b564354657e22a1f1ba5315d4', '2025-06-09 06:03:14', '2025-06-09 06:03:14')
INFO - 2025-06-09 06:03:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:03:14 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:03:14 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '117'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-09 06:03:14 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(200): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-09 06:03:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:03:15 --> REQ ----------->12
DEBUG - 2025-06-09 06:05:13 --> REQ ----------->
INFO - 2025-06-09 06:05:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:05:23 --> REQ ----------->test_dev_xakozigohi@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:05:23 --> test_dev_xakozigohi@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:05:23 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:05:25 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('7c5be68a5603f5f6', '<EMAIL>', 'live', '$P$B39xpM8vRUM/awmh7PQLIW/a45tmBy0', '', '', '$2y$09$0KvJlW3MgFJvCqtyFVY2Ce2dMLvlxuaC/FucDnFAm4/CpHDW2RhvG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-09 06:05:25', '2025-06-09 06:05:25')
DEBUG - 2025-06-09 06:05:25 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('7c5be68a5603f5f6', '<EMAIL>', 'live', '$P$B39xpM8vRUM/awmh7PQLIW/a45tmBy0', '', '', '$2y$09$0KvJlW3MgFJvCqtyFVY2Ce2dMLvlxuaC/FucDnFAm4/CpHDW2RhvG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"arcana\"}', '2025-06-09 06:05:25', '2025-06-09 06:05:25')
INFO - 2025-06-09 06:05:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:05:25 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '118'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-09 06:05:25 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '3cacb1ad4cee204d995dcb0860dcb3c7', '2025-06-09 06:05:25', '2025-06-09 06:05:25')
DEBUG - 2025-06-09 06:05:25 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '3cacb1ad4cee204d995dcb0860dcb3c7', '2025-06-09 06:05:25', '2025-06-09 06:05:25')
INFO - 2025-06-09 06:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:05:26 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:05:26 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '118'
ORDER BY `start-account`.`end_date` DESC
WARNING - 2025-06-09 06:05:26 --> [DEPRECATED] explode(): Passing null to parameter #2 ($string) of type string is deprecated in APPPATH\Models\BTModel.php on line 52.
 1 APPPATH\Models\BTModel.php(52): explode(',', null)
 2 APPPATH\Helpers\btdb_helper.php(200): App\Models\BTModel->findBy([...], null)
 3 APPPATH\Controllers\FlowPages\Payment.php(64): btdbFindBy('PlanModel', 'plan_id', null)
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\FlowPages\Payment->index()
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\FlowPages\Payment))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-09 06:05:26 --> REQ ----------->$2y$09$0KvJlW3MgFJvCqtyFVY2Ce2dM
INFO - 2025-06-09 06:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-09 06:05:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('aiproStart', null, 1752041126, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(393): btflag_set('aiproStart', null, [...])
 3 APPPATH\Controllers\Api.php(787): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(140): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-09 06:05:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('user_email', null, 1752041126, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(394): btflag_set('user_email', null, [...])
 3 APPPATH\Controllers\Api.php(787): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(140): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-09 06:05:26 --> [DEPRECATED] setcookie(): Passing null to parameter #2 ($value) of type string is deprecated in APPPATH\Helpers\btflag_helper.php on line 101.
 1 APPPATH\Helpers\btflag_helper.php(101): setcookie('chatpro_KJLF4XgSL8wjlGm', null, 1752041126, '/', '.ai-pro.org')
 2 APPPATH\Controllers\BaseController.php(395): btflag_set('chatpro_KJLF4XgSL8wjlGm', null, [...])
 3 APPPATH\Controllers\Api.php(787): App\Controllers\BaseController->reInitCookies()
 4 APPPATH\Controllers\Api.php(140): App\Controllers\Api->checkAuth()
 5 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Api->v1('check-auth')
 6 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Api))
 7 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
INFO - 2025-06-09 06:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:05:27 --> REQ ----------->12
INFO - 2025-06-09 06:06:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:06:31 --> REQ ----------->
INFO - 2025-06-09 06:06:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:06:41 --> REQ ----------->test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:06:41 --> test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:06:41 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:06:45 --> REQ ----------->
INFO - 2025-06-09 06:06:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:08 --> REQ ----------->
INFO - 2025-06-09 06:07:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:10 --> REQ ----------->
INFO - 2025-06-09 06:07:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:11 --> REQ ----------->test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:11 --> test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:11 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:07:23 --> REQ ----------->
INFO - 2025-06-09 06:07:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:23 --> REQ ----------->test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:23 --> test_dev_raseboti@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:23 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:07:28 --> REQ ----------->
INFO - 2025-06-09 06:07:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:50 --> REQ ----------->
INFO - 2025-06-09 06:07:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:07:56 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 06:07:56 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:07:56 --> REQ ----------->test_dev_sidemywov@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:56 --> test_dev_sidemywov@mailinator.comPassword1Password1
DEBUG - 2025-06-09 06:07:56 --> btdbFindBy ---> 
DEBUG - 2025-06-09 06:08:02 --> REQ ----------->
INFO - 2025-06-09 06:08:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:09:07 --> REQ ----------->
INFO - 2025-06-09 06:09:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:18:50 --> REQ ----------->
INFO - 2025-06-09 06:18:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:21:59 --> REQ ----------->
INFO - 2025-06-09 06:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 06:50:48 --> REQ ----------->
INFO - 2025-06-09 06:50:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:21:45 --> REQ ----------->
INFO - 2025-06-09 07:21:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:21:49 --> REQ ----------->
INFO - 2025-06-09 07:21:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:25:42 --> REQ ----------->
INFO - 2025-06-09 07:25:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:25:43 --> REQ ----------->
INFO - 2025-06-09 07:25:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:25:45 --> REQ ----------->
INFO - 2025-06-09 07:25:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:25:55 --> REQ ----------->
INFO - 2025-06-09 07:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:49:58 --> REQ ----------->
INFO - 2025-06-09 07:49:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:50:01 --> REQ ----------->
INFO - 2025-06-09 07:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:50:08 --> REQ ----------->
INFO - 2025-06-09 07:50:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:50:10 --> REQ ----------->
DEBUG - 2025-06-09 07:50:10 --> btdbFindBy ---> 
DEBUG - 2025-06-09 07:50:13 --> REQ ----------->
INFO - 2025-06-09 07:50:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 07:59:49 --> REQ ----------->
INFO - 2025-06-09 07:59:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:00:06 --> REQ ----------->
INFO - 2025-06-09 08:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:00:27 --> REQ ----------->
INFO - 2025-06-09 08:00:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:06:12 --> REQ ----------->
INFO - 2025-06-09 08:06:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:07:10 --> REQ ----------->
INFO - 2025-06-09 08:07:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:07:11 --> REQ ----------->test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:11 --> test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:11 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:07:12 --> REQ ----------->test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:12 --> test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:12 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:07:38 --> REQ ----------->
INFO - 2025-06-09 08:07:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:07:38 --> REQ ----------->test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:38 --> test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:07:38 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:10:23 --> REQ ----------->
INFO - 2025-06-09 08:10:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:10:23 --> REQ ----------->test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:10:23 --> test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:10:23 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:10:43 --> REQ ----------->
INFO - 2025-06-09 08:10:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:10:46 --> REQ ----------->test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:10:46 --> test_dev_lelixeco@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:10:46 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:11:56 --> REQ ----------->
INFO - 2025-06-09 08:11:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:11:57 --> REQ ----------->
INFO - 2025-06-09 08:11:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:11:59 --> REQ ----------->user<script>@example.com
DEBUG - 2025-06-09 08:11:59 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:12:08 --> REQ ----------->user<script>@example.com
DEBUG - 2025-06-09 08:12:08 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:12:10 --> REQ ----------->user<script>@example.com
DEBUG - 2025-06-09 08:12:10 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:12:21 --> REQ ----------->
INFO - 2025-06-09 08:12:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:12:25 --> REQ ----------->
INFO - 2025-06-09 08:12:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:12:40 --> REQ ----------->
INFO - 2025-06-09 08:12:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:12:48 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 08:12:48 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:13:35 --> REQ ----------->
INFO - 2025-06-09 08:13:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:13:39 --> REQ ----------->
INFO - 2025-06-09 08:13:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:14:23 --> REQ ----------->
INFO - 2025-06-09 08:14:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:15:06 --> REQ ----------->
INFO - 2025-06-09 08:15:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:16:37 --> REQ ----------->
INFO - 2025-06-09 08:16:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:16:38 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 08:16:38 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:16:43 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 08:16:43 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:16:45 --> REQ -----------><EMAIL>
DEBUG - 2025-06-09 08:16:45 --> <EMAIL>
DEBUG - 2025-06-09 08:16:45 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:16:53 --> REQ ----------->
INFO - 2025-06-09 08:16:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:16:53 --> REQ ----------->test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:16:53 --> test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:16:53 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:17:04 --> REQ ----------->
INFO - 2025-06-09 08:17:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:17:04 --> REQ ----------->test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:17:04 --> test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:17:04 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:19:06 --> REQ ----------->
INFO - 2025-06-09 08:19:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:19:06 --> REQ ----------->test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:19:06 --> test_dev_huqo@mailinator.comPassword1Password1
DEBUG - 2025-06-09 08:19:06 --> btdbFindBy ---> 
DEBUG - 2025-06-09 08:34:11 --> REQ ----------->
INFO - 2025-06-09 08:34:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:38:23 --> REQ ----------->
INFO - 2025-06-09 08:38:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 08:42:23 --> REQ ----------->
INFO - 2025-06-09 08:42:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:02:21 --> REQ ----------->
INFO - 2025-06-09 09:02:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:02:22 --> REQ ----------->
INFO - 2025-06-09 09:02:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:12:16 --> REQ ----------->
INFO - 2025-06-09 09:12:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:14:17 --> REQ ----------->
INFO - 2025-06-09 09:14:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:16:38 --> REQ ----------->
INFO - 2025-06-09 09:16:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:16:39 --> REQ ----------->
INFO - 2025-06-09 09:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:16:44 --> REQ ----------->
INFO - 2025-06-09 09:16:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:39:36 --> REQ ----------->
INFO - 2025-06-09 09:39:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:39:47 --> REQ ----------->
INFO - 2025-06-09 09:39:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:41:44 --> REQ ----------->
INFO - 2025-06-09 09:41:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:43:23 --> REQ ----------->
INFO - 2025-06-09 09:43:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:43:24 --> REQ ----------->
INFO - 2025-06-09 09:43:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:44:00 --> REQ ----------->
INFO - 2025-06-09 09:44:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:51:42 --> REQ ----------->
INFO - 2025-06-09 09:51:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:52:06 --> REQ ----------->
INFO - 2025-06-09 09:52:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:52:07 --> REQ ----------->
INFO - 2025-06-09 09:52:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:52:09 --> REQ ----------->
INFO - 2025-06-09 09:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:53:06 --> REQ ----------->
INFO - 2025-06-09 09:53:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:56:50 --> REQ ----------->
INFO - 2025-06-09 09:56:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:56:58 --> REQ ----------->
INFO - 2025-06-09 09:56:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 09:57:02 --> REQ ----------->
INFO - 2025-06-09 09:57:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 10:24:24 --> REQ ----------->
INFO - 2025-06-09 10:24:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 10:25:38 --> REQ ----------->
INFO - 2025-06-09 10:25:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
