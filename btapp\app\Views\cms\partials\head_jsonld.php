<script nonce="<?= $nonce ?>" type="application/ld+json">
<?= json_encode(array_filter([
    "@context" => "https://schema.org",
    "@type" => $type ?? "WebPage",
    "headline" => $title ?? '',
    "description" => $description ?? '',
    "articleBody" => $full_text_content ?? '',
    "wordCount" => $full_text_content ? str_word_count($full_text_content) : null,
    "image" => array_filter([
        "@type" => "ImageObject",
        "url" => $og_image_url ?: $image ?? null,
        "width" => isset($og_image_width) ? (int)$og_image_width : null,
        "height" => isset($og_image_height) ? (int)$og_image_height : null,
    ]),
    "author" => [
        "@type" => "Organization",
        "name" => "AI-PRO Team",
        "url" => "https://ai-pro.org/about-us",
    ],
    "publisher" => [
        "@type" => "Organization",
        "name" => $site_name ?? 'AI-Pro.org',
        "logo" => [
            "@type" => "ImageObject",
            "url" => "https://assets.ai-pro.org/assets/wp-content/uploads/2024/06/AIPRO-200x45-1.svg",
            "width" => 200,
            "height" => 45
        ]
    ],
    "datePublished" => $date_create_gmt ?? null,
    "dateModified" => $date_update_gmt ?? null,
    "mainEntityOfPage" => [
        "@type" => "WebPage",
        "@id" => $canonical_url ?? ''
    ],
    "inLanguage" => "en-US",
    "keywords" => !empty($category_name) ? [$category_name] : null,
    "articleSection" => $category_name ?? null,
    "url" => $canonical_url ?? ''
], fn($v) => $v !== null && $v !== '' && $v !== []), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT); ?>
</script>
