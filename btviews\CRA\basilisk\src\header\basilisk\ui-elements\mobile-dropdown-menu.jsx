import { FaChevronDown } from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
import React from "react";

function MobileDropdownMenu({ title, items, isActive, toggleMenu }) {
  return (
    <div className="border-b pb-2">
      <button
        className="flex items-center justify-between w-full text-gray-700 font-medium"
        onClick={toggleMenu}
        aria-expanded={isActive}
        aria-controls={`mobile-${title.toLowerCase().replace(" ", "-")}-menu`}
      >
        {title}
        <FaChevronDown className={`h-4 w-4 transition-transform ${isActive ? "rotate-180" : ""}`} aria-hidden="true" />
      </button>
      <AnimatePresence>
        {isActive && (
          <motion.div
            className="mt-2 ml-4 space-y-2"
            id={`mobile-${title.toLowerCase().replace(" ", "-")}-menu`}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {items.map((item) => (
              <a
                key={item.name}
                href={item.url}
                className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.name}
              </a>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default MobileDropdownMenu;