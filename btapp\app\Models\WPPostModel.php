<?php

namespace App\Models;

use CodeIgniter\Model;

class WPPostModel extends BTModel
{
    protected $table = 'posts';
    protected $primaryKey = 'id';

    protected $allowedFields = [];

    protected $returnType = 'App\Entities\WPPost';

    protected $skipValidation = false;

    protected $validationRules = [];

    protected $validationMessages = [];

    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function getPreviousPost($id)
    {
        $query = $this->db->table($this->table)
            ->select('posts.*, yoast_indexable.open_graph_image_meta as og_image_meta')
            ->join('yoast_indexable', 'yoast_indexable.object_id = posts.id', 'left')
            ->where('posts.id <', $id)
            ->where('posts.post_status', 'publish')
            ->where('posts.post_type', 'post')
            ->orderBy('id', 'DESC')
            ->limit(1)
            ->get();

        $results = $query->getResult();

        $results = $this->decodeOgImageMeta($results);

        return $results;
    }

    public function getNextPost($id)
    {
        $query = $this->db->table($this->table)
            ->select('posts.*, yoast_indexable.open_graph_image_meta as og_image_meta')
            ->join('yoast_indexable', 'yoast_indexable.object_id = posts.id', 'left')
            ->where('posts.id >', $id)
            ->where('posts.post_status', 'publish')
            ->where('posts.post_type', 'post')
            ->limit(1)
            ->get();


        $results = $query->getResult();

        $results = $this->decodeOgImageMeta($results);

        return $results;
    }

    private function decodeOgImageMeta($results)
    {
        if (isset($results[0]->og_image_meta) && !empty($results[0]->og_image_meta)) {
            $results[0]->og_image_meta = json_decode($results[0]->og_image_meta);
        } else {
            $results[0]->og_image_meta = (object) [
                'url' => '',
                'width' => '',
                'height' => '',
                'type' => '',
                'alt' => ''
            ];
        }
        return $results;
    }
}
