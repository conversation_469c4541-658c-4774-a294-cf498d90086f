<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\WPPostModel;

class Articles extends BaseController
{

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct() {}

    public function index($slug1)
    {
        return view('articles/' . $slug1 . '.php');
    }

    public function page($slug1, $slug2)
    {

        $pageSlug = 'https://ai-pro.org/learn-ai/' . $slug1 . '/' . $slug2 . '/';
        $posts = btdbFindBy("WPPostModel", ["post_name", "post_status"], [$slug2, "publish"]);
        $yoastIndex = btdbFindBy("WPYoastIndexModel", ["permalink", "object_sub_type"], [$pageSlug, "post"]);

        if (($yoastIndex['success'] && $yoastIndex['res'])
            && ($posts['success'] && $posts['res'])
        ) {
            $yoast = $yoastIndex['res'][0];
            $post = $posts['res'][0];
            $date = date_create($post->post_date_gmt);
            $wpPostModel = new WPPostModel();
            $prevPost = $wpPostModel->getPreviousPost($post->ID);
            $nextPost = $wpPostModel->getNextPost($post->ID);

            $open_graph_image_meta  = json_decode($yoast->open_graph_image_meta);

            $og_image_url = isset($open_graph_image_meta->url) ? $open_graph_image_meta->url : '';

            $og_image_url = transformAiProUrl($og_image_url);

            $data = [
                'pid' => $post->ID,
                'title' => $post->post_title,
                'description' => $yoast->description,
                'breadcrumb' => $yoast->breadcrumb_title,
                'date_modified' => $post->post_date_gmt,
                'url' => $pageSlug,
                'date_modified_formatted' => date_format($date, "F d Y"),
                'content' => $post->post_content,
                'prev_post' => $prevPost,
                'next_post' => $nextPost,
                'post_date_gmt' => date_format(date_create($post->post_date_gmt), "c"),
                'post_modified_gmt' => date_format(date_create($post->post_modified_gmt), "c"),
                'og_image_type' => $open_graph_image_meta->type ?? '',
                'og_image_width' => $open_graph_image_meta->width ?? '',
                'og_image_height' => $open_graph_image_meta->height ?? '',
                'og_image_url' => $og_image_url,
                'slug1' => $slug1,
            ];
            return view('articles/index', $data);
        }
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound($slug2);
        exit;
    }
}
