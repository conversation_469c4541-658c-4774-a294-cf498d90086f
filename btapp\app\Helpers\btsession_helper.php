<?php

use Config\Services as CI4Services;
use App\Models\AccountModel;

/**------------------------------------------------------------------------------
 * BTSession
 -------------------------------------------------------------------------------*/

/**
 * 	Initialize
 */
function btsessionInit() {
	return CI4Services::session();
}

/**
 * 	Set per Module
 */
function btsessionSet($_module, $_data) {
	$session = btsessionInit();

	if (btsessionHas($_module)) {
		return $session->push($_module, $_data);
	}

	$session->set($_module, $_data);
}

/**
 * 	Get per Module
 */
function btsessionGet($_module) {
	if (is_cli()) {
		return;
	}

	$session = btsessionInit();

	if (!btsessionHas($_module)) {
		return 0;
	}

	return (object) $session->get($_module);
}

/**
 * 	Remove per Module
 */
function btsessionClean($_module) {
	$session = btsessionInit();
	$session->remove($_module);
}

/**
 * 	Destroy/Remove All
 */
function btsessionDestroy() {
	$session = btsessionInit();
	$session->destroy();
}

/**
 * 	Has per Module
 */
function btsessionHas($_module) {
	$session = btsessionInit();
	return $session->has($_module);
}

/**------------------------------------------------------------------------------
 * Session: Logged-In Access
-------------------------------------------------------------------------------*/

function btsessionIsUserLoggedIn() {
	if (!btsessionHas('USER')) {
		return 0;
	}

	$session = btsessionInit();
	if (is_array($session)) {
		$session = (object) $session;
	}

	if (is_array($session->USER)) {
		$session->USER = (object) $session->USER;
	}

	if ( btflag('aiwp_logged_in',null) ) {
		btflag_set('aiwp_logged_in', btflag('aiwp_logged_in',null), array('domain'=>'ai-pro.org'));
	}
	return isset($session->USER->user_id);
}

function btsessionSetAccount($_user = [], $_reset = 0) {
	if (is_cli()) {
		return;
	}

	$user_log_data = [
		'user_data' => [],
		'account_data' => [],
		'acctuser_data' => [],
		'plan_data' => [],
	];

	if (!btsessionHas('USER') || $_reset) {
		btsessionClean('USER');
		btsessionSet('USER', $_user);
		$user_log_data['user_data'] = $_user;
	}

	btSessionSetUserSubscription($_user, $_reset);
}

function btSessionSetUserSubscription($_user = [], $_reset = 0) {
	if (!btsessionHas('ACCOUNT') || $_reset) {
		btsessionClean('ACCOUNT');
		if (!$_user) {
			return;
		}

		$accountModel = new AccountModel();
		$account = $accountModel->getActiveSubscription($_user->user_id);
		if (isset($account['res'][0])) {
			btsessionSet('ACCOUNT', $account['res'][0]);
		}
	}

	if (!btsessionHas('PLAN') || $_reset) {
		btsessionClean('PLAN');
		if (!btsessionGet('ACCOUNT')) {
			return;
		}

		$plan = btdbFindBy('PlanModel', 'plan_id', btsessionGet('ACCOUNT')->plan_id);
		if (isset($plan['res'][0])) {
			btsessionSet('PLAN', $plan['res'][0]);
		}
	}
}

function btsessionGetUser() {
	if (!btsessionIsUserLoggedIn()) {
		return 0;
	}

	if (!btsessionHas('USER')) {
		return 0;
	}

	$ob = session('USER');

	return session('USER');
}