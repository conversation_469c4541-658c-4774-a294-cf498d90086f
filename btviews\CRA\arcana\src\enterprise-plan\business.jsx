import codeBlockImg from './images/code-block.png';
import screensImg from './images/screens.png';
import securityImg from './images/security.png';
import { getCurrentDomain, getCurrentProtocol } from './functions';

export default function Business() {
    const handleGetAIPro = () => {
        const curDom = getCurrentDomain();
        const pricingDom = getCurrentProtocol() + '//' + (curDom.includes('staging') && !curDom.includes('localhost') ? 'staging.' : '') + 'start.ai-pro.org';

        window.location.href = `${pricingDom}/pricing/?ppg=71&kt8typtb=arcana`;
    };

    return (
        <section className="bg-[#F3F8FF] py-12 md:py-16 lg:py-20">
            <div className="ep_container px-4 md:px-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <h2 className="font-bold max-w-640:text-[32px] md:text-[36px] max-w-380:text-[31.5px] max-w-360:text-[29px]">
                        <span className="text-[#7B2FF7]">AI-PRO</span> for Businesses & Organizations
                    </h2>
                    <p className="max-w-[900px] text-gray-500 text-[16px]">
                        Automate operations, generate content, and make smarter decisions with data-driven insights from AI productivity tools. Whether you’re a remote team, a growing startup, or an enterprise, AI-PRO enhances performance with integrated AI tools tailored to your needs.
                    </p>
                </div>
                <div className="flex justify-center">
                    <div className="flex flex-row max-w-640:flex-col justify-center max-w-[1155px] gap-8 mx-auto py-16 max-w-640:py-[38px] max-w-640:px-[8px]">
                        <div className="flex flex-1 flex-col h-[302px] max-w-640:min-h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px]">
                            <div className="flex items-center justify-center rounded-full">
                                <img src={codeBlockImg} alt="AI Tools Access" />
                            </div>
                            <div>
                                <div className="min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] font-bold">20+ AI Tools</h3></div>
                                <p className="text-sm text-gray-500">
                                    Empower your team with top-tier AI productivity tools for business—from writing and analytics to marketing, design, and automation.
                                </p>
                            </div>
                        </div>
                        <div className="flex flex-1 flex-col h-[302px] max-w-640:min-h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px]">
                            <div className="flex items-center justify-center rounded-full">
                                <img src={screensImg} alt="Teamwork and collaboration" />
                            </div>
                            <div>
                                <div className="min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] font-bold">Seamless Collaboration</h3></div>
                                <p className="text-sm text-gray-500">
                                    Manage users and streamline teamwork with collaborative features built for modern workflows.
                                </p>
                            </div>
                        </div>
                        <div className="flex flex-1 flex-col h-[302px] max-w-640:min-h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px]">
                            <div className="flex items-center justify-center rounded-full">
                                <img src={securityImg} alt="Enterprise security" />
                            </div>
                            <div>
                                <div className="min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] font-bold">Enterprise-Level Security</h3></div>
                                <p className="text-sm text-gray-500">
                                    Trust in enterprise-grade data protection and compliance across all your AI tools for business.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex justify-center pt-[20px]">
                    <button className="gradient-button-purp px-6 py-3 rounded-md text-white text-[20px] font-semibold h-[53px]" onClick={handleGetAIPro}>
                        Get AI-PRO for Business
                    </button>
                </div>
            </div>
        </section>
    );
}