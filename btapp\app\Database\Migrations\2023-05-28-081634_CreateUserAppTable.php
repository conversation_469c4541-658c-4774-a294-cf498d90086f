<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateUserAppTable extends Migration
{
    private $table = "user_app";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'userapp_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'email' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'userapp_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
                'comment' => 'this is used by CHATGPT_CLONE'
            ],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('userapp_id', true);
		$this->forge->addKey(['email'], false, false, 'start_userapp_email_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
