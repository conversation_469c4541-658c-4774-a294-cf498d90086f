<?php

namespace App\Controllers\FlowPages;

use App\Controllers\BaseController;
use App\Models\AccountModel;

class Pricing extends BaseController
{
    private $theme = FLAG_THEME_DEFAULT;
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename
    private $data = []; // used to pass data on react

    private $pageSlug = '';
    private $date_now;

    private $redirectApps = ['chat' => 'https://chat.ai-pro.org/chat/new']; 
    
    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        $this->date_now = date('Y-m-d H:i:s', time());
        // parent::__construct();
    }

    public function index()
    {
        $this->init();

        $flow = btflag('flow', '');
        $ppg = btflag('ppg');

        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        if ($ppg == '97') {
            $this->theme = 'basilisk';

            $flow = '01';
        }
        
        if ($this->pageSlug === 'plans-and-pricing-d') {
            $this->theme = 'basilisk';

            btflag_set('flow', '04');

            $flow = '04';
        } else if ($this->pageSlug === 'subscription') {
            $this->theme = 'arcana_wp';

            btflag_set('kt8typtb', 'arcana_wp');
        } else if ($this->pageSlug === 'subscription-e') {
            $this->theme = 'arcana';
            $flow = '06';

            btflag_set('kt8typtb', 'arcana');
            btflag_set('flow', '06');
        } else {
            $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        }

        if(isset($_GET['ppg']) && $_GET['ppg']){
            $this->unsetAppCookies();
        }

        if ($flow === 'chatapp' && !btsessionHAS("PLAN") && btflag('n_flow') !== 'chatapp') {
            $redirectUrl = getenv('CHATBOTPRO_URL') ?: 'https://chatpro.ai-pro.org';
            $redirectUrl .= '/chat/new';
            header('Location: ' . $redirectUrl);
            die;
        }

        if ($flow === 'chatapp' && btflag('n_flow') === 'chatapp' && !btflag('user_plan')) {
            $redirectUrl = getenv('WORDPRESS_BASE_URL') ?: 'https://ai-pro.org';
            $redirectUrl .= '/pricing-redirect?from=chatapp';
            header('Location: ' . $redirectUrl);
            die;
        }

        if (array_key_exists(btflag('rdct'), $this->redirectApps) && !btsessionHAS("PLAN")) {
            header('Location: ' . $this->redirectApps[btflag('rdct')]);
            die;
        }

        if (array_key_exists(btflag('rdct'), $this->redirectApps) && !btflag('user_plan')) {
            $redirectUrl = getenv('WORDPRESS_BASE_URL') ?: 'https://ai-pro.org';
            $redirectUrl .= '/pricing-redirect';
            header('Location: ' . $redirectUrl);
            die;
        }
        
        if (btsessionGet('USER')){
            $ent_parent_user_id = isset(btsessionGet('USER')->ent_parent_user_id) ? btsessionGet('USER')->ent_parent_user_id : '';
            $plan = $this->getUserPlan();

            if ($ent_parent_user_id!='' || strtolower($plan)=='enterprise'){
                header("Location: " . base_url("manage-account"));
                die;
            }

            $userStatus = isset(btsessionGET("ACCOUNT")->status) ? strtolower(btsessionGET("ACCOUNT")->status) : '';
            if ($userStatus=='paused'){
                $isExpired =  isset(btsessionGet('ACCOUNT')->expired) ? btsessionGet('ACCOUNT')->expired : '';
                if ($isExpired=='yes'){
                    header("Location: " . base_url("resume"));
                    die;
                }
            }
        }

        $this->switch_theme();
    }

    public function upgrade(){

        if (btflag('flow') === 'chatapp' && btflag('n_flow') === 'chatapp' && !btflag('user_plan')) {
            $redirectUrl = getenv('WORDPRESS_BASE_URL') ?: 'https://ai-pro.org';
            $redirectUrl .= '/pricing-redirect';
            header('Location: ' . $redirectUrl);
            die;
        }

        if (array_key_exists(btflag('rdct'), $this->redirectApps) && !btflag('user_plan')) {
          $redirectUrl = getenv('WORDPRESS_BASE_URL') ?: 'https://ai-pro.org';
          $redirectUrl .= '/pricing-redirect';
          header('Location: ' . $redirectUrl);
          die;
        }

        $this->init();
        $this->authUserSubscription();

        $ppg = isset($_GET['ppg']) ? $_GET['ppg'] : '';
        $utpm = isset($_GET['utpm']) ? $_GET['utpm'] : '';
        $currency = btsessionHas('PLAN') ? strtolower(btsessionGet('PLAN')->currency) : '';

        $ppg_upgrade_promo = getenv('UPGRADE_PPG_PROMO');  
        if ($ppg_upgrade_promo && $ppg_upgrade_promo != '' && $currency=='usd' && $ppg==''){
            $arrPPGSettings = explode("|",$ppg_upgrade_promo);
            $ppg_promo = isset($arrPPGSettings[0]) ? $arrPPGSettings[0] : '';
            $ppg_validity = isset($arrPPGSettings[1]) ? $arrPPGSettings[1].' 23:59:59' : '';

            if ($ppg_promo!='' && $ppg_validity!='' && btsessionGet("PLAN")->ppg != '97'){
                if ($ppg_validity > $this->date_now){
                    die(header("Location: /upgrade/?ppg=".$ppg_promo . "&utpm=".$utpm ));
                }
            }
        }

        $plan = btsessionGET("PLAN");
        $user_plan = strtolower($plan->plan_type);
        $payment_interval = strtolower($plan->payment_interval);

        if ($user_plan === "pro") {
            if ($payment_interval === "yearly") {
                $this->data['user_plan'] = 'PRO_ANNUAL';
            } else {
                $this->data['user_plan'] = 'PRO';
            }
        } else if ($user_plan === 'advanced') {
            $this->data['user_plan'] = 'ADVANCED';
        } else if ($user_plan === "promax"){
            if ($payment_interval === "yearly") {
                if ($plan->currency != 'USD'){
                    header("Location: " . base_url("manage-account"));
                    die;
                } else {
                    $this->data['user_plan'] = 'PROMAX_ANNUAL';
                }
            } else {
                $this->data['user_plan'] = 'PROMAX';
            }
        } else if(strtolower(btsessionGET("PLAN")->plan_type) === "enterprise" ){
            header("Location: " . base_url("manage-account"));
            die;
        } else {
            if ($payment_interval === "yearly") {
                $this->data['user_plan'] = 'BASIC_ANNUAL';
            } else {
                $this->data['user_plan'] = 'BASIC';
            }
        }

        if (strtolower(btsessionGET("ACCOUNT")->status)=='paused'){
            $isExpired =  isset(btsessionGet('ACCOUNT')->expired) ? btsessionGet('ACCOUNT')->expired : '';
            if ($isExpired=='yes'){
                header("Location: " . base_url("resume"));
                die;
            }
        }

        setcookie('upg', '1', time() + (86400 * 30), '/');
        setcookie('upg', '1', time() + (86400 * 30), '/','.ai-pro.org');

        $this->data['user_plan_currency'] = btsessionGET("PLAN")->currency;
        $this->themePageData['page_title'] = 'AI-Pro | Upgrade';
        $this->theme = 'arcana';

        $this->switch_theme();
    }

    public function upgradeEnt($members){

        if (!is_numeric($members)){
            header("Location: " . base_url("404"));
            exit;    
        }

        setcookie('upg', '1', time() + (86400 * 30), '/');
        setcookie('upg', '1', time() + (86400 * 30), '/','.ai-pro.org');

        $this->init();
        $this->authUserSubscription();
        $this->data['plan'] = '16';
        $this->data['members'] = $members;
        $this->themePageData['page_title'] = 'AI-Pro | Upgrade';
        $this->theme = 'arcana';
        $this->switch_theme();
    }

    public function downgrade(){
        $this->init();
        $this->authUserSubscription();

        $plan = btsessionGET("PLAN");
        $user_plan = strtolower($plan->plan_type);
        $payment_interval = strtolower($plan->payment_interval);

        if ($user_plan === "basic") {
            if ($payment_interval === "yearly") {
                $this->data['user_plan'] = 'BASIC_ANNUAL';
            } else {
                header("Location: " . base_url("manage-account"));
                die;
            }
        } else if ($user_plan === 'advanced') {
            $this->data['user_plan'] = 'ADVANCED';
        } else if ($user_plan === "promax"){
            if ($payment_interval === "yearly") {
                $this->data['user_plan'] = 'PROMAX_ANNUAL';
            } else {
                $this->data['user_plan'] = 'PROMAX';
            }
        } else {
            if ($payment_interval === "yearly") {
                $this->data['user_plan'] = 'PRO_ANNUAL';
            } else {
                $this->data['user_plan'] = 'PRO';
            }
        }

        setcookie('upg', '1', time() + (86400 * 30), '/');
        setcookie('upg', '1', time() + (86400 * 30), '/','.ai-pro.org');

        $this->data['user_plan_currency'] = btsessionGET("PLAN")->currency;
        $this->themePageData['page_title'] = 'AI-Pro | Downgrade';
        $this->theme = 'arcana';

        $this->switch_theme();
    }

    public function authUserSubscription(){
        if(!btsessionIsUserLoggedIn()) { header("Location: ".base_url()."register"); die; }
        if(!btsessionHAS("PLAN")) { 
            $flow = btflag('flow');
            if ($flow === '06') {
                header("Location: ".base_url()."subscription-e");
            }
            else if ($flow === '04' || $flow === '01') {
                header("Location: ".base_url()."subscription-plan");
            } else {
                header("Location: ".base_url()."pricing"); 
            }
            die; 
        }
        if(strtolower(btsessionGET("PLAN")->currency) !== 'usd'
        && !(strtolower(btsessionGET("ACCOUNT")->merchant) === 'stripe' || strtolower(btsessionGET("ACCOUNT")->merchant) === 'paddle' || strtolower(btsessionGET("ACCOUNT")->merchant) === 'paypal' || strtolower(btsessionGET("ACCOUNT")->merchant) === 'fastspring')
        ) {
            header("Location: ".base_url()."my-account"); die;
        }
    }

    private function getUserPlan() {
        if(btsessionHas('PLAN')) {
            return strtolower(btsessionGet('PLAN')->plan_type);
        }
        return "";
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function init(){
        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data();
        $this->theme_pageVersion();
    }

    private function switch_theme(){
        switch ($this->theme) {
            case 'basilisk-03':
            case 'basilisk-02':
            case 'basilisk':
                $this->theme_basilisk();
                break;
            case 'druig':
                $this->theme_druig();
                break;
            case 'echo':
                $this->theme_echo();
                break;
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    private function theme_arcana(){
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->data),
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_basilisk(){
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_basilisk/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_druig(){
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_druig/index_{$this->themePageVersion}.twig", $viewData);
    }
    private function theme_echo(){
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_echo/index_{$this->themePageVersion}.twig", $viewData);
    }
    private function theme_data(){
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro | Pricing',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
                'time' => time(),
                'f_sle' => btflag('f_sle', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => false,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }
    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}
