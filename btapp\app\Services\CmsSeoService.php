<?php

namespace App\Services;

use App\Libraries\CacheService;
use CodeIgniter\HTTP\ResponseInterface;
use App\DTOs\CmsPostDTO;
use App\DTOs\CmsCollectionPageDTO;

class CmsSeoService
{
    /**
     * @var CacheService
     */
    protected $cacheService;
    
    /**
     * @var StrapiResolverService
     */
    protected $strapiResolver;
    
    /**
     * Cache duration in seconds
     */
    protected const CACHE_DURATION = 21600; // 6 hours
    protected const CACHE_DURATION_SHORT = 60; // 1 minute

    protected $data = [];
       
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->cacheService = new CacheService(useAemo: true);
        $this->strapiResolver = new StrapiResolverService();

        // Initialize data array
        $this->data['data'] = [];
    }
    
    /**
     * Returns main sitemap XML with proper content type
     * 
     * @return string | null
     */
    public function getSitemapXml() : ?string
    {
        // this is for sitemap-main.xml
        $dt = $this->strapiResolver->getStrapiData('categories', array_merge([
                                                                        'fields' => 'updatedAt',
                                                                        'sort' => 'updatedAt:desc',
                                                                        'pagination[limit]' => 1
                                                                        ]));
        $data['data'] = $dt['data'][0];
        $dto = CmsCollectionPageDTO::fromStrapiResult($data)->toView()->toArray();

        $this->data['lastmod_categories'] = $dto['updatedAt'] ?? date('c');
        // this is for sitemap-articles.xml
        $dt = $this->strapiResolver->getStrapiData('posts', array_merge([
                                                                        'fields' => 'updatedAt',
                                                                        'sort' => 'updatedAt:desc',
                                                                        'pagination[limit]' => 1
                                                                        ]));
        $data['data'] = $dt['data'][0];
        $dto = CmsPostDTO::fromStrapiResult($data)->toView()->toArray();
        $this->data['lastmod_articles'] = $dto['updatedAt'] ?? date('c');
        // this is for sitemap-pages.xml
        $dt = $this->strapiResolver->getStrapiData('pages', array_merge([
                                                                        'fields' => 'updatedAt',
                                                                        'sort' => 'updatedAt:desc',
                                                                        'pagination[limit]' => 1
                                                                        ]));
        $data['data']['data'] = $dt['data'][0];
        $dto = CmsPostDTO::fromStrapiResult($data)->toView()->toArray();
        $this->data['lastmod_pages'] = $dto['updatedAt'] ?? date('c');
        return $this->renderXmlResponse('cms/sitemaps/sitemap', 'cms_sitemap_xml');
    }
    
    /**
     * Returns sitemap articles XML with proper content type
     * 
     * @return string | null
     */
    public function getSitemapArticlesXml() : ?string
    {
        $this->cacheService->set('cms_title_before_sitemap', 'Articles', null, self::CACHE_DURATION_SHORT);
        $this->cacheService->set('cms_title_after_sitemap', '', null, self::CACHE_DURATION_SHORT);

        // Store all articles
        $allArticles = [];
    
        // Set batch size
        $limit = 20; // Fetch 100 articles at a time
        $page = 1;
        $hasMoreArticles = true;

        // Fetch articles in batches
        while ($hasMoreArticles) {
            $articles = $this->strapiResolver->getStrapiData('posts', [
                'fields' => ['slug', 'createdAt', 'updatedAt'], // Include needed fields
                'sort' => 'createdAt:desc',
                'pagination[page]' => $page,
                'pagination[pageSize]' => $limit,
                'filters' => [
                    'frontend_status' => 'published', // Only fetch published articles
                ],
                'populate' => [
                    'categories' => [
                        'fields' => ['name', 'slug'],
                        'populate' => [
                            'parent' => [
                                'fields' => ['name', 'slug']
                            ],
                        ],
                    ],
                    'seo' => [
                        'fields' => ['metaTitle', 'metaDescription', 'metaRobots'],
                    ]
                ],
            ]);

            // Check if we got results
            if (empty($articles['data']) || count($articles['data']) === 0) {
                $hasMoreArticles = false;
            } else {
                // Add this batch to our collection
                $allArticles = array_merge($allArticles, $articles['data']);

                // Check if we've reached the last page
                if (count($articles['data']) < $limit || 
                    $articles['meta']['pagination']['page'] >= $articles['meta']['pagination']['pageCount']) {
                    $hasMoreArticles = false;
                }

                // Move to next page
                $page++;
            }
        }

        if (!empty($allArticles)) {
            foreach ($allArticles as $articleData) {
                $this->data['data'][] = CmsPostDTO::fromStrapiResult(['data' => $articleData])->toArray();
            }
        }

        return $this->renderXmlResponse('cms/sitemaps/sitemap-articles', 'cms_sitemap_articles_xml');
    }
    
    /**
     * Returns sitemap pages XML with proper content type
     * 
     * @return string | null
     */
    public function getSitemapPagesXml(): ?string
    {
        $this->cacheService->set('cms_title_before_sitemap', 'Pages', null, self::CACHE_DURATION_SHORT);
        $this->cacheService->set('cms_title_after_sitemap', '', null, self::CACHE_DURATION_SHORT);

        // Store all articles
        $allArticles = [];
    
        // Set batch size
        $limit = 20; // Fetch 100 articles at a time
        $page = 1;
        $hasMoreArticles = true;
        $depth = 5; // Maximum depth for parent pages
        $parentSlugs = ['slug']; // To track parent slugs

        // Fetch articles in batches
        while ($hasMoreArticles) {
            $articles = $this->strapiResolver->getStrapiData('pages', [
                'fields' => ['slug', 'content', 'createdAt', 'updatedAt'], // Include needed fields
                'sort' => 'createdAt:desc',
                'pagination[page]' => $page,
                'pagination[pageSize]' => $limit,
                'filters' => [
                    'frontend_status' => 'published', // Only fetch published pages
                    'slug' => [
                        '$ne' => 'home',
                    ],
                ],
                'populate' => [
                    'parent' => $this->strapiResolver->buildRecursiveParentPopulate($depth, $parentSlugs),
                ],
            ]);

            // Check if we got results
            if (empty($articles['data']) || count($articles['data']) === 0) {
                $hasMoreArticles = false;
            } else {
                // Add this batch to our collection
                $allArticles = array_merge($allArticles, $articles['data']);

                // Check if we've reached the last page
                if (count($articles['data']) < $limit || 
                    $articles['meta']['pagination']['page'] >= $articles['meta']['pagination']['pageCount']) {
                    $hasMoreArticles = false;
                }

                // Move to next page
                $page++;
            }
        }

        if (!empty($allArticles)) {
            foreach ($allArticles as $articleData) {
                $data = CmsPostDTO::fromStrapiResult(['data' => $articleData])->toArray();
                $word_count = str_word_count($data['full_text_content'] ?? '');
                if($word_count < 20) {
                    // Skip pages with very short content
                    continue; // Skip empty content
                }
                $this->data['data'][] = $data;
            }
        }

        return $this->renderXmlResponse('cms/sitemaps/sitemap-pages', 'cms_sitemap_pages_xml');
    }

    public function getSitemapMainXml(): ?string
    {
        $this->cacheService->set('cms_title_before_sitemap', 'Main', null, self::CACHE_DURATION_SHORT);
        $this->cacheService->set('cms_title_after_sitemap', 'Index', null, self::CACHE_DURATION_SHORT);

        // this is for last modified of home page
        $dt = $this->strapiResolver->getStrapiData('pages', array_merge([
                                                                        'fields' => ['updatedAt'],
                                                                        'filters' => [
                                                                            'slug' => 'home'
                                                                        ],
                                                                        'pagination[limit]' => 1
                                                                        ]));
        $data['data'] = $dt['data'][0];
        $dto = CmsPostDTO::fromStrapiResult($data)->toView()->toArray();
        $this->data['lastmod_pages'] = $dto['updatedAt'] ?? date('c');

        // get collection pages
        // determine first if taxonomy(category/tags) has any pages, this will be the collection pages
        foreach ( ['categories','tags'] as $taxonomy ) {

            $dt = $this->strapiResolver->getStrapiData($taxonomy, array_merge([
                            'fields' => ['slug', 'createdAt', 'updatedAt'],
                            'sort' => 'updatedAt:desc',
                            'populate' => [
                                'posts' => [
                                    'fields' => ['id'],
                                    'filters' => [
                                        'frontend_status' => 'published',
                                    ],
                                ],
                            ],
                        ]));

            foreach ($dt['data'] as $taxonomyData) {
                //print_r($taxonomyData) . "\r\n\r\n";
                //print_r(count($taxonomyData['posts']));
                //echo "\r\n\r\n\r\n\r\n\r\n";
                if (count($taxonomyData['posts']) > 0) {
                    //print_r($taxonomyData['slug']); 
                    //echo " = " . count($taxonomyData['posts']);
                    //echo "\r\n\r\n";

                    // get the parent(s) of the category
                    if($taxonomy === 'categories') {
                        $collection = $this->strapiResolver->getStrapiData($taxonomy, [
                            'fields' => ['slug', 'createdAt', 'updatedAt'],
                            'filters' => [
                                'slug' => $taxonomyData['slug'],
                            ],
                            'populate' => [
                                'parent' => $this->strapiResolver->buildRecursiveParentPopulate(5, ['slug']),
                            ],
                        ]);
                        //$dto = CmsPostDTO::fromStrapiResult(['data' => $collection['data'][0]])->toArray();
                        $this->data['data'][] = CmsPostDTO::fromStrapiResult(['data' => $collection['data'][0]])->toArray();
                        //print_r($dto);
                    }
                    if($taxonomy === 'tags') {
                        $dto = CmsPostDTO::fromStrapiResult(['data' => $taxonomyData])->toArray();
                        $dto['canonical_url'] = base_url('learn-ai/tag/' . $dto['url']);
                        $this->data['data'][] = $dto;
                        //print_r($dto);
                    }

                    //print_r($collection);
                    //echo "\r\n\r\n\r\n\r\n\r\n";
                }
            }

            //print_r($dt);
           
        }

        return $this->renderXmlResponse('cms/sitemaps/sitemap-main', 'cms_sitemap_main_xml');
    }
    
    /**
     * Returns main sitemap XSL with proper content type
     * 
     * @return string | null
     */
    public function getSitemapXsl(): ?string
    { 
        $this->data['title_before_sitemap'] = $this->cacheService->get('cms_title_before_sitemap');
        $this->data['title_after_sitemap'] = $this->cacheService->get('cms_title_after_sitemap');
        return $this->renderXmlResponse('cms/sitemaps/sitemap-xsl', 'cms_sitemap_xsl');
    }

    public function getSitemapXslIndex(): ?string
    {
        return $this->renderXmlResponse('cms/sitemaps/sitemap-xsl-index', 'cms_sitemap_xsl_index');
    }

    /**
     * Returns main sitemap XSL with proper content type
     * 
     * @return string | null
     */
    public function getMainSitemapXsl(): ?string
    {
        return $this->renderXmlResponse('cms/sitemaps/main-sitemap', 'cms_main_sitemap_xsl');
    }
    
    /**
     * Returns sitemap index XML with proper content type
     * 
     * @return string | null
     */
    public function getSitemapIndexXml(): ?string
    {
        return $this->renderXmlResponse('cms/sitemaps/sitemap_index', 'cms_sitemap_index_xml');
    }
    
    /**
     * Returns page sitemap XML with proper content type
     * 
     * @return string | null
     */
    public function getPageSitemapXml(): ?string
    {
        return $this->renderXmlResponse('cms/sitemaps/page-sitemap', 'cms_page_sitemap_xml');
    }
    
    /**
     * Returns robots.txt with proper content type
     * 
     * @return string | null
     */
    public function getRobotsTxt($viewPath = 'cms/robots', $cacheKey = 'cms_robots_txt'): ?string
    {
        // cache strategy
        $cacheStrategy = $this->strapiResolver->useCache();
        
        // do not cache the following cache keys
        if (in_array($cacheKey, ['cms_sitemap_xsl'])) {
            $cacheStrategy = false; // Do not cache these keys
        }

        if ($cacheStrategy === false) {
            // Do not use cache
            return view($viewPath, $this->data);
        }

        $cacheKey = $cacheKey . '_' . md5(base_url());

        // cached content
        $cached = $this->cacheService->get($cacheKey);

        // refresh
        if ($cacheStrategy === 'refresh') {
            $cached = null; // Ignore any cached content
        }

        // generate or use cached content
        if ($cached === null) {
            //print_r($this->data); die();
            $view = view($viewPath, $this->data);
            $this->cacheService->set($cacheKey, $view, null, self::CACHE_DURATION);
            return $view;
        }

        return $cached;
    }
    
    /**
     * Helper method to render XML responses with caching
     * 
     * @param string $viewPath The view path
     * @param string $cacheKey The cache key
     * @return string | null
     */
    protected function renderXmlResponse(string $viewPath, string $cacheKey): ?string
    {
        // cache strategy
        $cacheStrategy = $this->strapiResolver->useCache();
        
        // do not cache the following cache keys
        if (in_array($cacheKey, ['cms_sitemap_xsl'])) {
            $cacheStrategy = false; // Do not cache these keys
        }

        $cacheKey = $cacheKey . '_' . md5(base_url());

        if ($cacheStrategy === false) {
            // Do not use cache
            return view($viewPath, $this->data);
        }

        // cached content
        $cached = $this->cacheService->get($cacheKey);

        // refresh
        if ($cacheStrategy === 'refresh') {
            $cached = null; // Ignore any cached content
        }

        // generate or use cached content
        if ($cached === null) {
            //print_r($this->data); die();
            $view = view($viewPath, $this->data);
            $this->cacheService->set($cacheKey, $view, null, self::CACHE_DURATION);
            return $view;
        }

        return $cached;
    }
    
    /**
     * Refresh the SEO cache (can be called from a cron job)
     * 
     * @return bool
     */
    public function refreshSeoCache(): bool
    {
        try {
            // Get fresh content for all SEO-related views
            $this->cacheService->delete('cms_sitemap_xml');
            $this->cacheService->delete('cms_sitemap_articles_xml');
            $this->cacheService->delete('cms_sitemap_pages_xml');
            $this->cacheService->delete('cms_sitemap_xsl');
            $this->cacheService->delete('cms_main_sitemap_xsl'); // migrated from wordpress
            $this->cacheService->delete('cms_sitemap_index_xml'); // migrated from wordpress
            $this->cacheService->delete('cms_page_sitemap_xml'); // migrated from wordpress
            $this->cacheService->delete('cms_robots_txt');
            
            // Pre-populate the cache
            $this->getSitemapXml();
            $this->getSitemapArticlesXml();
            $this->getSitemapPagesXml();
            $this->getSitemapXsl();
            $this->getMainSitemapXsl();
            $this->getSitemapIndexXml();
            $this->getPageSitemapXml();
            $this->getRobotsTxt();
            
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Failed to refresh SEO cache: ' . $e->getMessage());
            return false;
        }
    }
}