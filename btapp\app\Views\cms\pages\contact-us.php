<?= view('cms/partials/css/caller') ?>
<?= view('cms/partials/css/common') ?>
<?= view('cms/partials/css/openchat') ?>
<?= view('cms/partials/css/section-container') ?>
<?= view('cms/partials/scripts/common') ?>
<?= view('cms/partials/scripts/sitebot') ?>
<?= view('cms/partials/scripts/toastr') ?>
<?= view('cms/partials/scripts/google-recaptcha') ?>
<?= view('cms/partials/scripts/contact-us') ?>

<div class="header-background">
    <div class="header">
        <h1>Contact Us Anytime</h1>
        <p>
            We are happy to receive inquiries about our website and our services. Whether you have a question about a specific topic, need help with a tool or tutorial, or just want to provide feedback, we are always here to listen.
        </p>
    </div>
</div>

<div class="container">
    <div class="contact-info">
        <h2>Let's Keep In Touch</h2>
        <span>
            If you have any questions or comments about our website, or if you would like to learn more about our membership options, please don't hesitate to get in touch with us. Simply fill out the form with your name, email address, and
            message, and we will do our best to respond to your inquiry as soon as possible.
        </span>
        <hr class="solid" />
        <span class="telecom_widget">
            <img class="telecom" src="https://assets.ai-pro.org/assets/wp-content/uploads/2025/01/telecom-new-2.png" />
            <div id="call-widget" style="height: 60px; border: none; width: 100%; overflow: auto; margin-left: -10px; display: inline;"></div>
            <script nonce="<?= $nonce ?>">
                new AIProCallWidget({ container: '#call-widget', position: 'inline', width: '100%', height: '200px' });
            </script>
        </span>
        <p>Monday-Friday</p>
        <p>7AM-4PM &amp; 10PM-7AM</p>
        <p>(Pacific Time)</p>
    </div>

    <!-- Modal HTML -->
    <div id="callerModalBackdrop" class="caller-modal-backdrop" style="display: none;"></div>
    <div id="callerModal" class="caller-modal" style="display: none;">
        <h2>Before we start</h2>
        <p>Please provide your information to start the conversation:</p>
        <form id="callerForm" data-hs-cf-bound="true">
            <label for="caller-name">Name:</label>
            <input type="text" id="caller-name" name="name" required="" />

            <label for="caller-email">Email Address:</label>
            <input type="email" id="caller-email" name="email" required="" />

            <button type="submit">Continue</button>
        </form>
    </div>
    <div class="form-section">
        <form id="contactForm" action="" method="POST" enctype="multipart/form-data" data-hs-cf-bound="true">
            <label class="name_email" for="name">Name <span class="required-asterisk">*</span></label>
            <input type="text" id="name" name="name" />
            <div id="nameError" class="error-message" style="color: red; display: none; margin-bottom: 10px;">This field is required</div>

            <label class="name_email" for="email">Email <span class="required-asterisk">*</span></label>
            <input type="email" id="email" name="email" />
            <div id="emailError" class="error-message" style="color: red; display: none; margin-bottom: 10px;">This field is required</div>

            <label class="concern_tell">I have a concern with</label>
            <div class="radio-container">
                <input type="radio" id="account" name="concern" value="Account Settings" />
                <label for="account">Account Settings</label>
            </div>

            <div class="radio-container">
                <input type="radio" id="plan" name="concern" value="Plan and Subscription" />
                <label for="plan">Plan and Subscription</label>
            </div>

            <div class="radio-container">
                <input type="radio" id="technical" name="concern" value="Technical Issues" />
                <label for="technical">Technical Issues</label>
            </div>

            <div class="radio-container">
                <input type="radio" id="other" name="concern" value="Others" />
                <label for="other">Others</label>
            </div>
            <br />

            <label class="concern_tell" for="message">Tell us more</label>
            <textarea id="message" name="message" rows="6"></textarea>

            <div class="g-recaptcha" data-sitekey="6Lc7qzwrAAAAAM0O17KU_KSD0iSZ0ya0qqWpMacR"></div>
            <div id="captchaError" class="error-message" style="color: red; display: none; margin-bottom: 10px;">This field is required</div>

            <button class="cta-sbmt" style="margin-top: 20px;" type="submit">Submit</button>
        </form>
    </div>
</div>

<div class="page-content">
    <section class="section-container">
        <div class="content-block">
            <h2 class="section-title">We're listening</h2>
            <p class="section-description">
                At AI Pro.org, we are dedicated to providing the most accurate and up-to-date information about artificial intelligence. We offer a range of resources, including articles, tutorials, and tools, to help you learn more about
                this exciting and rapidly evolving field.
            </p>
        </div>

        <div class="image-block">
            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/2023/01/cyborg-4-1.png" alt="We're listening" class="section-image" />

            <div class="quote-overlay">
                <p class="quote-text">
                    "Artificial intelligence is not a replacement for human intelligence, but rather an extension of it. As AI systems continue to learn and evolve, they have the potential to augment our own abilities and help us achieve
                    even greater things."
                </p>
                <p class="quote-author">- Anonymous</p>
            </div>
        </div>
    </section>
    <section class="section-container">
        <div class="content-block">
            <h2 class="section-title">Let's learn together</h2>
            <p class="section-description">
                In addition to our educational resources, we also offer a membership area for those who want to delve deeper into the world of AI. Our membership area includes exclusive content, access to a community of like-minded
                individuals, and opportunities to connect with experts in the field.
            </p>
        </div>

        <div class="image-block">
            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/2022/12/ai-art-fireman.png" alt="Let's learn together" class="section-image" />

            <div class="quote-overlay">
                <p class="quote-text">"Learning together is a journey, not a destination. By sharing knowledge and collaborating with others, we can achieve greater understanding and make a positive impact in the world."</p>
                <p class="quote-author">- Anonymous</p>
            </div>
        </div>
    </section>
</div>

<script nonce="<?= $nonce ?>">
    document.getElementById("contactForm").addEventListener("submit", function (e) {
        const recaptchaResponse = grecaptcha.getResponse();
        const captchaError = document.getElementById("captchaError");
        const name = document.getElementById("name");
        const email = document.getElementById("email");
        const nameError = document.getElementById("nameError");
        const emailError = document.getElementById("emailError");
        const nameAsterisk = document.querySelector('label[for="name"] .required-asterisk');
        const emailAsterisk = document.querySelector('label[for="email"] .required-asterisk');

        let isValid = true;

        nameError.style.display = "none";
        emailError.style.display = "none";
        captchaError.style.display = "none";
        name.style.border = "";
        email.style.border = "";
        nameAsterisk.style.color = "";
        emailAsterisk.style.color = "";

        if (!name.value.trim()) {
            nameError.style.display = "block";
            name.style.border = "2px solid red";
            nameAsterisk.style.color = "red";
            isValid = false;
        }

        if (!email.value.trim()) {
            emailError.style.display = "block";
            email.style.border = "2px solid red";
            emailAsterisk.style.color = "red";
            isValid = false;
        }

        if (!recaptchaResponse) {
            captchaError.style.display = "block";
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
        }
    });

    document.getElementById("name").addEventListener("input", function () {
        const nameError = document.getElementById("nameError");
        const name = document.getElementById("name");
        const nameAsterisk = document.querySelector('label[for="name"] .required-asterisk');
        if (name.value.trim()) {
            nameError.style.display = "none";
            name.style.border = "";
            nameAsterisk.style.color = "";
        }
    });

    document.getElementById("email").addEventListener("input", function () {
        const emailError = document.getElementById("emailError");
        const email = document.getElementById("email");
        const emailAsterisk = document.querySelector('label[for="email"] .required-asterisk');
        if (email.value.trim()) {
            emailError.style.display = "none";
            email.style.border = "";
            emailAsterisk.style.color = "";
        }
    });
</script>

<script nonce="<?= $nonce ?>">
    $(document).ready(function () {
        $("#contactForm").on("submit", function (event) {
            toastr.options.positionClass = "toast-top-center";
            event.preventDefault();
            $(".cta-sbmt").prop("disabled", true);

            var recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                toastr.error("Please complete the reCAPTCHA");
                $(".cta-sbmt").prop("disabled", false);
                return;
            }

            // Gather the form data
            var formData = {
                name: $("#name").val(),
                email: $("#email").val(),
                concern: $('input[name="concern"]:checked').val(),
                message: $("#message").val(),
                "g-recaptcha-response": recaptchaResponse,
            };

            console.log("Form Data: ", formData);
            $.ajax({
                url: "https://start.ai-pro.org/api/contact-us",
                type: "POST",
                data: formData,
                dataType: "json",
                beforeSend: function () {
                    // console.log("Submitting form...");
                },
                success: function (response) {
                    // console.log("Response: ", response);
                    if (response.success) {
                        toastr.success(response.data.msg || "Your message has been sent successfully.");
                        $(".form-section").html('<div style="display:flex; font-size:16px;">Thanks for contacting us! We will be in touch with you shortly</div>');
                        // setTimeout(function() {
                        //     location.reload();
                        // }, 2000);
                    } else {
                        toastr.error(response.error || "An error occurred.");
                        $(".cta-sbmt").prop("disabled", false);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // console.log("AJAX Error: ", textStatus, errorThrown); // Log AJAX error if occurs
                    toastr.error("An error occurred during the request.");
                    $(".cta-sbmt").prop("disabled", false);
                },
            });
        });
    });
</script>

<?= view('cms/partials/scripts/caller') /* this should be placed bellow "call us now" */ ?>
