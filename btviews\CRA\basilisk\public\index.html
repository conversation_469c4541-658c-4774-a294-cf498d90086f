<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link href='https://fonts.googleapis.com/css?family=Alegreya Sans' rel='stylesheet'>
    <title>{{ PAGE.page_title }}</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" integrity="sha384-R334r6kryDNB/GWs2kfB6blAOyWPCxjdHSww/mo7fel+o5TM/AOobJ0QpGRXSDh4" crossorigin="anonymous">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js" integrity="sha384-UG8ao2jwOWB7/oDdObZc6ItJmwUkR/PfMyt9Qs5AwX7PsnYn1CRKCTWyncPTWvaS" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js" integrity="sha384-Si3HKTyQYGU+NC4aAF3ThcOSvK+ZQiyEKlYyfjiIFKMqsnCmfHjGa1VK1kYP9UdS" crossorigin="anonymous"></script>
    <link rel="preload" as="image" href="%REACT_APP_BTUTIL_API_URL%ext-app/images/bg.png">
    <script>
      var user_data = [];
      {% if USER is defined %}
      user_data = {{ USER|raw }};
      {% endif %}
      var baseURL = "%REACT_APP_BASE_URL%";
      var start_URL = "%REACT_APP_BASE_URL%";
      toastr.options = {
        positionClass: 'toast-top-center'
      };
    </script>
    <script>
      let ver = new Date().getTime();
      let btutilAssetCSS = document.createElement('link');
      btutilAssetCSS.setAttribute("rel", "stylesheet");
      btutilAssetCSS.setAttribute("href", "%REACT_APP_BTUTIL_CSS_URL%" + ver);
      document.head.appendChild(btutilAssetCSS);
    </script>
    <style>
      #modal-container {
        font-family: 'Alegreya Sans', sans-serif!important;
        z-index: 9999999;
      }
      button.close {
        float: right;
        font-size: 25px;
      }
    </style>
    {% include 'includes/head_session.twig' %}
    {% include 'includes/head_mixpanel.twig' %}
    {% include 'includes/head_vwo.twig' %}
    {% include 'includes/head_quora.twig' %}
    {% include 'includes/head_fbmeta.twig' %}
    {% include 'includes/head_tiktok.twig' %}
    {% include 'includes/head_twitter.twig' %}
    {% include 'includes/head_gtag.twig' %}
    {% include 'includes/head_bing.twig' %}
    {% include 'includes/head_rewardful.twig' %}
    {% include 'includes/head_fullstory.twig' %}
    {% include 'includes/head_hubspot.twig' %}
    {% include 'includes/head_f_sale.twig' %}
    {% include 'includes/head_cookieyes.twig' %}
    {% include 'includes/head_flags.twig' %}

  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <form data-rewardful></form>
    <div id="modal-container" class="no-subtitle" style="display: none;">
      <div id="modal-content">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h2 id="modal-title">Switch to PRO to continue using <span class="app-name">ChatGPTPro</span></h2>
        <p id="modal-desc">Switch now to PRO and get access to <span>14</span> different creativity and productivity AI tools.</p>
        <a id="modal-cta" href="#" target="_parent">Continue</a>
      </div>
    </div>
  </body>
  <script>
    document.querySelector('#modal-container').addEventListener('click', function(e){
      if(e.target && e.target.id == 'modal-container') {
        document.getElementById('modal-container').style.display = 'none';
      }
    });
    document.querySelector('#modal-container .close').addEventListener('click', function(){
      document.getElementById('modal-container').style.display = 'none';
    });
  </script>
</html>
