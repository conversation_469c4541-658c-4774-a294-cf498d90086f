<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterPlanTable20230711 extends Migration
{
    private $table = "plan";

    public function up()
    {
        if ($this->db->fieldExists("plan_description", $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            "plan_description" => [
                "type" => "TEXT",
                "default" => "",
                "after" => "display_txt3"
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        //
    }
}
