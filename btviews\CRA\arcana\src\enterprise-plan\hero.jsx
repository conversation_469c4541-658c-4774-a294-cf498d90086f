import { useState, useEffect } from 'react';
import classNames from 'classnames';
import heroBg from './images/hero_bg.png';
import axios from 'axios';
import Chatfieldmodal from './chatfieldmodal';
import './hero.css';

export default function Hero() {
    const defaultForm = {
        firstname: '',
        lastname: '',
        companyName: '',
        jobTitle: '',
        email: '',
        phoneNumber: '',
        companySize: '',
        reason: '',
        comments: '',
    };

    // const formErrors = {...defaultForm};
    const [chatModalOpen, setChatModelOpen] = useState(false);
    const [formData, setFormData] = useState(defaultForm);
    const [isMobile, setIsMobile] = useState(false);
    const [hubspotAttached, setHubspotAttached] = useState(false);

    const handleEdit = (e) => {
        const id = (e.target.id).replace("mobile-","");
        if (id === 'reason') {
            setFormData({...formData, reason: e.target.value});
        }
    };

    // const handleValidation = (edited, value) => {

    // };

    const handleSubmit = async () => {
        const portalId = "44168812";
        const formId = "4a67c003-4266-4ceb-8ac8-9cde0ea72eaf";
        const region = "na1";

        const payload = {
            fields: [
                { name: "firstname", value: formData.firstname },
                { name: "lastname", value: formData.lastname },
                { name: "0-2/name", value: formData.companyName },
                { name: "jobtitle", value: formData.jobTitle },
                { name: "email", value: formData.email },
                { name: "phone", value: formData.phoneNumber },
                { name: "0-2/company_size___enterprise_gen", value: formData.companySize },
                { name: "enterprise_leadgen_form_purpose", value: formData.reason },
                { name: "questions_or_comments", value: formData.comments },
            ]
        };

        try {
            await axios.post(
                `https://api.hsforms.com/submissions/v3/integration/submit/${portalId}/${formId}?region=${region}`,
                payload,
                { headers: { 'Content-Type': 'application/json' } }
            );

            alert("Form submitted successfully!");
            setFormData(defaultForm); //reset
        } catch (error) {
            console.error("Submission error:", error);
            alert("There was an error submitting the form.");
        }
    };

    useEffect(() => {
        if (!hubspotAttached) {
            const script = document.createElement("script");
            script.src = "//js.hsforms.net/forms/embed/v2.js";
            script.async = true;
            script.onload = () => setHubspotAttached(true);
            document.body.appendChild(script);
        }

        if (window.hbspt) {
            window.hbspt.forms.create({
                region: "na1",
                portalId: "44168812",
                formId: "4a67c003-4266-4ceb-8ac8-9cde0ea72eaf",
                target: "#hubspotForm",
                cssRequired: true,
                onFormReady: function($form) {
                    document.getElementsByName("firstname")[0].placeholder = "e.g. John";
                    document.getElementsByName("lastname")[0].placeholder = "e.g. Doe";
                    document.getElementsByName("email")[0].placeholder = "e.g. <EMAIL>";
                    document.getElementsByName("company")[0].placeholder = "e.g. Acme Corp.";
                    document.getElementsByName("jobtitle")[0].placeholder = "e.g. Marketing Manager";
                    document.getElementsByName("phone")[0].placeholder = "e.g. ****** 567 8900";

                    const link = document.querySelector('.legal-consent-container a');
                    link.href = '/privacy-policy';
                },
            });
        }

        const handleResize = () => {
            setIsMobile(window.innerWidth <= 640);
        };
    
        handleResize(); // Check on initial render
    
        window.addEventListener('resize', handleResize);
        return () => {
        window.removeEventListener('resize', handleResize);
        };
    }, [hubspotAttached]);

    return <>
        <section id="hero-section" className={classNames(`hero-section bg-cover bg-center bg-no-repeat py-12 md:py-16 lg:py-20`)} style={{ backgroundImage: `url(${heroBg})`}}>
            <div className={`mx-auto max-w-[1440px]`}>
                <div className="ep_container px-4 md:px-6">
                    <div className="mx-auto max-w-[1280px] grid gap-6 lg:grid-cols-2 lg:gap-10 md:min-h-[738px]">
                        <div className="flex flex-col items-center max-w-640:justify-center md:items-start md:pt-[15%]">
                            <div className="flex flex-col gap-[30px] max-w-640:items-center max-w-640:text-center md:w-[545px] md:mb-[150px]">
                                <div className="inline-block rounded-full bg-blue-100 px-[20px] py-[15px] text-sm text-[#1559ED] w-[fit-content] font-bold">
                                    AI-PRO ENTERPRISE FOR BUSINESS
                                </div>
                                <div className="flex flex-col gap-[25px]">
                                    <h1 className="text-4xl font-bold sm:text-[64px] max-w-640:text-[40px] leading-[100%]">
                                        <span className="text-[#1559ED]">Productivity</span> in<br />
                                        <span className="text-gray-900">Higher Education & Business</span>
                                    </h1>
                                    <p className="max-w-[600px] text-gray-500 text-[24px]">
                                        Designed to bring out the full potential of AI in higher education and enterprise-level environments
                                    </p>
                                </div>    
                            </div>
                            <div className="md:hidden w-full px-[20px]">
                                <div className="py-[20px]">
                                    <button className="gradient-button w-full rounded-md text-white text-[16px] font-semibold h-[48px]" onClick={() => setChatModelOpen(true)}>
                                        Talk to us
                                    </button>    
                                </div>
                            </div>
                        </div>
                        
                        {isMobile && 
                            <Chatfieldmodal 
                                isOpen={chatModalOpen} 
                                handleClose={() => setChatModelOpen(false)} 
                                handleEdit={handleEdit} 
                                handleSubmit={handleSubmit} />
                        }
                        
                        <div className={`flex ${isMobile ? 'hidden' : ''}`}>
                            <div id="hubspotForm" />
                        </div>

                        <div className="hidden items-center justify-end">
                            <div className="w-[550px] ml-auto space-y-4 rounded-lg bg-white p-6 shadow-lg">
                                <div className="text-left">
                                    <h3 className="text-[24px] font-medium">Fill out our contact form and we'll get in touch with you.</h3>
                                </div>
                                
                                <div className="space-y-3">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <label htmlFor="first-name" className="block font-semibold">
                                                First Name*
                                            </label>
                                            <input
                                                id="first-name"
                                                placeholder="e.g. John"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                            <p></p>
                                        </div>
                                        <div className="space-y-2">
                                            <label htmlFor="last-name" className="block font-semibold">
                                                Last Name*
                                            </label>
                                            <input
                                                id="last-name"
                                                placeholder="e.g. Doe"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <label htmlFor="company-name" className="block font-semibold">
                                                Company Name*
                                            </label>
                                            <input
                                                id="company-name"
                                                placeholder="e.g. Acme Corp."
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <label htmlFor="job-title" className="block font-semibold">
                                                Job Title*
                                            </label>
                                            <input
                                                id="job-title"
                                                placeholder="e.g. Marketing Manager"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <label htmlFor="email" className="block font-semibold">
                                                Email Address*
                                            </label>
                                            <input
                                                id="email"
                                                type="email"
                                                placeholder="e.g. <EMAIL>"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <label htmlFor="phone" className="block font-semibold">
                                                Phone Number*
                                            </label>
                                            <input
                                                id="phone"
                                                type="tel"
                                                placeholder="e.g. ****** 567 8900"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                    </div>
                                    <div className="space-y-2">
                                        <label htmlFor="company-size" className="block font-semibold">
                                            Company Size
                                        </label>
                                        <select
                                            id="company-size"
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 appearance-none bg-white"
                                            onChange={(e) => handleEdit(e)}
                                        >
                                            <option value="">Please Select</option>
                                            <option value="self-employed">Self-employed</option>
                                            <option value="1-10">1-10 employees</option>
                                            <option value="11-50">11-50 employees</option>
                                            <option value="51-200">51-200 employees</option>
                                            <option value="201-500">201-500 employees</option>
                                            <option value="501-1000">501-1,000 employees</option>
                                            <option value="1001-5000">1,001-5,000 employees</option>
                                            <option value="5001-10000">5,001-10,000 employees</option>
                                            <option value="10001+">10,001+ employees</option>
                                        </select>
                                    </div>
                                    <div className="space-y-2">
                                        <label htmlFor="reason" className="block font-semibold">
                                            Reason for completing the form*
                                        </label>
                                        <select
                                            id="reason"
                                            className="w-full rounded-md border border-gray-300 px-3 py-2 appearance-none bg-white"
                                            onChange={(e) => handleEdit(e)}
                                        >
                                            <option value="">Please Select</option>
                                            <option value="support">I need help from your support agents.</option>
                                            <option value="custom">I have a question about your AI solutions.</option>
                                            <option value="enterprise">I have a question about your Enterprise plan.</option>
                                        </select>
                                    </div>
                                    {formData.reason &&
                                        <div className="space-y-2">
                                            <label htmlFor="mobile-comments" className="text-base font-semibold block">
                                                Questions or Comments
                                            </label>
                                            <textarea
                                                id="mobile-comments"
                                                className="w-full rounded-md border border-gray-300 px-3 py-2 min-h-[40px]"
                                                onChange={(e) => handleEdit(e)}
                                            />
                                        </div>
                                    }
                                    <button className="gradient-button w-full py-2 rounded-md text-white">Submit Form</button>
                                    <p className="text-xs text-gray-500 text-center">
                                        By clicking "Submit" I agree to receive email or phone communications about your services, offers,
                                        and promotions. We use your information as described in our{" "}
                                        <a href="https://ai-pro.org/privacy-policy/" target="_blank" rel="noreferrer" className="text-blue-600 hover:underline">
                                        Privacy Notice
                                        </a>
                                        .
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </>
}