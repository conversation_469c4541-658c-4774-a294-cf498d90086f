						<nav class="post-navigation ">
							<?php if($prev_post) { ?>
							<a href="/<?= $prev_post[0]->post_name; ?>" class="nav-item-prev">
								<!-- <?php if($prev_post[0]->og_image_meta->url) { ?> -->
								<figure class="ct-image-container  ">
									<img width="300" height="200" 
										src="<?= transformAiProUrl($prev_post[0]->og_image_meta->url ?? '' ) ?>"
										class="attachment-medium size-medium wp-post-image" 
										alt="<?= mb_convert_encoding($prev_post[0]->og_image_meta->alt ?? '' , 'UTF-8', 'ISO-8859-1') ?>"
										decoding="async" loading="lazy" 
										sizes="(max-width: 300px) 100vw, 300px" 
										itemprop="image" 
										style="aspect-ratio: 1/1;">
									<svg width="20px" height="15px" viewBox="0 0 20 15">
										<polygon points="0,7.5 5.5,13 6.4,12.1 2.4,8.1 20,8.1 20,6.9 2.4,6.9 6.4,2.9 5.5,2 "></polygon>
									</svg>
								</figure>
								<!-- <?php }	?> -->
								<div class="item-content">
									<span class="item-label">Previous <span>Post</span></span>
									<span class="item-title ct-hidden-sm"><?= $prev_post[0]->post_title; ?></span>
								</div>
							</a>
							<?php } ?>
							<?php if($next_post) { ?>
							<a href="/<?= $next_post[0]->post_name; ?>" class="nav-item-next">
								<div class="item-content">
									<span class="item-label">Next <span>Post</span></span>
									<span class="item-title ct-hidden-sm"><?= $next_post[0]->post_title; ?></span>
								</div>
								<?php if($next_post[0]->og_image_meta->url) { ?>
								<figure class="ct-image-container  ">
									<img width="300" height="200" 
										src="<?= transformAiProUrl($next_post[0]->og_image_meta->url) ?>"
										class="attachment-medium size-medium wp-post-image" 
										alt="<?= mb_convert_encoding($next_post[0]->og_image_meta->alt ?? '' , 'UTF-8', 'ISO-8859-1') ?>"
										decoding="async" loading="lazy" 
										sizes="(max-width: 300px) 100vw, 300px" 
										itemprop="image" 
										style="aspect-ratio: 1/1;">
									<svg width="20px" height="15px" viewBox="0 0 20 15">
										<polygon points="14.5,2 13.6,2.9 17.6,6.9 0,6.9 0,8.1 17.6,8.1 13.6,12.1 14.5,13 20,7.5 "></polygon>
									</svg>
								</figure>
								<?php }	?>
							</a>
							<?php } ?>
						</nav>