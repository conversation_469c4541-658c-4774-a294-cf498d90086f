import { React, useState, useEffect, useRef } from 'react';
import './style.css';
import { motion } from "framer-motion";
// import Header from '../header/headerlogo';
import axios from 'axios';
import { Auth } from '../core/utils/auth';
import { SetCookie, GetCookie } from '../core/utils/cookies';
import { ValidatePassword } from '../core/utils/validation';
// import { RiEyeLine, RiEyeOffLine } from 'react-icons/ri';
import errors from '../locales/errors.json';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import RegisterButtons from "./RegisterButtons";


const params = new URLSearchParams(window.location.search);

function Register() {
  const [ email, setEmail ] = useState("");
  const [ password, setPassword ] = useState("");
  const [ emailError, setEmailError ] = useState('');
  const [ passwordError, setPasswordError ] = useState('');
  const [ tocError, setTocError ] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [willRedirect, setWillRedirect] = useState(true);
  const members = GetCookie("members") ? GetCookie("members") : "";
  const kt8typtb = GetCookie("kt8typtb") ? GetCookie("kt8typtb") : "";
  const [emailOptIn, setEmailOptIn] = useState("");
  const honeypotRef = useRef(null);
  const { t } = useTranslation();


  const auth = Auth();

  useEffect(() => {
    //Display google error from google signin
    let google_error_code = params.get("e");
    let google_error = "";
    if (google_error_code!==null && google_error_code!==''){
      google_error = errors.find(({ error_code }) => error_code === google_error_code)
      if (google_error!==undefined){
        window.toastr.error(google_error.msg);
      }else{
        window.toastr.error(google_error_code);
      }
    }
    if (GetCookie("emailopt") === 'on') {
      setEmailOptIn(true);
    }
  }, [t]);

  if(auth === undefined) return;
  if(willRedirect) {

    if(!auth === false) {
      if(auth.status === 'active') {
        window.location.href = '/my-account';
        return;
      }
      if (members === 'on' && kt8typtb === 'basilisk') {
        window.location.href = '/my-account';
      }else{
        window.location.href = '/pricing-redirect';
      }
      return;
    }
  }

  const validatePassword = () => {
    let msg = ValidatePassword(password);
    if (msg) {
      setPasswordError(msg);
      setPasswordStrength(0); // Reset password strength when there's an error
      return false;
    }

    const strength = calculatePasswordStrength(password);
    setPasswordStrength(strength);
    return true;
  };

  // const calculatePasswordStrength = (password) => {
  //   if (password.length === 0) {
  //     return 0;
  //   } else if (password.length <= 4) {
  //     return 1;
  //   } else if (password.length <= 8) {
  //     return 2;
  //   } else if (password.length <= 12) {
  //     return 3;
  //   } else {
  //     return 4;
  //   }
  // };

  const calculatePasswordStrength = (password) => {
    let strength = 0;

    if (password.length >= 8) strength++;              
    if (password.length >= 12) strength++;             
    if (/[A-Z]/.test(password)) strength++;            
    if (/[0-9]/.test(password)) strength++;            
    if (/[^A-Za-z0-9]/.test(password)) strength++;     

    return Math.min(strength, 5);
  };


  const validateEmail = () => {
    let isPassed = false;
    const allowedCharsRegex = /^[a-zA-Z0-9@._]+$/;

    if (!email) {
      setEmailError(t('basilisk.register.index.email_error'));
    } else if (!allowedCharsRegex.test(email)) {
      setEmailError(t('basilisk.register.validation.passwordInvalidChars')); 
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError(t('basilisk.register.index.invalid_email'));
    } else {
      setEmailError('');
      isPassed = true;
    }
    return isPassed;
  };


  const validateToc = () => {
    let isToc = false;
    if (!agreeTerms) {
      setTocError(t('basilisk.register.index.terms_and_condition'));
    } else {
      setTocError('');
      isToc = true;
    }
    return isToc;
  };

  const handleCheckboxChange = (event) => {
    setAgreeTerms(event.target.checked);
  };

  function registerUser(){

    if (honeypotRef.current?.value) {
      SetCookie('ishoneypot', "yes", { path: '/' });
      SetCookie('ishoneypot', "yes", { domain: '.ai-pro.org', path: '/' });
      // setTimeout(() => {
      //   window.location.href = ("https://start.ai-pro.org/typage");
      // }, 500);
      // return;
    }
    setWillRedirect(false);
    var isEmailPassed = validateEmail();
    var isPasswordPassed = validatePassword();
    var agreeTerms = validateToc();
    // if(!isEmailPassed || !isPasswordPassed) return;
    if (!isEmailPassed || !isPasswordPassed || !agreeTerms) return;

    document.querySelector(".loader-container").classList.add('active');
    axios.post(`${process.env.REACT_APP_API_URL}/t/register`, {
      email,
      password,
      pass_con: password,
      emailOptIn
    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){
      let output = res.data;

      if(output.success) {
        try {
          if (GetCookie("emailopt") === 'on') {
            // Mixpanel tracking
            window.mixpanel.track("register", {
              emailOptIn
            });
          }
          // Quora tracking
          window.qp('track', 'CompleteRegistration');
          // TikTok pixel tracking code
          window.ttq.identify({ "email": `${email}` })
          window.ttq.track('Registration Page', {
            "contents": [
              {
                "content_id": `${output.data.login_token}`,
                "content_name": `${email}`
              }
            ],
            "description": "Registration (Basilisk)"
          })
        } catch (error) {
          console.error('An error occurred:', error.message);
        }
        window.toastr.success(t('basilisk.register.index.success'));
        SetCookie("access", output.data.login_token);
        if (members === 'on' && kt8typtb === 'basilisk') {
          window.location.href = '/my-account';
        }else{
          window.location.href = '/pricing-redirect';
        }
        return;
      }
      document.querySelector(".loader-container").classList.remove('active');
      if (output.data) {
        if (output.data.msg.includes("already exists")) {
          setEmailError("Email address has already been taken");
          window.location.href = "/start-splash-redirect";
        } else {
          setEmailError(output.data.msg);
          // clear email
          setEmail("");
          const  emailInput = document.querySelector(".email-input");
          if (emailInput) {
            emailInput.value = '';
          }

          // terms-checkbox
          setAgreeTerms(false);
          const termsCheckbox = document.querySelector("#termsCheckbox");
          if (termsCheckbox) {
            termsCheckbox.checked = false;
          }
        }
      }
    }).catch(function (error) {
      if (error.response && error.response.status===429) {
        document.querySelector(".loader-container").classList.remove('active');
        window.toastr.error(t('basilisk.register.index.too_many_request'));
      }
    });
  }

  return (
    <>
      <Helmet>
         <title>
          {`${t('basilisk.register.index.registration_flow04')}`}
        </title>
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      </Helmet>
      <div className="register_b flow4-bg p-[8px] lg:p-[20px] pt-[50px] min-h-[800px] h-[100vh] flex justify-center lg:pt-10 md:pt-2">
          <div className="mx-auto w-[95%] absolute
                bg-white 
                md:px-4 
                lg:px-0 
                px-[15px] 
                sm:px-0 
                lg:w-[1124px] 
                lg:mt-[6rem] 
                max-h-fit
                block 
                lg:content-center 
                pt-[3rem] 
                pb-[2rem] 
                rounded-[5px] 
                custom1018:max-w-[500px]">
            <div className="reg_col text-center mb-8">
              <h1 className="text-[30px] lg:leading-[28px] leading-[44px] system-title lg:text-[30px] font-bold text-center mb-6 lg:mb-[19px] text-[#192a3d]">{t('basilisk.register.index.create_your_account')}</h1>
              <div className="">
                <div className="lg:px-2 px-[15px] py-2">
                  <label className="relative block text-left">
                    <input type="text" ref={honeypotRef} name="user_phone" className="hidden absolute left-[-9999px]" autocomplete="off" />
                    {emailError && <span className="box1 sb6 text-red-500 text-xs text-right p-1 md:right-0 lg:right-[19rem] mb-2 absolute z-10 mt-[48px] ml-[-5px]">{emailError}</span>}
                    <div className="xemail pb-[30px] w-full max-w-[500px] lg:w-[501px] mx-auto">
                      <label className="iump-labels-register"><span className="text-red-600">*</span>{t('basilisk.register.index.email')}</label>
                      <input
                      className={`email-input placeholder:italic hover:border-[#31aae1] hover:shadow-[2px_0_30px_rgba(0,0,0,0.1)] placeholder:text-slate-400 block bg-white w-full border ${emailError ? 'alertinfo' : 'border-slate-300'} rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:ring-1 sm:text-sm`}
                      type="email"
                      name="email"
                      onBlur={validateEmail}
                      onChange={(event) => setEmail(event.target.value)}
                      onKeyUp={(event) => {
                        setEmailError('');
                        setEmail(event.target.value);
                        if(event.keyCode === 13) registerUser();
                      }}/>
                    </div>
                    {passwordError && <span className="box2 sb6 text-red-500 text-xs text-right p-1 md:right-0 lg:right-[19rem] mb-2 absolute z-10 mt-[47px] ml-[-5px]">{passwordError}</span>}
                    <div className="xepass w-full max-w-[500px] lg:w-[501px] mx-auto">
                      <label className="iump-labels-pass">
                        <span className="text-red-600">*</span>{t('basilisk.register.index.password')}
                      </label>
                        <div className="password-input-container">
                          <input
                            className={`placeholder:italic hover:border-[#31aae1] hover:shadow-[2px_0_30px_rgba(0,0,0,0.1)] placeholder:text-slate-400 block bg-white w-full border  ${passwordError ? 'alertinfo' : 'border-slate-300'} rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm`}
                            type={showPassword ? 'text' : 'password'}
                            name="password"
                            onFocus={(event) => {
                              // Auto-scroll on mobile only when password field is focused
                              const isMobile = ('ontouchstart' in window) && window.innerWidth <= 768;
                              if (isMobile) {
                                setTimeout(() => {
                                  event.target.scrollIntoView({ 
                                    behavior: 'smooth', 
                                    block: 'center' 
                                  });
                                }, 300);
                              }
                            }}
                            onBlur={validatePassword}
                            onChange={(event) => {
                              setPassword(event.target.value)
                              setPasswordStrength(calculatePasswordStrength(event.target.value))
                            }}
                            onKeyUp={(event) => {
                              setPasswordError('');
                              setPassword(event.target.value);
                              setPasswordStrength(calculatePasswordStrength(event.target.value))
                              if (event.keyCode === 13) registerUser();
                            }}
  
                          />
                          <div className="password-toggle-icon z-3" onClick={() => setShowPassword(!showPassword)}>
                            {
                              showPassword ? (
                                <span>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#708090" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z" />
                                    <path d="M3 3l18 18" />
                                    <circle cx="12" cy="12" r="3" />
                                  </svg>
                                </span>
                              ) : (
                                <span>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#708090" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                  </svg>
                                </span>
                              )
                            }
                          </div>
                        </div>
                    </div>
                    <div className="w-full max-w-[500px] mx-auto">
                      <div className="password-strength-bars flex gap-[1px] h-2 mt-1">
                        {[...Array(5)].map((_, index) => {
                          let barColor = 'bg-gray-200';
                          if (password) {
                            if (passwordStrength === 5 && index < 5) barColor = 'bg-[#00ff00]';
                            else if (passwordStrength === 4 && index < 4) barColor = 'bg-[#00ff00]';
                            else if (passwordStrength === 3 && index < 3) barColor = 'bg-[#99ff00]';
                            else if (passwordStrength === 2 && index < 2) barColor = 'bg-[#ff9900]';
                            else if (passwordStrength === 1 && index === 0) barColor = 'bg-[#ff0000]';
                          }
                          return (
                            <div
                              key={index}
                              className={`w-[40px] rounded-full transition-all duration-300 h-full ${barColor}`}
                            />
                          );
                        })}
                      </div>
                    </div>

                    <p className="text-sm mt-[5px] md:mt-[17px] mx-auto text-right font-medium text-[#999999] min-h-[1.25rem] transition-opacity duration-300 w-full lg:max-w-[500px]">
                      {passwordStrength === 1 && password && t('basilisk.register.index.very_weak')}
                      {passwordStrength === 2 && password && t('basilisk.register.index.weak')}
                      {passwordStrength === 3 && password && t('basilisk.register.index.moderate')}
                      {passwordStrength === 4 && password && t('basilisk.register.index.strong')}
                      {passwordStrength === 5 && password && t('basilisk.register.index.strong')}
                    </p>

                    {tocError && <span className="box3 sb6 text-red-500 text-xs text-left p-1 mb-2 absolute right-0 lg:right-[19rem] z-10 mt-[35px] ml-[42px]">{tocError}</span>}  
                    {GetCookie("emailopt") === 'on' ? (
                      <div className="text-left mb-[20px] mt-[15px] mx-4 flex items-center">
                        <input
                          type="checkbox"
                          id="email_opt"
                          checked={emailOptIn}
                          onChange={(event) => {
                            setEmailOptIn(event.target.checked);
                          }}
                          className="mr-2 min-w-[18px] h-[18px]"
                        />
                        <label htmlFor="termsCheckbox" className="text-xs text-[#0074a2]">
                          {/* <a href="#" className="hover:underline text-[14px]" rel="noreferrer">I want to receive emails with advertising, news, suggestions, or marketing promotions.</a> */}
                        </label>
                      </div>
                    ): ''}
                    <div className="text-left mb-[20px] mt-[15px] flex mx-auto sm:w-[67%] lg:w-[500px]">
                      <input
                        type="checkbox"
                        id="termsCheckbox"
                        checked={agreeTerms}
                        onChange={handleCheckboxChange}
                        className="mr-2 w-[18px] h-[18px] checkbox-input !accent-[#2872fa]"
                      />
                      {/* <label htmlFor="termsCheckbox" className="text-xs text-[#0074a2]">
                        <a href="https://ai-pro.org/member-tos-page/" target="_blank" className="hover:underline text-[14px]" rel="noreferrer">{t('basilisk.register.index.agree_terms_and_condition')}</a>
                      </label> */}
                      <label htmlFor="termsCheckbox" className="text-xs text-[#0074a2]">
                        <span className='text-black text-[12px] md:text-[14px]'>{t('basilisk.register.index.by_continuing')} <a href="/member-tos-page" target="_blank" className="hover:underline text-[14px] text-[#0074a2]" rel="noreferrer">{t('basilisk.register.index.terms_and_condition_link')}</a>{t('basilisk.register.index.and')}<a href="/privacy-policy/" target="_blank" className='text-[#0074a2] hover:underline' rel="noreferrer">{t('basilisk.register.index.privacy_policy')}</a>.</span> 
                      </label>
                    </div>
                  </label>

                  <motion.button
                    onClick={registerUser}
                    className={`bg-[#2872fa] hover:bg-blue-400 mb-6 text-[20px] font-bold text-white py-[14px] px-[15px] my-3 rounded-sm w-full max-w-[500px] register-btn h-[63px]`}
                  >
                    {t('basilisk.register.index.continue')}
                  </motion.button>
                  {/* <div className="py-4 text-sm text-[#0074a2]">
                    {t('basilisk.register.index.already_have_an_account')} <a href="/login" className="text-blue-500">{t('basilisk.register.index.login')}</a>
                  </div> */}
                  <RegisterButtons />
                </div>
              </div>
            </div>
          </div>
        </div>
    </>
  );
}

export default Register;