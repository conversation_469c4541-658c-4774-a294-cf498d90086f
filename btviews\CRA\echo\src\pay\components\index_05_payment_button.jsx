import React from "react";
import Loader from "./loader";
import { FaCircleCheck } from "react-icons/fa6";
import DOMPurify from 'dompurify';


const PaymentButton = ({
  selectedMethod,
  onClick,
  isLoading,
  isSelected,
  buttonClass,
  method,
  htmlFor,
  label,
  src,
  alt,
}) => (
  <button
    className={`relative bg-white rounded-2xl hover:lg:bg-gray-200 ${buttonClass} overflow-hidden border-2 flex flex-col items-center justify-center aspect-square w-[102px] h-[102px] xl:w-[144px] xl:h-[144px] p-2 xl:p-0 ${
      isSelected && !isLoading
        ? "text-blue-500 border-blue-500 hover:lg:bg-white"
        : "text-gray-500"
    }`}
    onClick={onClick}
    disabled={isLoading}
  >
    {selectedMethod === method && isLoading && (
      <>
        <div role="status" className="relative">
          <div className="absolute right-[-100px] top-[-20px] w-44 h-44 z-10 bg-black/50"></div>
          <div className="absolute top-[37px] right-[-20px] z-10">
            <Loader />
          </div>
        </div>
      </>
    )}
    {selectedMethod === method && !isLoading && (
      <FaCircleCheck className="absolute top-[7px] right-[7px] w-4 h-4 z-10 text-blue-500" />
    )}
    <div className="payment-container w-full h-full flex justify-center items-center">
      <div className="relative w-full flex flex-col items-center">
        <img
          src={src}
          alt={alt}
          className="block mx-auto w-auto h-8 mb-2 xl:mb-4"
          loading="lazy"
        />
        <label
          htmlFor={htmlFor}
          id="other-label"
          className="text-center font-medium text-xs cursor-pointer xl:[&_br]:hidden"
          dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(label) }}
        >
        </label>
      </div>
    </div>
  </button>
);

export default PaymentButton;