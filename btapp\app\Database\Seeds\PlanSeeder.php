<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class PlanSeeder extends Seeder
{

	protected $planColumn = [
		"plan_id",
		"plan_pid",
		"plan_name",
		"plan_type",
		"details",
		"label",
		"ppg",
		"recurly_plan_id",
		"stripe_plan_id",
		"paddle_plan_id",
		"fs_plan_id",
		"trial_days",
		"payment_interval",
		"price",
		"trial_price",
		"currency",
		"currency_symbol",
		"country",
		"sort_order",
		"price_per_member",
		"max_members",
		"max_tokens",
		"display_txt1",
		"display_txt2",
		"display_txt3",
		"display_txt4",
		"plan_description",
		"options",
		"locales",
		"created_at",
		"updated_at",
	];

	protected $basic_pricing_description = "Access to Basic Models:
		<div class='indent'>GPT-4o</div>
		<div class='indent'>DeepSeek V3</div>
		<div class='hover-target indent'>Claude 3.5: 25k tokens/month
			<span class='description-tooltip'>Approximately 1,000 sentences.</span>
		</div>
		<div class='indent'>DALL-E 3: 20 Images/Month</div>
		<div class='indent'>Flux: 15 Images/Month</div>
		<div class='indent'>LLaMA3-8B</div>
		<div class='indent'>Gemma-7B</div>
		<br>
		<div class='hover-target'>Standard Context Memory
			<span class='description-tooltip'>This is how much the AI model will remember within one conversation.</span>
		</div>
		<div class='hover-target'>Standard Dialogue Limit
			<span class='description-tooltip'>This is the maximum total length the AI model can output for a monthly period. Approximately 500,000 tokens.</span>
		</div>";

	protected $pro_pricing_description = "Access to Advanced Models:
		<div class='indent'>GPT-4o</div>
		<div class='indent'>DeepSeek V3</div>
		<div class='hover-target indent'>DeepSeek R1: 50k tokens/month
			<span class='description-tooltip'>Approximately 2,000 sentences.</span>
		</div>
		<div class='indent newBadge'>Grok AI✨
			<span style='background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;'>NEW</span>
		</div>
		<div class='hover-target indent'>Claude 3.7: 50k tokens/month
			<span class='description-tooltip'>Approximately 2,000 sentences.</span>
		</div>
		<div class='indent'>DALL-E 3: 50 images/month</div>
		<div class='indent'>Flux: 30 images/month</div>
		<div class='indent'>LLaMA3-70B</div>
		<div class='indent'>Gemini Pro</div>
		<br>
		<div class='hover-target'>Full Context Memory
			<span class='description-tooltip'>This is how much the AI model will remember within one conversation.</span>
		</div>
		<div class='hover-target'>Full Dialogue Limit
			<span class='description-tooltip'>This is the maximum total length the AI model can output for a monthly period. Approximately 1,000,000 tokens.</span>
		</div>
		<div class='hover-target'>
			Full Access to Advanced Tools
			<span class='description-tooltip'>
			Access to over a dozen specialized AI tools, including a text-to-image generator, a document analyzer, an AI-enhanced search engine, etc.
			</span>
		</div>";

	protected $promax_pricing_description = "Access to Premium Models:
		<div class='indent'>GPT-4o</div>
		<div class='indent'>GPT-4</div>
		<div class='indent'>OpenAI o1</div>
		<div class='indent newBadge'>OpenAI o3 mini✨
			<span style='background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;'>NEW</span>
		</div>
		<div class='indent'>DeepSeek V3</div>
		<div class='indent'>DeepSeek R1</div>
		<div class='indent newBadge'>Grok AI✨
			<span style='background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;'>NEW</span>
		</div>
		<div class='indent newBadge'>Claude 3.7✨
			<span style='background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;'>NEW</span>
		</div>
		<div class='indent'>DALL-E 3</div>
		<div class='indent'>Flux: 160 images/month</div>
		<div class='indent'>LLaMA3-70B</div>
		<div class='indent'>Gemini Pro</div>
		<div class='indent'>(10+ other Models)</div>
		<br>
		<div class='hover-target'>Advanced Context Memory
			<span class='description-tooltip'>
				This is how much the AI model will remember within one conversation.
			</span>
		</div>
		<div class='hover-target'>Advanced Dialogue Limit
			<span class='description-tooltip'>
				This is the maximum total length the AI model can output for a monthly period.
			</span>
		</div>
		<div class='hover-target'>
			Full Access to Advanced Tools
			<span class='description-tooltip'>
				Access to over a dozen specialized AI tools, including a text-to-image generator, a document analyzer, an AI-enhanced search engine, etc.
			</span>
		</div>";

	protected $enterprise_pricing_description = "Access to Advanced Models:
		<div class='indent'>GPT-4o mini</div>
		<div class='indent'>GPT-4o</div>
		<div class='indent'>LLaMA3-70B</div>
		<div class='indent'>Gemini Pro</div>
		<div class='indent'>(10+ other LLM Models)</div>
		<br />
		<div class='hover-target'>Full Context Memory
			<span class='description-tooltip'>
				This is how much the AI model will remember within one conversation. Approximately 8,196 tokens.
			</span>
		</div>
		<div class='hover-target'>Full Dialogue Limit
			<span class='description-tooltip'>
				This is the maximum total length the AI model can output for a monthly period. Approximately 500,000 tokens.
			</span>
		</div>
		<div>Unlimited Chat History Saves</div>
		<div>Prompt Templates</div>
		<div>Image File Upload</div>
		<br>
		Advanced Tools:
		<div class='hover-target'>Full Access to Advanced Tools
			<span class='description-tooltip'>
				Access to over a dozen specialized AI tools, including a text-to-image generator, a document analyzer, an AI-enhanced search engine, etc.
			</span>
		</div>";

	protected $pro_single_pricing_description = "<div class='description'>
			<div class='hover-target'>
				<p class='font-medium text-[#2872FA]'>Limited access to the latest language models:</p>
				<span class='description-tooltip'>
					500,000 tokens per month
				</span>
			</div>
			<div class='mt-[5px] pl-2.5'>
				<p>GPT-4o mini, GPT-4o</p>
				<p>LLaMA3-8B</p>
				<p>Gemini Pro</p>
			</div>
		</div>
		<div class='description'>
			<div class='hover-target'>
				<p class='font-medium text-[#2872FA]'>Limited access to AI apps</p>
				<div class='description-tooltip flex-row gap-2'>
					<div class='w-1/2'>
						<p class='font-medium mb-1'>Language apps</p>
						<div>
							<p>ChatPDF </p>
							<p>Grammar AI </p>
							<p>Teacher AI </p>
							<p>Search AI </p>
							<p>Multi-Chat </p>
							<p>SiteBot</p>
							<p>Coding AI </p>
							<p>Homework Help</p>
						</div>
					</div>
					<div class='w-1/2'>
						<p class='font-medium mb-1'>Image apps</p>
						<div>
							<p>DreamPhoto</p>
							<p>Interior AI</p>
							<p>Restore Photo</p>
							<p>Remove Background</p>
							<p>Avatar Maker</p>
							<p>Storybook</p>
						</div>
					</div>
				</div>
			</div>
		</div>";

	protected $checkIcon = "<svg stroke='currentColor' fill='currentColor' stroke-width='0' viewBox='0 0 512 512' class='checkIcon flex-none inline text-xs mr-3 text-[#7fafff]' height='1em' width='1em' xmlns='http://www.w3.org/2000/svg'>
			<path d='M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z'></path>
		</svg>";

	protected function getMaxPricingDescription($prompt){
		return "<div class='description'>
			<p class='font-medium text-[#2872FA]'>Unlimited access to the latest language and image models:</p>
			<div class='mt-[5px] pl-2.5'>
				<p>GPT-4, GPT-4o, GPT-4o mini</p>
				<p>Claude 3.7 Sonnet</p>
				<p>Llama 3.1</p>
				<p>Gemini Pro</p>
				<p>DALL-E</p>
				<div class='hover-target'>
					<p>Plus 6 more models</p>
					<div class='description-tooltip flex-row gap-2'>
						<div>
							<p>Gemma-2 </p>
							<p>Mistral </p>
							<p>DeepSeek </p>
							<p>DBRX </p>
							<p>Qwen </p>
							<p>Mixtral </p>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class='description'>
			<p class='font-medium text-[#2872FA]'>" . $prompt . " prompts per user for advanced image and reasoning models:</p>
			<div class='mt-[5px] pl-2.5'>
				<p>Flux.1 Pro</p>
				<p>OpenAI o1-preview</p>
				<p>OpenAI o1-mini</p>
			</div>
		</div>
		<div class='description'>
			<div class='hover-target'>
				<p class='font-medium text-[#2872FA]'>Unlimited access to AI apps</p>
				<div class='description-tooltip flex-row gap-2'>
					<div class='w-1/2'>
						<p class='font-medium mb-1'>Language apps</p>
						<div>
							<p>ChatPDF </p>
							<p>Grammar AI </p>
							<p>Teacher AI </p>
							<p>Search AI </p>
							<p>Multi-Chat </p>
							<p>SiteBot</p>
							<p>Coding AI </p>
							<p>Homework Help</p>
						</div>
					</div>
					<div class='w-1/2'>
						<p class='font-medium mb-1'>Image apps</p>
						<div>
							<p>DreamPhoto</p>
							<p>Interior AI</p>
							<p>Restore Photo</p>
							<p>Remove Background</p>
							<p>Avatar Maker</p>
							<p>Storybook</p>
						</div>
					</div>
				</div>
			</div>
		</div>";
	}

	protected function getMaxPricingDescription2($users, $prompt)
	{
		return "  
					<div class='flex'>" . $this->checkIcon . "
						<p>Up to " . $users . " users with shared prompt limits</p>
					</div>
					<div class='flex'>" . $this->checkIcon . "
						<p>Unlimited access to the latest language and image models:
							GPT-4, GPT-4o, GPT-4o mini, Claude 3.7 Sonnet, Llama 3.1, 
							Gemini Pro, DALL-E, plus 6 more models</p>
					</div>
					<div class='flex'>" . $this->checkIcon . "
						<p>" . $prompt . " prompts per user for advanced image models: Flux.1 Pro</p>
					</div>
					<div class='flex'>" . $this->checkIcon . "
						<p>" . $prompt . " prompts per user for advanced reasoning models: OpenAI o1-preview and OpenAI o1-mini</p>
					</div>
					<div class='flex'>" . $this->checkIcon . "
						<p>Unlimited access to AI apps</p>
					</div>
					";
	}

	protected function getBasicData() {
		$basic_data = "<div>ChatGPT-powered tools</div>";
		$basic_data .= "<div>4,096 context window</div>";
		$basic_data .= "<div>250,000 Token Cap - comprehensive and exhaustive responses</div>";
		$basic_data .= "<div>Customizable Response Models - can provide scenario-based responses <small>(ex. I am a college professor. Write me a lecture about...)</small></div>";
		$basic_data .= "<div>Save Chat History - store up to hundreds of research results accessible any time</div>";
		$basic_data .= "<div>Choose Light or Dark Mode - for all screen types</div>";
		$basic_data .= "<div>Export Conversations - Image, Text, CSV or JSON files</div>";
		$basic_data .= "<div>AI Art Maker - generate art based on prompts</div>";
		$basic_data .= "<div>Live Support</div>";

		return $basic_data;
	}

	protected function getProMaxData() {
		$promax_data = "<div>ChatGPT-powered tools</div>";
		$promax_data .= "<div>128,000 context window</div>";
		$promax_data .= "<div>No Response Cap - exhaustive response; no word count limit</div>";
		$promax_data .= "<div>Customizable Response Models - can provide scenario-based responses <small>(ex. I am a college professor. Write me a lecture about...)</small></div>";
		$promax_data .= "<div>Save Chat History - store up to hundreds of research results accessible any time</div>";
		$promax_data .= "<div>Choose Light or Dark Mode - for all screen types</div>";
		$promax_data .= "<div>Export Conversations - Image, Text, CSV or JSON files</div>";
		$promax_data .= "<div>AI Art Maker - generate art based on prompts</div>";
		$promax_data .= "<div>Live Support</div>";
		$promax_data .= "<div>ChatGPT-4 model</div>";
		$promax_data .= "<div>Chatbot Themes - change your background color</div>";
		$promax_data .= "<div>ChatPDF - ask questions based on your PDF document</div>";
		$promax_data .= "<div>Interior AI - create new room designs easily</div>";
		$promax_data .= "<div>Stable Diffusion-powered tools to create realistic images</div>";
		$promax_data .= "<div>AI Art Prompt Gallery</div>";

		return $promax_data;
	}

	protected function ppgWithAdvancePlanDescription($plan_type) {
        $token_limit = '';
        $pro_chatbot_access = 'Access to all Pro Chatbots,<br/><span class="italic">plus</span><br/>';
        $models = '';
        $limited_access = '';

        switch ($plan_type) {
            case 'Pro':
                $token_limit = '1,000,000 tokens per month<br/><span class="text-xs">(approximately 40,000 sentences)</span><br/>';
                $pro_chatbot_access = 'Access to Pro Chatbots:<br/>';

                $models = [
					'<span class="advanced-models"></span>GPT-4o',
					'<span class="advanced-models"></span>DeepSeek V3',
					'<span class="advanced-models"></span>Grok AI✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEW</span>',
					'<span class="advanced-models"></span>LLaMA3-70B',
					'<span class="advanced-models"></span>Gemini Pro',
                ];

                $limited_access = [
                    '<span class="advanced-models"></span>Claude 3.7: 50k tokens/month<br/><span class="text-xs">(approximately 2,000 sentences)</span>',
                    '<span class="advanced-models"></span>DALL-E 3: 50 Images/month',
                    '<span class="advanced-models"></span>Flux AI: 30 Images/month',
                ];
                break;
            case 'Advanced':
                $token_limit = '2,500,000 tokens per month<br/><span class="text-xs">(approximately 100,000 sentences)</span><br/>';
                $pro_chatbot_access .= 'Access to Advanced Chatbots:';

                $models = [
                     '<span class="advanced-models"></span>GPT-4', '<span class="advanced-models"></span>Claude 3.5',
					 '<span class="advanced-models"></span>DeepSeek R1✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEW</span>',
                ];

                $limited_access = [
                    '<span class="advanced-models"></span>Claude 3.7: 50k tokens/month<br/><span class="text-xs">(approximately 2,000 sentences)</span>',
                    '<span class="advanced-models"></span>DALL-E 3: 80 Images/month',
                    '<span class="advanced-models"></span>Flux AI: 80 Images/month',
                ];
                break;
            case 'ProMax':
                $token_limit = 'No token limit<br/><span class="text-xs">(unlimited chatbot responses)</span><br/>';
                $pro_chatbot_access .= 'Access to all Advanced Chatbots,<br/><span class="italic">plus</span><br/>Access to Premium Chatbots:';

                $models = [
                    '<span class="advanced-models"></span>o1-mini',
					'<span class="advanced-models"></span>o3 mini✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEW</span>',
					'<span class="advanced-models"></span>Claude 3.7✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEW</span>',
                ];

                $limited_access = [
                    '<span class="advanced-models"></span>Flux AI: 160 Images/month',
                ];
                break;
        }

        if ($models) {
            $models = '<div>'. implode('</div><div>', $models) .'</div>';
        }

        if ($limited_access) {
            $limited_access = '<div>'. implode('</div><div>', $limited_access) .'</div>';
        }

        $dalle_limit = '';

        if ($plan_type === 'ProMax') {
            //$dalle_limit = '<div class="mt-7">DALL-E 3: No image limit</div>'; //refer to #35751
			$dalle_limit = '<span class="advanced-models"></span><div>DALL-E 3: No image limit</div>';
        }

		$align = ""; // alignment per qa request, refer to #35751

        $format = '%s <br/> %s %s %s
        Limited Access to:
        %s<br/>%s
        <span class="hover-target" style="display: block;">
            Exclusive AI Apps
            <span class="description-tooltip">
                Access to over a dozen specialized AI tools, including a text-to-image generator, a document analyzer, an AI-enhanced search engine, etc.
            </span>
        </span>';

        $description = sprintf($format, $token_limit, $pro_chatbot_access, $models, $dalle_limit, $limited_access, $align);

        return $description;
    }

	protected function getPolishLocale($label, $plan_type, $price_text, $trial_options = [], $plan_id = null) {
		$access_to = '';
		$models = ['GPT-4o mini', 'GPT-4o'];
		$context_memory = '';
		$context_memory_tooltip = 'To, ile model AI zapamięta w ramach jednej rozmowy.';
		$dialogue_limit = '';
		$dialogue_limit_tooltip = '';
		$unlimited_chat_history =  'Nieograniczone zapisy historii czatów';
		$deepseek = [];
		switch ($plan_type) {
			case 'Basic':
				$access_to = 'Dostęp do podstawowych modeli:';

				$models = array_merge($models, [
					'DeepSeek V3' => [
						'new_badge' => true,
					],
					'LLaMA3-8B',
					'Gemma-7B',
					'Claude 3.5: 25&nbsp;000 tokenów/miesiąc' => [
						'tooltip' => 'Około 1 000 zdań.',
					],
					'DALL-E 3: 20 obrazów/miesiąc',
					'Flux: 15 obrazów/miesiąc',
				]);

				$context_memory = 'Standardowa pamięć kontekstowa';
				$dialogue_limit = 'Standardowy limit dialogu';
				$dialogue_limit_tooltip = 'Jest to maksymalna łączna długość, jaką model AI może wygenerować w ciągu miesiąca. Około 500&nbsp;000 tokenów.';
			break;
			case 'Pro':
				$access_to = 'Dostęp do zaawansowanych modeli:';
				if ($plan_id == '106' || $plan_id == '4') {
					$models = ['GPT-4o'];

						$models = array_merge($models,[ 
							'DeepSeek V3',
							'DeepSeek R1: 50k tokenów/miesiąc' => [
								'tooltip' => 'Około 2 000 zdań.',
							],
							'Grok AI' => [
								'new_badge' => true,
							],
							'Claude 3.7: 50k tokenów/miesiąc' => [
								'tooltip' => 'Około 2 000 zdań.',
							],
							'DALL-E 3: 50 obrazów/miesiąc',
							'Flux: 30 obrazów/miesiąc',
							'LLaMA3-70B',
							'Gemini Pro',
						]);
						
				} else {
						$models = array_merge($models, [
							'LLaMA3-70B',
							'Gemini Pro',
							'Claude 3.7: 50&nbsp;000 tokenów/miesiąc' => [
								'tooltip' => 'Około 2 000 zdań.',
							],
							'DALL-E 3: 50 obrazów/miesiąc',
							'Flux: 30 obrazów/miesiąc',
						]);
						$context_memory = 'Pełna pamięć kontekstowa';
						$dialogue_limit = 'Pełny limit dialogu';
						$dialogue_limit_tooltip = 'To maksymalna łączna długość, jaką model AI może wygenerować w ciągu miesiąca. Około 1&nbsp;000&nbsp;000 tokenów.';
				}

			break;
			case 'ProMax':
				$access_to = 'Dostęp do modeli premium:';
				if($plan_id == '106') {
					$deepseek = [
						'DeepSeek V3',
							'DeepSeek R1' => [
								'new_badge' => true,
						],
					];
				} else {
						$deepseek = [];
				}
				$models = array_merge($models, [
					'GPT-4',
					'o1-preview' => [
						'new_badge' => true,
					],
					'o1-mini' => [
						'new_badge' => true,
					],
					'Flux: 160 obrazów/miesiąc',
					'LLaMA3-70B',
					'Gemini Pro',
					], $deepseek,[
					'Grok AI' => [
						'new_badge' => true,
					],
					'Claude 3.7' => [
						'new_badge' => true,
					],
					'DALL-E 3',
					'(10+ other Models)',
				]);

				$context_memory = 'Zaawansowana pamięć kontekstowa';
				$dialogue_limit = 'Zaawansowany limit dialogu';
				$dialogue_limit_tooltip = 'To jest maksymalna całkowita długość, jaką model AI może wygenerować w okresie miesięcznym.';
			break;
		}

		$current_models = '';

		foreach ($models as $key => $model) {
			$additional_class = '';
			$content = $model;

			if (is_array($content)) {
				if (array_key_exists('tooltip', $content)) {
					$additional_class = 'hover-target';

					$content = sprintf(
						"%s<span class=\"description-tooltip\">%s</span>", 
						$key,
						$content['tooltip'],
					);
				} else if (array_key_exists('new_badge', $content)) {
					$additional_class = 'newBadge';
					$new_label = 'NOWOŚĆ';

					$content = sprintf(
						"%s✨<span style=\"background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;\">%s</span>", 
						$key,
						$new_label,
					);
				}
			}

			$current_models .= "<div class=\"indent $additional_class\">$content</div>";
		}

		if ($plan_type === 'ProMax') {
			$image_file_upload = '';
		} else {
			if ($plan_id !== '106') {
				$image_file_upload = 'Przesyłanie plików z obrazami';
				$image_file_upload = sprintf('<div>%s</div>',
					$image_file_upload
				);
			}
			$image_file_upload = '';
		}

		if ($plan_type === 'Basic') {
			$advance_tools_text = 'Zaawansowane narzędzia:';
			$advance_tools = 'Generator tekstu na obraz tylko';

			$advance_tools_access = sprintf(
				'<br/> %s <div>%s</div>',
				$advance_tools_text,
				$advance_tools,
			);
		} else {

			if ($plan_id == '106' || $plan_id == '4') {
				$unlimited_chat_history = '';
				$advance_tools_access = '';

				$pro_access_details = [
					'Pełna pamięć kontekstowa' => 'To, ile model AI zapamięta w ramach jednej rozmowy.',
					'Pełny limit dialogu' => 'Jest to maksymalna łączna długość, jaką model AI może wygenerować w ciągu miesiąca. Około 1 000 000 tokenów.',
					'Pełny dostęp do zaawansowanych narzędzi' => 'Dostęp do ponad tuzina specjalistycznych narzędzi AI, w tym generatora tekstu na obraz, analizatora dokumentów, ulepszonej wyszukiwarki AI i innych.',
					/*'14-dniowy okres próbny, a potem tylko 95zł miesięcznie' => ''*/
				];

				foreach ($pro_access_details as $detail => $tooltip) {
						if (!empty($tooltip)) {
								$advance_tools_access .= sprintf(
										'<div class="hover-target">%s <span class="description-tooltip">%s</span></div>',
										$detail,
										$tooltip
								);
						} else {
								$advance_tools_access .= sprintf('<div>%s</div>', $detail);
						}
				}
				
			}else{
				$advance_tools_access = 'Pełny dostęp do zaawansowanych narzędzi';
				$advance_tools_access_tooltip = 'Dostęp do ponad tuzina specjalistycznych narzędzi AI, w tym generatora tekstu na obraz, analizatora dokumentów, ulepszonej wyszukiwarki AI i innych.';
	
				$advance_tools_access = sprintf(
					'<div class="hover-target">%s <span class="description-tooltip">%s</span></div>',
					$advance_tools_access,
					$advance_tools_access_tooltip,
				);
			}
		}

		if (empty($trial_options)) {
			$trial_options = '';
		} else {
			$trial_days = $trial_options['trial_days'] . '-dniowy okres próbny';
			$then = ($plan_id === 106 || $plan_id === 4) ? 'potem' : 'następnie';

			$trial_options = sprintf(
				'<div>%s, a %s tylko %s miesięcznie</div>',
				$trial_days,
				$then,
				$trial_options['price_text'],
			);
		}

		$format = '%s
		%s
		<br/>
		';
		
		//if ($plan_id !== 106) {
		if (!in_array($plan_id, array(106, 4))) {
				$format .= '<div class="hover-target">%s <span class="description-tooltip">%s</span></div>
				<div class="hover-target">%s <span class="description-tooltip">%s</span></div>
				<div>%s</div>';
		} else { //for plan_id 106 and 4
				$format .= '
				%s
				'; //added this for trial options
		}
		
		$format .= '%s
		%s
		%s';
		

		$params = [
			$access_to,
			$current_models,
			$unlimited_chat_history,
			$image_file_upload,
			$advance_tools_access,
			$trial_options
		];

		//if ($plan_id !== 106) {
		if (!in_array($plan_id, array(106, 4))) {
			array_splice($params, 2, 0, [$context_memory, $context_memory_tooltip, $dialogue_limit, $dialogue_limit_tooltip]);
		}

		$description = sprintf($format, ...$params);


		return [
			'label' => $label,
			'price_text' => $price_text,
			'description' => trim($description),
			'button_label' => 'Kontynuuj',
		];
	}

	protected $planType = array(
		'1' => 'pro',
		'2' => 'basic',
		'3' => 'basic',
		'4' => 'pro',
		'5' => 'basic',
		'6' => 'pro',
		'7' => 'pro',
		'8' => 'basic',
		'9' => 'pro',
		'10' => 'pro',
		'11' => 'basic',
		'12' => 'pro',
		'13' => 'basic',
		'14' => 'basic',
		'15' => 'basic',
		'16' => 'pro',
		'17' => 'pro',
		'18' => 'basic',
		'19' => 'basic',
		'20' => 'pro',
		'23' => 'basic',
		'24' => 'basic',
		'25' => 'basic',
		'26' => 'basic',
		'27' => 'basic',
		'28' => 'basic',
		'29' => 'basic',
		'30' => 'pro',
		'31' => 'basic',
		'32' => 'pro',
		'33' => 'basic',
		'34' => 'pro',
		'35' => 'basic',
		'36' => 'pro',
		'37' => 'basic',
		'38' => 'pro',
		'39' => 'basic',
		'40' => 'pro',
		'41' => 'basic',
		'42' => 'pro',
		'43' => 'basic',
		'44' => 'pro',
		'45' => 'basic',
		'46' => 'pro',
		'47' => 'basic',
		'48' => 'pro',
		'49' => 'pro',
		'50' => 'basic',
		'51' => 'basic',
		'52' => 'basic',
		'53' => 'basic',
		'54' => 'basic',
		'55' => 'basic',
		'56' => 'pro',
		'57' => 'basic',
		'58' => 'basic',
		'59' => 'pro',
		'60' => 'basic',
		'61' => 'pro',
		'62' => 'enterprise',
		'63' => 'basic',
		'64' => 'pro',
		'65' => 'basic',
		'66' => 'pro',
		'67' => 'promax',
		'68' => 'basic',
		'69' => 'pro',
		'70' => 'pro',
		'71' => 'pro',
		'72' => 'pro',
		'73' => 'promax',
		'74' => 'pro',
		'75' => 'promax annual',
		'76' => 'promax',
		'77' => 'promax annual',
		'78' => 'promax',
		'79' => 'promax annual',
		'80' => 'promax',
		'81' => 'promax annual',
		'82' => 'promax',
		'83' => 'promax annual',
		'84' => 'promax',
		'85' => 'promax annual',
		'86' => 'pro',
		'87' => 'pro',
		'88' => 'basic',
		'89' => 'pro',
		'90' => 'pro',
		'91' => 'basic',
		'92' => 'pro',
		'93' => 'basic',
		'94' => 'basic',
		'95' => 'pro',
		'96' => 'promax',
		'97' => 'promax',
		'98' => 'basic',
		'99' => 'basic',
		'100' => 'pro',
		'101' => 'promax',
		'102' => 'promax',
		'103' => 'promax',
		'104' => 'promax annual',
		'105' => 'enterprise',
		'106' => 'pro',
		'107' => 'basic',
		'108' => 'pro',
		'109' => 'pro',
		'110' => 'promax',
		'111' => 'basic',
		'112' => 'pro',
		'113' => 'basic',
		'114' => 'pro',
		'115' => 'pro',
		'116' => 'promax',
		'117' => 'basic',
		'118' => 'pro',
		'119' => 'basic',
		'120' => 'pro',
		'121' => 'pro',
		'122' => 'promax',
		'123' => 'basic',
		'124' => 'pro',
		'125' => 'basic',
		'126' => 'pro',
		'127' => 'pro',
		'128' => 'promax',
		'129' => 'basic',
		'130' => 'pro',
		'131' => 'basic',
		'132' => 'pro',
		'133' => 'pro',
		'134' => 'promax',
		'135' => 'basic',
		'136' => 'pro',
		'137' => 'basic',
		'138' => 'pro',
		'139' => 'pro',
		'140' => 'promax',
		'141' => 'basic',
		'142' => 'pro',
		'143' => 'basic',
		'144' => 'pro',
		'145' => 'promax',
		'146' => 'basic',
		'147' => 'pro',
		'148' => 'promax annual',
		'149' => 'basic',
		'150' => 'basic',
		'151' => 'pro',
		'152' => 'promax',
		'153' => 'promax',
		'154' => 'basic',
		'155' => 'pro',
		'156' => 'promax',
		'157' => 'basic',
		'158' => 'pro',
		'159' => 'promax annual',
		'160' => 'basic',
		'161' => 'pro',
		'162' => 'promax',
		'163' => 'basic',
		'164' => 'pro',
		'165' => 'promax annual',
		'166' => 'basic',
		'167' => 'pro',
		'168' => 'promax',
		'169' => 'basic',
		'170' => 'pro',
		'171' => 'promax annual',
		'172' => 'pro',
		'173' => 'basic',
		'174' => 'pro',
		'175' => 'pro',
		'176' => 'promax',
		'177' => 'basic',
		'178' => 'pro',
		'179' => 'promax',
		'180' => 'basic',
		'181' => 'pro',
		'182' => 'promax annual',
		'183' => 'basic',
		'184' => 'pro single',
		'185' => 'team max',
		'186' => 'office max',
		'187' => 'enterprise max',
		'188' => 'basic',
		'189' => 'pro',
		'190' => 'basic',
		'191' => 'pro',
		'192' => 'basic',
		'193' => 'pro',
		'194' => 'basic',
		'195' => 'pro',
		'196' => 'promax',
		'197' => 'pro',
		'198' => 'advanced',
		'199' => 'promax',
		'200' => 'pro',
		'201' => 'basic',
		'202' => 'pro',
		'203' => 'pro',
		'204' => 'pro',
		'205' => 'pro',
		'206' => 'pro',
		'207' => 'pro',
		'208' => 'promax',
		'209' => 'pro',
	);

	protected function getLocales($plan_id){
		if ($plan_id==4){ //ppg 03
			return json_encode([
        "de" => [
        "label" => "14-tägige Testversion",
        "price_text" => "$2.85",
        "description" => '
					Zugang zu fortgeschrittenen Modellen:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5: 50K Tokens/Monat
							<span class="description-tooltip">
									Ungefähr 2.000 Sätze.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 Bilder/Monat</div>
					<div class="indent newBadge">Flux: 30 Bilder/Monat✨
						<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEU</span>
					</div>
					<br>
					<div class="hover-target">
							Voller Kontextspeicher
							<span class="description-tooltip">
									Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.
							</span>
					</div>
					<div class="hover-target">
							Vollständiges Dialoglimit
							<span class="description-tooltip">
									Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 1.000.000 Tokens.
							</span>
					</div>
					<div>Unbegrenzte Chatverlauf-Speicherung</div>
					<div>Bild-Datei-Upload</div>
					<div class="hover-target">
							Vollständiger Zugriff auf erweiterte Tools
							<span class="description-tooltip">
									Zugriff auf über ein Dutzend spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KIunterstützte Suchmaschine usw
							</span>
					</div>
					<div>14 Tage Testversion, danach nur 23,85&nbsp;$ pro Monat.</div>
				
				',
        "button_label" => "Fortfahren"
        ],
        "es" => [
        "label" => "14 Días de Prueba",
        "price_text" => "$2.85",
        "description" => '
					Acceso a modelos avanzados:
					<div class="indent">GPT-4o</div>
					<div class="indent newBadge">DeepSeek V3✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NUEVO</span></div>
					<div class="hover-target indent">
							DeepSeek R1: 50k tokens/mes
							<span class="description-tooltip">
									Aproximadamente 2,000 oraciones.
							</span>
					</div>
					<div class="hover-target indent">
							Claude 3.7: 50K tokens/mes
							<span class="description-tooltip">
									Aproximadamente 2,000 oraciones.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 imágenes/mes</div>
					<div class="indent">Flux: 30 imágenes/mes</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<br>
					<div class="hover-target">
							Memoria de contexto completo
							<span class="description-tooltip">
									Esto es lo que el modelo de IA recordará dentro de una conversación.
							</span>
					</div>
					<div class="hover-target">
							Límite completo de diálogo
							<span class="description-tooltip">
									Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1,000,000 tokens.
							</span>
					</div>
					<div class="hover-target">
							Acceso completo a herramientas avanzadas.
							<span class="description-tooltip">
									Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.
							</span>
					</div>
					<div>Prueba de 14 días, luego solo $23.85 por mes.</div>
				',
        "button_label" => "Continuar"
        ],
        "fr" => [
        "label" => "Essai de 14 jours",
        "price_text" => "$2.85",
        "description" => '
					Accès aux Modèles Avancés:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5 : 50 000 jetons/mois
							<span class="description-tooltip">
									Environ 2 000 phrases
							</span>
					</div>
					<div class="indent">DALL-E 3 : 50 images/mois</div>
					<div class="indent newBadge">Flux : 30 images/mois✨
					<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOUVEAU</span>
					</div>
					<br>
					<div class="hover-target">
							Mémoire de Contexte Complète
							<span class="description-tooltip">
									Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation
							</span>
					</div>
					<div class="hover-target">
							Limite Complète de Dialogue
							<span class="description-tooltip">
									Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 1 000 000 jetons.
							</span>
					</div>
					<div>Sauvegardes illimitées de l\'historique de chat</div>
					<div>Téléchargement de fichiers image</div>
					<div class="hover-target">
							Accès complet aux outils avancés
							<span class="description-tooltip">
									Accès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc
							</span>
					</div>
					<div>Essai de 14 jours, puis seulement 23,85&nbsp;$ par mois.</div>
				',
        "button_label" => "Continuer"
        ],
				"it" => [
        "label" => "Prova di 14 giorni",
        "price_text" => "€2.85",
        "description" => '
					Accesso ai modelli avanzati:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5: 50K Token al mese
							<span class="description-tooltip">
									Circa 2.000 frasi.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 immagini al mese</div>
					<div class="indent">Flux: 30 immagini al mese</div>
					<br>
					<div class="hover-target">
							Memorizzazione del contesto completa
							<span class="description-tooltip">
									Indica quanto il modello di intelligenza artificiale sarà in grado di ricordare all\'interno di una singola conversazione.
							</span>
					</div>
					<div class="hover-target">
							Limite totale di dialogo
							<span class="description-tooltip">
									Questa è la lunghezza massima totale che il modello di intelligenza artificiale può produrre al mese, ossia circa 1.000.000 token.
							</span>
					</div>
					<div>Salvataggi illimitati della cronologia della chat</div>
					<div>Caricamento di file immagine</div>
					<div class="hover-target">
							Accesso completo agli strumenti avanzati
							<span class="description-tooltip">
									Accesso a oltre una dozzina di strumenti di AI specializzati, tra cui un generatore di testo in immagini, un analizzatore di documenti, un motore di ricerca potenziato dall\'intelligenza artificiale, ecc.
							</span>
					</div>
					<div>Prova di 14 giorni, poi solo €&nbsp;23,85 al mese.</div>
				',
        "button_label" => "Continua"
        ],
				'tr' => [
        'label' => '14 Günlük Deneme',
        'price_text' => '$2.85',
        'description' => '
					Gelişmiş Modeller\'e Erişim:
					<div class="indent">GPT-4o</div>
					<div class="indent newBadge">DeepSeek V3✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">YENİ</span></div>
					<div class="hover-target indent">
							DeepSeek R1: Aylık 50K Token
							<span class="description-tooltip">
									Yaklaşık 2.000 cümle.
							</span>
					</div>
					<div class="hover-target indent">
							Claude 3.7: Aylık 50K Token
							<span class="description-tooltip">
									Yaklaşık 2.000 cümle.
							</span>
					</div>
					<div class="indent">DALL-E 3: Aylık 50 Görsel</div>
					<div class="indent">Flux: Aylık 30 Görsel</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<br>
					<div class="hover-target">
							Tam Bağlam Hafızası
							<span class="description-tooltip">
									Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.
							</span>
					</div>
					<div class="hover-target">
							Tam Diyalog Sınırı
							<span class="description-tooltip">
									Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.
							</span>
					</div>
					<div class="hover-target">
						Gelişmiş Araçlara Tam Erişim
							<span class="description-tooltip">
									Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.
							</span>
					</div>
					<div>14 Gün Deneme, ardından sadece ayda 23,85&nbsp;$</div>
				',
        'button_label' => 'Devam ediniz'
        ],
				'pt' => [
        'label' => 'Teste de 14 Dias',
        'price_text' => '$2.85',
        'description' => '
					Acesso aos Modelos Avançados:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5: 50K Tokens/Mês
							<span class="description-tooltip">
									Aproximadamente 2.000 frases.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 Imagens/Mês</div>
					<div class="indent newBadge">Flux: 30 Imagens/Mês✨
					<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span>
					</div>
					<br>
					<div class="hover-target">
							Memória de Contexto Completa
							<span class="description-tooltip">
									Esta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.
							</span>
					</div>
					<div class="hover-target">
							Limite Total de Diálogo
							<span class="description-tooltip">
									Este é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 1.000.000 tokens.
							</span>
					</div>
					<div>Salvamento Ilimitado do Histórico de Conversas</div>
					<div>Upload de Arquivo de Imagem</div>
					<div class="hover-target">
							Acesso Completo às Ferramentas Avançadas
							<span class="description-tooltip">
									Acesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.
							</span>
					</div>
					<div>Teste de 14 Dias, depois apenas $23,85 por mês.</div>
				',
        'button_label' => 'Continuar'
				],
				'pl' => $this->getPolishLocale('14-dniowy okres próbny', 'Pro', '$2.85', [
					'trial_days' => 14,
					'price_text' => '$23,85',
				], 4),
			]);
		}elseif($plan_id=='6'){ //PPG 05
			return json_encode([
        "de" => [
        "label" => "14-tägige Testversion",
        "price_text" => "€2.85",
        "description" => '
            		Zugang zu fortgeschrittenen Modellen:
            		<div class="indent">GPT-4o</div>
            		<div class="indent newBadge">
            		    DeepSeek V3✨
            		    <span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NEU</span>
            		</div>
            		<div class="hover-target indent">
            		    DeepSeek R1: 50k Token/Monat
            		    <span class="description-tooltip">Ungefähr 2.000 Sätze.</span>
            		</div>
            		<div class="hover-target indent">
            		    Claude 3.7: 50K Tokens/Monat
            		    <span class="description-tooltip">Ungefähr 2.000 Sätze.</span>
            		</div>
            		<div class="indent">DALL-E 3: 50 Bilder/Monat</div>
            		<div class="indent">Flux: 30 Bilder/Monat</div>
            		<div class="indent">LLaMA3-70B</div>
            		<div class="indent">Gemini Pro</div>
            		<br>
            		<div class="hover-target">
            		    Voller Kontextspeicher
            		    <span class="description-tooltip">Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.</span>
            		</div>
            		<div class="hover-target">
            		    Vollständiges Dialoglimit
            		    <span class="description-tooltip">Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 1.000.000 Tokens.</span>
            		</div>
            		<div class="hover-target">
            		    Vollständiger Zugang zu fortgeschrittenen Tools
            		    <span class="description-tooltip">Zugriff auf über ein Dutzend spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KI-unterstützte Suchmaschine usw.</span>
            		</div>
            		<div>14 Tage Testversion, danach nur 23,85 € pro Monat.</div>
				',
        "button_label" => "Fortfahren"
        ],
        "es" => [
        "label" => "14 Días de Prueba",
        "price_text" => "€2.85",
        "description" => '
            		Acceso a modelos avanzados:
            		<div class="indent">GPT-4o</div>
            		<div class="indent newBadge">
            		    DeepSeek V3✨
            		    <span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NUEVO</span>
            		</div>
            		<div class="hover-target indent">
            		    DeepSeek R1: 50k tokens/mes
            		    <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span>
            		</div>
            		<div class="hover-target indent">
            		    Claude 3.7: 50K tokens/mes
            		    <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span>
            		</div>
            		<div class="indent">DALL-E 3: 50 imágenes/mes</div>
            		<div class="indent">Flux: 30 imágenes/mes</div>
            		<div class="indent">LLaMA3-70B</div>
            		<div class="indent">Gemini Pro</div>
            		<br>
            		<div class="hover-target">
            		    Memoria de contexto completo
            		    <span class="description-tooltip">Esto es lo que el modelo de IA recordará dentro de una conversación.</span>
            		</div>
            		<div class="hover-target">
            		    Límite completo de diálogo
            		    <span class="description-tooltip">Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1,000,000 tokens.</span>
            		</div>
            		<div class="hover-target">
            		    Acceso completo a herramientas avanzadas
            		    <span class="description-tooltip">Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.</span>
            		</div>
            		<div>Prueba de 14 días, luego solo €23.85 por mes.</div>
				',
        "button_label" => "Continuar"
        ],
        "fr" => [
        "label" => "Essai de 14 jours",
        "price_text" => "€2.85",
        "description" => '
            		Accès aux Modèles Avancés:
            		<div class="indent">GPT-4o</div>
            		<div class="indent newBadge">DeepSeek V3✨
            		    <span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOUVEAU</span>
            		</div>
            		<div class="hover-target indent">
            		    DeepSeek R1: 50k jetons par mois
            		    <span class="description-tooltip">Environ 2 000 phrases.</span>
            		</div>
            		<div class="hover-target indent">
            		    Claude 3.7 : 50k jetons par mois
            		    <span class="description-tooltip">Environ 2 000 phrases.</span>
            		</div>
            		<div class="indent">DALL-E 3 : 50 images par mois</div>
            		<div class="indent">Flux : 30 images par mois</div>
            		<div class="indent">LLaMA3-70B</div>
            		<div class="indent">Gemini Pro</div>
            		<br>
            		<div class="hover-target">
            		    Mémoire de Contexte Complète
            		    <span class="description-tooltip">Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.</span>
            		</div>
            		<div class="hover-target">
            		    Limite Dialogue Complète
            		    <span class="description-tooltip">Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 1 000 000 jetons.</span>
            		</div>
            		<div class="hover-target">
            		    Accès complet aux outils avancés
            		    <span class="description-tooltip">Accès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc.</span>
            		</div>
            		<div>Essai de 14 jours, puis seulement 23,85&nbsp;€ par mois.</div>
				',
        "button_label" => "Continuer"
        ],
				"it" => [
        "label" => "Prova di 14 giorni",
        "price_text" => "€2.85",
        "description" => '
            Accesso ai modelli avanzati:
            <div class="indent">GPT-4o</div>
            <div class="indent newBadge">DeepSeek V3✨
                <span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NUOVO</span>
            </div>
            <div class="hover-target indent">
                DeepSeek R1: 50k Token al mese
                <span class="description-tooltip">Circa 2.000 frasi.</span>
            </div>
            <div class="hover-target indent">
                Claude 3.7: 50K Token al mese
                <span class="description-tooltip">Circa 2.000 frasi.</span>
            </div>
            <div class="indent">DALL-E 3: 50 immagini al mese</div>
            <div class="indent">Flux: 30 immagini al mese</div>
            <div class="indent">LLaMA3-70B</div>
            <div class="indent">Gemini Pro</div>
            <br>
            <div class="hover-target">
                Memorizzazione del contesto completa
                <span class="description-tooltip">Indica quanto il modello di intelligenza artificiale sarà in grado di ricordare all\'interno di una singola conversazione.</span>
            </div>
            <div class="hover-target">
                Limite totale di dialogo
                <span class="description-tooltip">Questa è la lunghezza massima totale che il modello di intelligenza artificiale può produrre al mese, ossia circa 1.000.000 token.</span>
            </div>
            <div class="hover-target">
                Accesso completo agli strumenti avanzati
                <span class="description-tooltip">Accesso a oltre una dozzina di strumenti di AI specializzati, tra cui un generatore di testo in immagini, un analizzatore di documenti, un motore di ricerca potenziato dall\'intelligenza artificiale, ecc.</span>
            </div>
            <div>Prova di 14 giorni, poi solo €&nbsp;23,85 al mese.</div>
		',
        "button_label" => "Continua"
        ],
				'tr' => [
        'label' => '14 Günlük Deneme',
        'price_text' => '€2.85',
        'description' => '
					Gelişmiş Modeller\'e Erişim:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5: 50K Token/Ay
							<span class="description-tooltip">
									Yaklaşık 2.000 cümle.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 Görsel/Ay</div>
					<div class="indent newBadge">Flux: 30 Görsel/Ay✨
					<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">YENİ</span>
					</div>
					<br>
					<div class="hover-target">
							Tam Bağlam Hafızası
							<span class="description-tooltip">
									Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir
							</span>
					</div>
					<div class="hover-target">
							Tam Diyalog Sınırı
							<span class="description-tooltip">
									Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.
							</span>
					</div>
					<div>Sınırsız Sohbet Geçmişi Kaydetme</div>
					<div>Görüntü Dosyası Yükleme</div>
					<div class="hover-target">
							Gelişmiş Araçlara Tam Erişim
							<span class="description-tooltip">
									Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.
							</span>
					</div>
					<div>14 Gün Deneme, ardından sadece ayda 23,85&nbsp;€</div>
				',
        'button_label' => 'Devam ediniz'
        ],
				'pt' => [
        'label' => 'Teste de 14 Dias',
        'price_text' => '€2.85',
        'description' => '
					Acesso aos Modelos Avançados:
					<div class="indent">GPT-4o mini</div>
					<div class="indent">GPT-4o</div>
					<div class="indent">LLaMA3-70B</div>
					<div class="indent">Gemini Pro</div>
					<div class="hover-target indent">
							Claude 3.5: 50K Tokens/Mês
							<span class="description-tooltip">
									Aproximadamente 2.000 frases.
							</span>
					</div>
					<div class="indent">DALL-E 3: 50 Imagens/Mês</div>
					<div class="indent newBadge">Flux: 30 Imagens/Mês✨
					<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span>
					</div>
					<br>
					<div class="hover-target">
							Memória de Contexto Completa
							<span class="description-tooltip">
									Esta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.
							</span>
					</div>
					<div class="hover-target">
							Limite Total de Diálogo
							<span class="description-tooltip">
									Este é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 1.000.000 tokens.
							</span>
					</div>
					<div>Salvamento Ilimitado do Histórico de Conversas</div>
					<div>Upload de Arquivo de Imagem</div>
					<div class="hover-target">
							Acesso Completo às Ferramentas Avançadas
							<span class="description-tooltip">
									Acesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.
							</span>
					</div>
					<div>Teste de 14 Dias, depois apenas €23,85 por mês.</div>
				',
        'button_label' => 'Continuar'
        ]
			]);
		}elseif($plan_id=='13'){ //PPG 11
			return json_encode([
					"pt" => [
						"label" => "Teste de 7 Dias",
						"price_text" => "R$5.77",
						"description" => '
        					Acesso aos Modelos Básicos:
        					<div class="indent">GPT-4o</div>
        					<div class="indent newBadge">DeepSeek V3✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span></div>
        					<div class="hover-target indent">
        					    Claude 3.5: 25K Tokens/Mês
        					    <span class="description-tooltip">Aproximadamente 1.000 frases.</span>
        					</div>
        					<div class="indent">DALL-E 3: 20 Imagens/Mês</div>
        					<div class="indent">Flux: 15 Imagens/Mês</div>
        					<div class="indent">LLaMA3-8B</div>
        					<div class="indent">Gemma-7B</div>
        					<br>
        					<div class="hover-target">
        					    Memória de Contexto Padrão
        					    <span class="description-tooltip">Esta é a quantidade de informação de que o modelo de IA se recordará numa única conversa.</span>
        					</div>
        					<div class="hover-target">
        					    Limite Padrão de Diálogo
        					    <span class="description-tooltip">Este é o total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.</span>
        					</div>
        					<div>Teste de 7 Dias, depois apenas R$49 por mês.</div>
        					<br><br>
						',
						"button_label" => "Continuar"
				]
			]);
		}elseif($plan_id=='33'){ //PPG 26 BASIC
			return json_encode([
				"de" => [
						"label" => "BASIC",
						"price_text" => "€10/Monat",
						"description" => '
							Zugang zu Basis-Modellen:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5: 25K Tokens/Monat
									<span class="description-tooltip">
											Ungefähr 1.000 Sätze.
									</span>
							</div>
							<div class="indent">DALL-E 3: 20 Bilder/Monat</div>
							<div class="indent newBadge">Flux: 15 Bilder/Monat✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEU</span>
							</div>
							<br>
							<div class="hover-target">
									Standardkontextgedächtnis
									<span class="description-tooltip">
											Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.</span>
							</div>
							<div class="hover-target">
									Standard-Dialoglimit
									<span class="description-tooltip">
											Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 500.000 Tokens.
									</span>
							</div>
							<div>Unbegrenzte Chatverlauf-Speicherung</div>
							<div>Bild-Datei-Upload</div>
							<div>Erweiterte Werkzeuge:</div>
							<div>Nur Text-zu-Bild-Generator</div>
						',
						"button_label" => "Fortfahren"
				],
				"es" => [
						"label" => "BASIC",
						"price_text" => "€10/Mes",
						"description" => '
							Acceso a modelos básicos:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5: 25K tokens/mes
									<span class="description-tooltip">
											Aproximadamente 1,000 oraciones.
									</span>
							</div>
							<div class="indent">DALL-E 3: 20 imágenes/mes</div>
							<div class="indent newBadge">Flux: 15 imágenes/mes✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NUEVO</span>
							</div>
							<br>
							<div class="hover-target">
									Memoria de Contexto Estándar
									<span class="description-tooltip">
											Esto es lo que el modelo de IA recordará dentro de una conversación.
									</span>
							</div>
							<div class="hover-target">
									Límite estándar de diálogo
									<span class="description-tooltip">
											Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 500,000 tokens.
									</span>
							</div>
							<div>Guardado ilimitado del historial de chat</div>
							<div>Subida de archivos de imagen</div>
							<br>
							<div>Herramientas avanzadas:</div>
							<div>Solo generador de texto a imagen</div><br>
						',
						"button_label" => "Continuar"
				],
				"fr" => [
						"label" => "BASIC",
						"price_text" => "€10/Mois",
						"description" => '
							Accès aux Modèles de Base:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5 : 25 000 jetons/mois
									<span class="description-tooltip">
											Environ 1 000 phrases.
									</span>
							</div>
							<div class="indent">DALL-E 3 : 20 images/mois</div>
							<div class="indent newBadge">Flux : 15 images/mois✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOUVEAU</span>
							</div>
							<br>
							<div class="hover-target">
									Mémoire de Contexte Standard
									<span class="description-tooltip">
											Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.
									</span>
							</div>
							<div class="hover-target">
									Limite de Dialogue Standard
									<span class="description-tooltip">
											Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 500 000 jetons.
									</span>
							</div>
							<div>Sauvegardes illimitées de l\'historique de chat</div>
							<div>Téléchargement de fichier image</div>
							<br>
							<div>Outils Avancés:</div>
							<div>Générateur de texte en image uniquement</div>
						',
						"button_label" => "Continuer"
				],
				'pt' => [
						'label' => 'BASIC',
						'price_text' => '€10/Mês',
						'description' => '
							Acesso aos Modelos Básicos:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5: 25K Tokens/Mês
									<span class="description-tooltip">
											Aproximadamente 1.000 frases.
									</span>
							</div>
							<div class="indent">DALL-E 3: 20 Imagens/Mês</div>
							<div class="indent newBadge">Flux: 15 Imagens/Mês✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span>
							</div>
							<br>
							<div class="hover-target">
									Memória de Contexto Padrão
									<span class="description-tooltip">
											Esta é a quantidade de informação de que o modelo de IA se recordará numa única conversa.
									</span>
							</div>
							<div class="hover-target">
									Limite Padrão de Diálogo
									<span class="description-tooltip">
											Este é o total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.
									</span>
							</div>
							<div>Arquivo Ilimitado do Histórico de Conversas</div>
							<div>Upload de Arquivo de Imagem</div>
							<br>
							<div>Ferramentas Avançadas:</div>
							<div>Gerador de Texto para Imagem</div><br>
						',
						'button_label' => 'Continuar'
				]
			]);			
		}elseif($plan_id=='34'){ //PPG 26 PRO
			return json_encode([
				"de" => [
						"label" => "PRO",
						"price_text" => "€23.85/Monat",
						"description" => '
							Zugang zu fortgeschrittenen Modellen:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5: 50K Tokens/Monat
									<span class="description-tooltip">
											Ungefähr 2.000 Sätze.
									</span>
							</div>
							<div class="indent">DALL-E 3: 50 Bilder/Monat</div>
							<div class="indent newBadge">Flux: 30 Bilder/Monat✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NEU</span>
							</div>
							<br>
							<div class="hover-target">
									Voller Kontextspeicher
									<span class="description-tooltip">
											Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.
									</span>
							</div>
							<div class="hover-target">
									Vollständiges Dialoglimit
									<span class="description-tooltip">
											Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 1.000.000 Tokens.
									</span>
							</div>
							<div>Unbegrenzte Chatverlauf-Speicherung</div>
							<div>Bild-Datei-Upload</div>
		
							<div class="hover-target">
									Vollständiger Zugriff auf erweiterte Tools
									<span class="description-tooltip">
									Zugriff auf über ein Dutzend spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KIunterstützte Suchmaschine usw
									</span>
							</div>
						',
						"button_label" => "Fortfahren"
				],
				"es" => [
						"label" => "PRO",
						"price_text" => "€23.85/Mes",
						"description" => '
							Acceso a modelos avanzados:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
								Claude 3.5: 50K tokens/mes
								<span class="description-tooltip">
										Aproximadamente 2,000 oraciones.
								</span>
							</div>
							<div class="indent">DALL-E 3: 50 imágenes/mes</div>
							<div class="indent newBadge">Flux: 30 imágenes/mes✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NUEVO</span>
							</div>
							<br>
							<div class="hover-target">
								Memoria de contexto completo
								<span class="description-tooltip">
										Esto es lo que el modelo de IA recordará dentro de una conversación.</span>
							</div>
							<div class="hover-target">
								Límite completo de diálogo
								<span class="description-tooltip">
										Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1,000,000 tokens.
								</span>
							</div>
							<div>Guardado ilimitado del historial de chat</div>
							<div>Subida de archivos de imagen</div>
							<div class="hover-target">
									Acceso completo a herramientas avanzadas.
									<span class="description-tooltip">
									Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.
									</span>
							</div>
						',
						"button_label" => "Continuar"
				],
				"fr" => [
						"label" => "PRO",
						"price_text" => "€23.85/Mois",
						"description" => '
							Accès aux Modèles Avancés:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5 : 50 000 jetons/mois
									<span class="description-tooltip">
											Environ 2 000 phrases.
									</span>
							</div>
							<div class="indent">DALL-E 3 : 50 images/mois</div>
							<div class="indent newBadge">Flux : 30 images/mois✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOUVEAU</span>
							</div>
							<br>
							<div class="hover-target">
									Mémoire de Contexte Complète
									<span class="description-tooltip">
											Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.
									</span>
							</div>
							<div class="hover-target">
									Limite Complète de Dialogue
									<span class="description-tooltip">
											Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 1 000 000 jetons.
									</span>
							</div>
							<div>Sauvegardes illimitées de l\'historique de chat</div>
							<div>Téléchargement de fichiers image</div>
		
							<div class="hover-target">
									Accès complet aux outils avancés
									<span class="description-tooltip">
									Accès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc.
									</span>
							</div>
						',
						"button_label" => "Continuer"
				],
				'pt' => [
						'label' => 'PRO',
						'price_text' => '€23.85/Mês',
						'description' => '
							Acesso aos Modelos Avançados:
							<div class="indent">GPT-4o mini</div>
							<div class="indent">GPT-4o</div>
							<div class="indent">LLaMA3-70B</div>
							<div class="indent">Gemini Pro</div>
							<div class="hover-target indent">
									Claude 3.5: 50K Tokens/Mês
									<span class="description-tooltip">
											Aproximadamente 2.000 frases.
									</span>
							</div>
							<div class="indent">DALL-E 3: 50 Imagens/Mês</div>
							<div class="indent newBadge">Flux: 30 Imagens/Mês✨
							<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span>
							</div>
							<br>
							<div class="hover-target">
									Memória de Contexto Completa
									<span class="description-tooltip">
											Esta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.
									</span>
							</div>
							<div class="hover-target">
									Limite Total de Diálogo
									<span class="description-tooltip">
											Este é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 1.000.000 tokens.
									</span>
							</div>
							<div>Salvamento Ilimitado do Histórico de Conversas</div>
							<div>Upload de Arquivo de Imagem</div>
							<div class="hover-target">
									Acesso Completo às Ferramentas Avançadas
									<span class="description-tooltip">
									Acesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.
									</span>
							</div>
						',
						'button_label' => 'Continuar'
						]
					]);			
		
		}elseif($plan_id=='15'){ //PPG 14 DEFAULT
			return json_encode([
				"es" => [
						'label' => 'BASIC',
						'price_text' => '$10/Mes',
						'description' => "
							Acceso a modelos básicos:
							<div class='indent'>GPT-4o mini</div>
							<div class='indent'>GPT-4o</div>
							<div class='indent'>LLaMA3-8B</div>
							<div class='indent'>Gemma-7B</div>
							<div class='hover-target indent'>
									Claude 3.5: 25K tokens/mes
								<span class='description-tooltip'>
											Aproximadamente 1,000 oraciones.
									</span>
							</div>
							<div class='indent'>DALL-E 3: 20 imágenes/mes</div>
							<div class='indent newBadge'>Flux: 15 imágenes/mes✨
							<span style='background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;'>NUEVO</span></div>
							<br />
				
							<div class='hover-target'>
									Memoria de Contexto Estándar
									<span class='description-tooltip'>
											Esto es lo que el modelo de IA recordará dentro de una conversación.
									</span>
							</div>
				
							<div class='hover-target'>
									Límite estándar de diálogo
									<span class='description-tooltip'>
											Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 500,000 tokens.
									</span>
							</div>
				
							<div>Guardado ilimitado del historial de chat</div>
							<div>Subida de archivos de imagen</div>
							<br>
							Herramientas avanzadas:
							<div>Solo generador de texto a imagen</div>
						",
						"button_label" => "Continuar"
				],
				'tr' => [
						'label' => 'BASIC',
						'price_text' => '$10/Ay',
						'description' => "
							Temel Modeller'e Erişim::
							<div class='indent'>GPT-4o mini</div>
							<div class='indent'>GPT-4o</div>
							<div class='indent'>LLaMA3-8B</div>
							<div class='indent'>Gemma-7B</div>
							<div class='hover-target indent'>
									Claude 3.5: 25K Token/Ay
									<span class='description-tooltip'>
											Yaklaşık 1.000 cümle
									</span>
							</div>
							<div class='indent'>DALL-E 3: 20 Görsel/Ay</div>
							<div class='indent newBadge'>Flux: 15 Görsel/Ay✨<span style='background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;'>YENİ</span></div>
							<br />
				
							<div class='hover-target'>
									Standart Bağlam Hafızası
									<span class='description-tooltip'>
											Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir
									</span>
							</div>
				
							<div class='hover-target'>
									Standart Diyalog Sınırı
									<span class='description-tooltip'>
											Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500,000 token.
									</span>
							</div>
				
							<div>Sınırsız Sohbet Geçmişi Kaydetme</div>
							<div>Görüntü Dosyası Yükleme</div>
							<br>
							Gelişmiş Araçlar:
							<div>Sadece Metinden Görüntü Üretici</div>
						",
						'button_label' => 'Devam ediniz'
				]
			]);
		}elseif($plan_id=='16'){ //PPG 14 DEFAULT
			return json_encode([
				"es" => [
					"label" => "PRO",
					"price_text" => "$23.85/Mes",
					"description" => "
					Acceso a modelos avanzados:
					<div class='indent'>GPT-4o mini</div>
					<div class='indent'>GPT-4o</div>
					<div class='indent'>LLaMA3-70B</div>
					<div class='indent'>Gemini Pro</div>
					<div class='hover-target indent'>
							Claude 3.5: 50K tokens/mes
							<span class='description-tooltip'>
									Aproximadamente 2,000 oraciones.
							</span>
					</div>
					<div class='indent'>DALL-E 3: 50 imágenes/mes</div>
					<div class='indent newBadge'>Flux: 30 imágenes/mes ✨<span style='background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;'>NUEVO</span></div>
					<br />
		
					<div class='hover-target'>
							Memoria de contexto completo
							<span class='description-tooltip'>
								Esto es lo que el modelo de IA recordará dentro de una conversación.
							</span>
					</div>
		
					<div class='hover-target'>
							Límite completo de diálogo
							<span class='description-tooltip'>
									Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1,000,000 tokens.
							</span>
					</div>
		
					<div>Guardado ilimitado del historial de chat</div>
					<div>Subida de archivos de imagen</div>
					<div class='hover-target'>
							Acceso completo a herramientas avanzadas.
							<span class='description-tooltip'>
									Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.
							</span>
					</div>
					",
					"button_label" => "Continuar"
				],
				'tr' => [
						'label' => 'PRO',
						'price_text' => '$23.85/Ay',
						'description' => "
						Gelişmiş Modeller'e Erişim:
						<div class='indent'>GPT-4o mini</div>
						<div class='indent'>GPT-4o</div>
						<div class='indent'>LLaMA3-70B</div>
						<div class='indent'>Gemini Pro</div>
						<div class='hover-target indent'>
								Claude 3.5: 50K Token/Ay
								<span class='description-tooltip'>
										Yaklaşık 2.000 cümle.
								</span>
						</div>
						<div class='indent'>DALL-E 3: 50 Görsel/Ay</div>
						<div class='indent newBadge'>Flux: 30 Görsel/Ay✨<span style='background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;'>YENİ</span></div>
						<br />
			
						<div class='hover-target'>
								Tam Bağlam Hafızası
								<span class='description-tooltip'>
									Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.
								</span>
						</div>
			
						<div class='hover-target'>
								Tam Diyalog Sınırı
								<span class='description-tooltip'>
										Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.
								</span>
						</div>
			
						<div>Sınırsız Sohbet Geçmişi Kaydetme</div>
						<div>Görüntü Dosyası Yükleme</div>
						<div class='hover-target'>
								Gelişmiş Araçlara Tam Erişim
								<span class='description-tooltip'>
										Metin'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.
								</span>
						</div>
						",
						'button_label' => 'Devam ediniz'
				]
			]);
		}
		return '';
	}

	protected function updateSCTPlan(){
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-whoqfghxjiidux' WHERE plan_id='1';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mhzrmhaezbwcol' WHERE plan_id='2';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cahwwwyihholil' WHERE plan_id='3';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tiseixycftlznq' WHERE plan_id='4';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fdumwrftfyifes' WHERE plan_id='5';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lodaxyhrgpdstp' WHERE plan_id='6';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-snfrzjtimtjyff' WHERE plan_id='7';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oabirarrrqudlt' WHERE plan_id='8';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gxbexxvnfguesa' WHERE plan_id='9';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-iuurhiidgfnqga' WHERE plan_id='10';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-eajlydurkktwud' WHERE plan_id='11';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dfqhweabyaneme' WHERE plan_id='12';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-grtgwrjgmogvzr' WHERE plan_id='13';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-swpkxhijwldmie' WHERE plan_id='14';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gqfeowfvcatzuw' WHERE plan_id='15';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qnfepdmcdhgvrn' WHERE plan_id='16';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-sppjwdcnmrbmzx' WHERE plan_id='17';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-nmjhphtrdvgpir' WHERE plan_id='18';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lifjswshngytix' WHERE plan_id='19';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oetchtfiwvxgbb' WHERE plan_id='20';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cbirkhrbylhlgf' WHERE plan_id='23';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qqboapnsqbvuoi' WHERE plan_id='24';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-wkpfhdhccvplzk' WHERE plan_id='25';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ccqsuanofmxeto' WHERE plan_id='26';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-apgvedwstuyzfq' WHERE plan_id='27';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vqgmegibmueinw' WHERE plan_id='28';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-byaihhgbgivybm' WHERE plan_id='29';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ygoiircybnugou' WHERE plan_id='30';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-djmcdstelazvfo' WHERE plan_id='31';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dzbrdyohxmmndu' WHERE plan_id='32';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qumlhcwgosgmgj' WHERE plan_id='33';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rotqaffmzsvhon' WHERE plan_id='34';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oichqonornhxfn' WHERE plan_id='35';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zjcuxfqlaqfsgf' WHERE plan_id='36';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-avlfeolgsccajm' WHERE plan_id='37';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-yjnjnopxmthrmk' WHERE plan_id='38';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-irigobcczlqbhw' WHERE plan_id='39';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-iupsgzjwhjcdiy' WHERE plan_id='40';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kkupuepdjklgqn' WHERE plan_id='41';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-slbkxhzkrjtowd' WHERE plan_id='42';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-acaogktzwwyckn' WHERE plan_id='43';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mepwntsxjaigqm' WHERE plan_id='44';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mwqysvieeknsoh' WHERE plan_id='45';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-azggwwssrnlwqy' WHERE plan_id='46';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lpsekjksmwctxr' WHERE plan_id='47';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cfgedtitiojcei' WHERE plan_id='48';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ziljagdypvxmba' WHERE plan_id='49';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vubrjvohpoxlpz' WHERE plan_id='50';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-acxvhkougnniyd' WHERE plan_id='51';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-wbvszwqzlfthop' WHERE plan_id='52';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hlwroaffzbeaok' WHERE plan_id='54';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jvttihkuosocqb' WHERE plan_id='55';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kvonfodnnqensh' WHERE plan_id='56';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pgvjalzjgoeben' WHERE plan_id='57';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-weqqjzmprsagrr' WHERE plan_id='58';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ftllhilnvrhdjs' WHERE plan_id='59';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-avpgiiwtzivybr' WHERE plan_id='60';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-luebfvmurweujp' WHERE plan_id='61';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ylxcbngszhdjay' WHERE plan_id='62';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tloaczuqloznde' WHERE plan_id='63';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ipuvpstibskpqm' WHERE plan_id='64';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rxhdylphcvksli' WHERE plan_id='65';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mnbxekqfsztrel' WHERE plan_id='66';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ugrjxuhyncpdxq' WHERE plan_id='67';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zeyxripxpuspys' WHERE plan_id='68';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rqffvkeqlprpsz' WHERE plan_id='69';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ezzdhukrwaqgnj' WHERE plan_id='70';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tvkvhrwwewkkfc' WHERE plan_id='71';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-xjkifxkksrxtfr' WHERE plan_id='72';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-yudthmzoxipnfm' WHERE plan_id='73';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pdhbpmtgqtqpnw' WHERE plan_id='74';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-aktlsmifyjfkbm' WHERE plan_id='75';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gjtylswtkfwtxj' WHERE plan_id='76';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ykjxttumssoqzn' WHERE plan_id='77';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-upfhpsawjkstwv' WHERE plan_id='78';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-uwohqvfwivhrtm' WHERE plan_id='79';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gjxnmqoelonibn' WHERE plan_id='80';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zrpqiylqizxbxk' WHERE plan_id='81';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dnyrwtbhwovivz' WHERE plan_id='82';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lyftmvkhnckhqx' WHERE plan_id='83';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-csjalvjuqyhupl' WHERE plan_id='84';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-icknjmevuehlwu' WHERE plan_id='85';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ykelxrrbhbhygq' WHERE plan_id='86';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mwwlvvoqennwrt' WHERE plan_id='87';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lgsblakdprlyrx' WHERE plan_id='88';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gejrwvbgkwgwyv' WHERE plan_id='89';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qxzysqokqfnqpf' WHERE plan_id='90';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mbdiqcomdnzhtd' WHERE plan_id='91';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hhshtonadjdigy' WHERE plan_id='92';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-sivttccazjryal' WHERE plan_id='93';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oeywcufszkpwst' WHERE plan_id='94';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ytdwmlvvuqgsig' WHERE plan_id='95';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pkoqnmjalfitet' WHERE plan_id='96';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pauwrwgjmfysnd' WHERE plan_id='97';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cfqlxwrgjyfjae' WHERE plan_id='98';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rjvkfackxxbvme' WHERE plan_id='99';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-xuhgnszofrdnvr' WHERE plan_id='100';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rpnxdngovdnmzv' WHERE plan_id='101';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ehgfwritihwjnm' WHERE plan_id='102';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ofijpqqudxnpvo' WHERE plan_id='103';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fdunqmiydpwyyj' WHERE plan_id='104';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cygvouvlbeikod' WHERE plan_id='105';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hsracgegpqdyug' WHERE plan_id='106';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bxpouytrfnypcc' WHERE plan_id='107';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pinlzrxhzdgcjk' WHERE plan_id='108';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-suanyomtkwinsy' WHERE plan_id='109';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zopmznhmdaaswk' WHERE plan_id='110';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tvkbgbrarhvcac' WHERE plan_id='111';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dcpffzkbzfsbha' WHERE plan_id='112';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zoipcnwscphvgf' WHERE plan_id='113';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jgevibmuvpiyfv' WHERE plan_id='114';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rftocpbinzseox' WHERE plan_id='115';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ocevrwjqprgevc' WHERE plan_id='116';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jacetlyhbaiefv' WHERE plan_id='117';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pyjoawdqlgzfzp' WHERE plan_id='118';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mpwcpoxmitqjba' WHERE plan_id='119';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-srlyhavbovtpna' WHERE plan_id='120';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bmhjlnkllsbmmm' WHERE plan_id='121';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ovcthocjflvbmj' WHERE plan_id='122';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-slevfrbsqtzirc' WHERE plan_id='123';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hondkmrtgkxhgy' WHERE plan_id='124';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-adyitfhptlffux' WHERE plan_id='125';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-recjlrluorqwiu' WHERE plan_id='126';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dhwatxbgtvsjdp' WHERE plan_id='127';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gfzmdahgiyxmga' WHERE plan_id='128';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hbmiqfqxpugdhd' WHERE plan_id='129';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-xbilfqznnvxwfe' WHERE plan_id='130';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jpkxtqdgycdqns' WHERE plan_id='131';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-chmfbsoazznwvq' WHERE plan_id='132';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lqzlojzhwgzoob' WHERE plan_id='133';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kpxlenyruytckr' WHERE plan_id='134';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tzhsoeqibnpyif' WHERE plan_id='135';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fmvchsgnxjkith' WHERE plan_id='136';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-guzfynilcxjaan' WHERE plan_id='137';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jyfrngetseulnn' WHERE plan_id='138';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ngqvqyftvvaatf' WHERE plan_id='139';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kujfmejahedurh' WHERE plan_id='140';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cnxgoptzcwfmno' WHERE plan_id='141';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bvguchpzjbmzts' WHERE plan_id='142';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-zcruoududtfmzj' WHERE plan_id='143';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-muhyqaszhtcnfk' WHERE plan_id='144';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oognxvdszjhqbu' WHERE plan_id='145';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vxkphvkjmwwdzd' WHERE plan_id='146';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dijcooedtgamfu' WHERE plan_id='147';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ztsokeeydjxmvr' WHERE plan_id='148';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-leadimfbcsnwon' WHERE plan_id='149';  "; $query= $this->db->query($query);
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mqszxvahhgqtqa' WHERE plan_id='150';  "; $query= $this->db->query($query);
 
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-uhybotakmzsieh' WHERE plan_id='151';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mgdyhvwvywdllq' WHERE plan_id='152';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-dzdvshcvuhwtbf' WHERE plan_id='153';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-wuwkfditimgdjd' WHERE plan_id='154';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-wmfyylcfulsirv' WHERE plan_id='155';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-sbwtlgrgbmjltu' WHERE plan_id='156';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-woqkhbzqgyyuvq' WHERE plan_id='157';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-rmpebndlsflwdm' WHERE plan_id='158';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-idihgfckgxzaxe' WHERE plan_id='159';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-sruenchwppsxss' WHERE plan_id='160';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vhckiyjdpkryou' WHERE plan_id='161';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fqlncqlflrivnb' WHERE plan_id='162';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fnzmwhoigknyyy' WHERE plan_id='163';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-miqkndpewmeone' WHERE plan_id='164';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bjlyzxayqkgpzy' WHERE plan_id='165';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-pnosisuacpvpef' WHERE plan_id='166';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-nljejeqsodxhdc' WHERE plan_id='167';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lrzloaelxwmsmu' WHERE plan_id='168';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-unvzwqfusrmtrq' WHERE plan_id='169';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kltgthljsnbszd' WHERE plan_id='170';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-plrdrvzyiaqlmx' WHERE plan_id='171';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-uuusjufbbixsgt' WHERE plan_id='172';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bujuqoaxofzell' WHERE plan_id='173';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-tiikyrifvagsbh' WHERE plan_id='174';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-fokzsdddbnaesq' WHERE plan_id='175';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qfxghayryobgao' WHERE plan_id='176';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ruydtwtfymmwnn' WHERE plan_id='177';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vnzabklilqyaif' WHERE plan_id='178';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gzbjxqiwikvvcn' WHERE plan_id='179';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-nefujxgonxwzvt' WHERE plan_id='180';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qjdikgyigelbmr' WHERE plan_id='181';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-thfbzesalowruu' WHERE plan_id='182';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-uzrubvvboqizbn' WHERE plan_id='183';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ieznqyziqmkmrq' WHERE plan_id='184';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-aupreplpubbyqz' WHERE plan_id='185';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-lgqmsjehzulitl' WHERE plan_id='186';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-xuconzjqqcdtzh' WHERE plan_id='187';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-cdqhlhtbmsrgip' WHERE plan_id='188';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-ckotcufpjahpem' WHERE plan_id='189';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-oodcyqrqicrmil' WHERE plan_id='190';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-mgjtqpjfbojzbx' WHERE plan_id='191';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qayblurpqpovfc' WHERE plan_id='192';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gmrpvaaqaqhjzi' WHERE plan_id='193';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-hfktzdnpspobap' WHERE plan_id='194';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-xricjjrhwyejqy' WHERE plan_id='195';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qgrbmvtdwunoei' WHERE plan_id='196';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kblwkkklchxegh' WHERE plan_id='197';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-kfvtndewtunceg' WHERE plan_id='198';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bcgzxvcvlaqvdi' WHERE plan_id='199';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-vaklsnotitbklx' WHERE plan_id='200';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-udwjjixjcvivgw' WHERE plan_id='201';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-gdcqjnfhffgakp' WHERE plan_id='202';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-yupglwsveyboin' WHERE plan_id='203';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jqweiovkonnccs' WHERE plan_id='204';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jupbinifuhlmfu' WHERE plan_id='205';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-bpwqhqwpvdwyxo' WHERE plan_id='206';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-qgimbrqdaxcmnu' WHERE plan_id='207';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-jzrxgyftlbaixj' WHERE plan_id='208';   "; $query= $this->db->query($query);  
		$query =  " UPDATE `start-plan` SET sct_plan_id='plan-nnicahhkwzekkd' WHERE plan_id='209';   "; $query= $this->db->query($query);  	 
	}

	protected $stripe_basic_id = '{"test":"prod_OCUWwiEMO9bZaP","live":"prod_OCUUZxq2eSLN5r"}';
	protected $stripe_pro_id = '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}';

	protected $stripe_promax_id = '{"test":"prod_PViLUBjm5K8FTP","live":"prod_PViKe9XmZ2L4aW"}';
	protected $stripe_promax_annual_id = '{"test":"prod_PW9wo6QkI7dZ4f","live":"prod_PW9v1uQ1SVYOXU"}';
	protected $stripe_advanced_id = '{"test":"prod_RHOCsUFp9Nbw8V","live":"prod_RHOF8gCsccsRPx"}';
	protected $version = 0; // prevent plan id to delete or reinsert || 209 last plan_id
}