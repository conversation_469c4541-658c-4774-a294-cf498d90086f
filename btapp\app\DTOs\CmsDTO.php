<?php

namespace App\DTOs;

/**
 * CmsDTO is a Data Transfer Object for handling CMS data.
 */
class CmsDTO
{
    public ?string $site_name = '';
    public ?string $full_text_content = '';

    /**
     * CmsDTO constructor.
     * Initializes properties from an array of data.
     *
     * @param array $data Associative array of properties to initialize.
     */
    public function __construct(array $data = [])
    {
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }
    }

    /**
     * Convert the CmsDTO to an associative array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function getBaseUrl(): string
    {
        $uri = service('uri');
        $host = $uri->getHost();
        $parts = explode('.', $host);
        $count = count($parts);

        if ($count >= 2) {
            return $parts[$count - 2] . '.' . $parts[$count - 1];
        }

        return $host; // Fallback to the full host if no subdomain is found
    }

    public function getDomainForTitle($domain = null): string
    {
        if (is_null($domain)) {
            $domain = base_url();
        }
        $domain = parse_url($domain, PHP_URL_HOST);

        switch (true) {
            case stristr($domain, 'ai-pro.org'):
                return 'AI-Pro.org';
            default:
                return $domain; // Return the original domain if no match found
        }
    }

    /**
     * Convert content into an excerpt
     *
     * @param string $content The content to convert
     * @param int $word_count The number of words to include in the excerpt (default: 55)
     * @param string $more The string to append if content is trimmed (default: '...')
     * @param bool $strip_shortcodes Whether to strip shortcodes (default: true)
     * @return string The generated excerpt
     */
    public function convertToExcerpt($content, $word_count = 55, $more = '...', $strip_shortcodes = true) : string
    { 
        libxml_use_internal_errors(true); // Avoid warnings from malformed HTML
        $doc = new \DOMDocument();
        $doc->loadHTML('<?xml encoding="UTF-8">' . $content);

        $tagsToRemove = ['script', 'style', 'video', 'audio', 'source'];
        foreach ($tagsToRemove as $tag) {
            while (($elements = $doc->getElementsByTagName($tag))->length > 0) {
                $element = $elements->item(0);
                $element->parentNode->removeChild($element);
            }
        }

        // Remove <a> tags but keep the inner content
        $anchors = $doc->getElementsByTagName('a');
        for ($i = $anchors->length - 1; $i >= 0; $i--) {
            $anchor = $anchors->item($i);
            $fragment = $doc->createDocumentFragment();
            while ($anchor->firstChild) {
                $fragment->appendChild($anchor->firstChild);
            }
            $anchor->parentNode->replaceChild($fragment, $anchor);
        }

        // Remove inline styles and event attributes
        $xpath = new \DOMXPath($doc);
        foreach ($xpath->query('//*[@style or @onclick or @onload or @onmouseover or @onerror]') as $node) {
            $node->removeAttribute('style');
            $node->removeAttribute('onclick');
            $node->removeAttribute('onload');
            $node->removeAttribute('onmouseover');
            $node->removeAttribute('onerror');
        }

        // Extract text and trim
        $text = $doc->textContent;
        $text = preg_replace('/\s+/', ' ', $text); // Normalize spacing
        $text = trim($text);

        // Store full text content
        $this->full_text_content = $text;

        // Limit words
        $words = explode(' ', $text);
        if (count($words) > $word_count) {
            $words = array_slice($words, 0, $word_count);
            $text = implode(' ', $words) . $more;
        }

        return $text;
    }

    /**
     * Adds sandbox attribute to all iframes in given HTML content.
     * If iframe already has a sandbox attribute, it leaves it unchanged.
     *
     * @param string $html The input HTML content
     * @param string $sandboxValue The value to set for the sandbox attribute
     * @return string Updated HTML content
     */
    public function addSandboxToIframes(string $html, string $sandboxValue = 'allow-scripts allow-same-origin allow-presentation allow-popups allow-forms'): string
    {
        libxml_use_internal_errors(true);

        $doc = new \DOMDocument();
        // Use UTF-8 hack to prevent charset issues
        $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        $iframes = $doc->getElementsByTagName('iframe');
        foreach ($iframes as $iframe) {
            // Only set sandbox if it doesn't already exist
            if (!$iframe->hasAttribute('sandbox')) {
                $iframe->setAttribute('sandbox', $sandboxValue);
            }
        }

        // Remove <html><body> wrapping added by loadHTML
        $body = $doc->getElementsByTagName('body')->item(0);
        $newHtml = '';
        if ($body) {
            foreach ($body->childNodes as $child) {
                $newHtml .= $doc->saveHTML($child);
            }
        }

        return $newHtml;
    }

    /**
     * Removes all <iframe> elements from the given HTML content and replaces them with a custom message.
     *
     * @param string $html The input HTML content.
     * @param string|null $replacementText Optional text to replace the iframe with (e.g., "Embedded content removed").
     * @return string HTML content without iframe elements and replacing them with a message.
     */
    public function removeIframes(string $html, ?string $replacementText = null): string
    {
        libxml_use_internal_errors(true);

        $doc = new \DOMDocument();
        // Use UTF-8 encoding to handle special characters
        $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        $iframes = $doc->getElementsByTagName('iframe');

        // Default message if none provided
        $defaultMessage = 'Security Violation: Embedded iframes are not allowed in content. Please update the content accordingly.';
        $message = $replacementText ?? $defaultMessage;

        // Loop backwards because we're modifying the live NodeList
        for ($i = $iframes->length - 1; $i >= 0; $i--) {
            $iframe = $iframes->item($i);

            if ($message !== null) {
                // Create a replacement <div> with the given text
                $replacementDiv = $doc->createElement('div', htmlspecialchars($message));
                $replacementDiv->setAttribute('style',
                    'background-color: #fff3cd; color: #856404; border: 1px solid #ffeeba; padding: 10px; margin: 10px 0; font-size: 14px; border-radius: 4px;'
                );
                $iframe->parentNode->replaceChild($replacementDiv, $iframe);
            } else {
                // Remove iframe entirely
                $iframe->parentNode->removeChild($iframe);
            }
        }

        // Extract cleaned content from the <body>
        $body = $doc->getElementsByTagName('body')->item(0);
        $cleanHtml = '';
        if ($body) {
            foreach ($body->childNodes as $child) {
                $cleanHtml .= $doc->saveHTML($child);
            }
        }

        return $cleanHtml;
    }

}
