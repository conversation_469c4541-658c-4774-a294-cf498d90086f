<?= '<?xml version="1.0" encoding="UTF-8"?>' . "\n" ?>
<xsl:stylesheet version="1.0"
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9">

  <xsl:output method="html" encoding="UTF-8" indent="yes" />

  <!-- Match the root node with namespace -->
  <xsl:template match="/">
    <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <title>AI-Pro.org <?= $title_before_sitemap ?> Sitemap <?= $title_after_sitemap ?></title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 2em;
            background-color: #f9f9f9;
            color: #333;
          }
          h1 {
            font-size: 24px;
            margin-bottom: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
          }
          th, td {
            text-align: left;
            padding: 10px;
            border-bottom: 1px solid #ddd;
          }
          th {
            background-color: #f2f2f2;
          }
          a {
            color: #007BFF;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
        </style>
      </head>
      <body>
        <h1>AI-Pro.org <?= $title_before_sitemap ?> Sitemap <?= $title_after_sitemap ?></h1>
        <p>Total URLs: <xsl:value-of select="count(sitemap:urlset/sitemap:url)" /></p>
        <table>
          <thead>
            <tr>
              <th>URL</th>
              <th>Last Modified</th>
              <th>Priority</th>
            </tr>
          </thead>
          <tbody>
            <xsl:for-each select="sitemap:urlset/sitemap:url">
              <tr>
                <td>
                  <a href="{sitemap:loc}">
                    <xsl:value-of select="sitemap:loc" />
                  </a>
                </td>
                <td><xsl:value-of select="sitemap:lastmod" /></td>
                <td><xsl:value-of select="sitemap:priority" /></td>
              </tr>
            </xsl:for-each>
          </tbody>
        </table>
      </body>
    </html>
  </xsl:template>

</xsl:stylesheet>