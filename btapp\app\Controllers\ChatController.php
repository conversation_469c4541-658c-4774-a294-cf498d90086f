<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class ChatController extends BaseController
{
    public function startChat()
    {
        include(FCPATH . 'global_lps.php');
        return view('chat/start');
    }

    public function startChatGPTAR()
    {
        include(FCPATH . 'global_lps.php');
        return view('chat/start-ar');
    }

    public function startGrokAI()
    {
        include(FCPATH . 'global_lps.php');
        return view('chat/start-grok');
    }

    public function startStableDiffusion()
    {
        include(FCPATH . 'global_lps.php');
        return view('chat/start-stable');
    }

    public function startDeepSeekAIc()
    {
        include(FCPATH . 'global_lps.php');
        return view('chat/start-deepseek-ai-c');
    }
} 