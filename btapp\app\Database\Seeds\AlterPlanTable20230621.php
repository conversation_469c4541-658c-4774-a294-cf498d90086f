<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AlterPlanTable20230621 extends Seeder
{
	protected $table = "plan";
    public function run()
    {
        $basic_data = "<div>ChatGPT-powered tools</div>";
        $basic_data .= "<div>No Response Cap - exhaustive response; no word count limit</div>";
        $basic_data .= "<div>Customizable Response Models - can provide scenario-based responses <small>(ex. I am a college professor. Write me a lecture about...)</small></div>";
        $basic_data .= "<div>Save Chat History - store up to hundreds of research results accessible any time</div>";
        $basic_data .= "<div>Choose Light or Dark Mode - for all screen types</div>";
        $basic_data .= "<div>Export Conversations - Image, Text, CSV or JSON files</div>";
        $basic_data .= "<div>Live Support</div>";
        //
        $pro_data = "<div>ChatGPT-powered tools</div>";
        $pro_data .= "<div>No Response Cap - exhaustive response; no word count limit</div>";
        $pro_data .= "<div>Customizable Response Models - can provide scenario-based responses <small>(ex. I am a college professor. Write me a lecture about...)</small></div>";
        $pro_data .= "<div>Save Chat History - store up to hundreds of research results accessible any time</div>";
        $pro_data .= "<div>Choose Light or Dark Mode - for all screen types</div>";
        $pro_data .= "<div>Export Conversations - Image, Text, CSV or JSON files</div>";
        $pro_data .= "<div>Live Support</div>";
        $pro_data .= "<div>ChatGPT-4 model</div>";
        $pro_data .= "<div>Chatbot Themes - change your background color</div>";
        $pro_data .= "<div>ChatPDF - ask questions based on your PDF document</div>";
        $pro_data .= "<div>Interior AI - create new room designs easily</div>";
        $pro_data .= "<div>Stable Diffusion-powered tools to create realistic images</div>";
        $pro_data .= "<div>AI Art Maker - generate art based on prompts</div>";
        $pro_data .= "<div>AI Art Prompt Gallery</div>";
        //
        $data = array(
            '1' => 'pro',
            '2' => 'basic',
            '3' => 'basic',
            '4' => 'pro',
            '5' => 'basic',
            '6' => 'pro',
            '7' => 'pro',
            '8' => 'basic',
            '9' => 'pro',
            '10' => 'pro',
            '11' => 'basic',
            '12' => 'pro',
            '13' => 'basic',
            '14' => 'basic',
            '15' => 'basic',
            '16' => 'pro',
            '17' => 'pro',
            '18' => 'pro',
            '19' => 'basic',
            '20' => 'pro'
        );
        //
        foreach($data as $key => $value) {
            $builder = $this->db->table($this->table);
            $update_data = [];
            if($value == 'basic') {
                $update_data['plan_type'] = 'Basic';
                $update_data['display_txt2'] = $basic_data;
            } else {
                $update_data['plan_type'] = 'Pro';
                $update_data['display_txt2'] = $pro_data;
            }
            $builder->where('plan_id', $key)->update($update_data);
        }
    }
}
