<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

class Throttle implements FilterInterface
{
    /**
     * This is a demo implementation of using the Throttler class
     * to implement rate limiting for your application.
     * https://codeigniter.com/user_guide/libraries/throttler.html
     *
     * @param array|null $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
      $throttler = Services::throttler();
      // Restrict an IP address to no more than 1 request
      // per second across the entire site.
      // if ($throttler->check(md5($request->getIPAddress()), 60, MINUTE) === false) {
      //     return Services::response()->setStatusCode(429);
      // }

      // log_message('info', json_encode($arguments));
      // log_message('info', json_encode($request->getServer()['REQUEST_URI']));
			// echo getenv('API_RATE_LIMIT');

      if ($throttler->check(md5($request->getIPAddress()), getenv('API_RATE_LIMIT'), MINUTE) === false) {
        log_message("debug",  getenv('API_RATE_LIMIT'));
        return Services::response()->setStatusCode(429, 'Sorry, too many requests. Please try again in a bit!');
      }
    }

    /**
     * We don't have anything to do here.
     *
     * @param array|null $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // ...
    }
}