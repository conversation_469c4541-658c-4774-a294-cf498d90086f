import Cookies from 'universal-cookie';
import { aiproFlags } from '../../hooks/flags';
const cookies = new Cookies();

export function SetCookie(name, value, option = { path: '/' }) {
    cookies.set(name, value, option);
    aiproFlags.flags[name] = value;
};
export function RemoveCookie(name, option = { path: '/' }) {
    cookies.remove(name, option);
    aiproFlags.flags[name] = "";
};
export function GetCookie(name) {
    return aiproFlags.flags[name] || "";
};

export function GetSubdomain() {
    const fullHostName = window.location.hostname;
    const checkDomain = fullHostName.includes("chatapp") || fullHostName.includes("chat-app");
    return checkDomain;
}