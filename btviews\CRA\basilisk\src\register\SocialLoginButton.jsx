const SocialLoginButton = ({
  provider,
  variant = "classic",
  imgSrc,
  text,
  redirectUrl,
}) => {
  const isStyled = variant === "styled";

  return (
    <button 
      type="button"
      onClick={() => (window.location.href = redirectUrl)}
      className={`w-full max-w-[203px] mx-auto mb-4 p-2 z-50 text-center cursor-pointer flex items-center justify-center gap-2 
        ${
          isStyled
            ? "bg-gray-300 rounded p-2 mx-auto"
            : "border border-gray-300 rounded"
        }`}
    >
      <img
        src={imgSrc}
        alt={`${provider} login`}
        className={`${
          provider === "google" ? "w-[17px]" : "w-[20px]"
        } max-h-[20px] inline align-sub`}
      />
      <span
        className={`text-sm ${
          isStyled ? "font-bold" : "font-normal"
        } text-black leading-[23.1px]`}
      >
        {text}
      </span>
    </button>
  );
};

export default SocialLoginButton;
