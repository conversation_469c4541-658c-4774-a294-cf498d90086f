<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateAccountTable extends Migration
{
    private $table = "account";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'account_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'account_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'user_id' => [
				'type' => 'INT',
				'constraint' => 11
			],
			'plan_id' => [
				'type' => 'INT',
				'constraint' => 11
			],
			'start_date' => [
				'type' => 'DATETIME',
			],
			'end_date' => [
				'type' => 'DATETIME',
			],
			'trial_end' => [
				'type' => 'DATETIME',
			],
			'merchant' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
            ],
			'merchant_customer_id' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
            ],
			'merchant_subscription_id' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
            ],
			'status' => [
				'type' => "SET('active','inactive')",
				'default' => 'active',
            ],
			'cancelled_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			]
		]);

		$this->forge->addKey('account_id', true);
		$this->forge->addKey(['user_id'], false, false, 'start_account_user_id_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
