    <script nonce="<?= $nonce ?>">
        // Keep your existing JavaScript, but update the close functionality
        window.addEventListener("message", function (event) {
            if (event.data.type === "OPEN_CALLER_MODAL") {
                document.getElementById("callerModal").style.display = "block";
                document.getElementById("callerModalBackdrop").style.display = "block";
            }
        });

        function closeModal() {
            document.getElementById("callerModal").style.display = "none";
            document.getElementById("callerModalBackdrop").style.display = "none";
        }

        // Add click handler for the Ã- in the title
        document.querySelector(".caller-modal h2").addEventListener("click", function (e) {
            closeModal();
        });

        document.getElementById("callerForm").addEventListener("submit", function (event) {
            event.preventDefault();

            const name = this.name.value;
            const email = this.email.value;

            const iframe = document.querySelector("iframe"); // Assuming only one iframe
            if (iframe) {
                iframe.contentWindow.postMessage(
                    {
                        type: "CALLER_INFO_SUBMITTED",
                        payload: { name, email },
                    },
                    "https://dolttbbx.ai-pro.org/"
                );
            }

            closeModal();
        });
    </script>