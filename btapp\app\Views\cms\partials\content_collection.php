

                                <?php foreach ($articles as $article): ?>
                                    <article id="post-<?= esc($article['pid']) ?>" class="entry-card post-<?= esc($article['pid']) ?> post type-post status-publish format-standard has-post-thumbnail hentry category-<?= esc($article['category_slug']) ?> tag-<?= esc($article['tags'][0] ?? '') ?>">
                                        <ul class="entry-meta" data-type="simple:slash">
                                            <li class="meta-categories" data-type="simple">
                                                <a href="<?= esc($article['canonical_url']) ?>" rel="tag" class="ct-term-<?= esc($article['category_id']) ?>">
                                                    <?= esc($article['category_name']) ?>
                                                </a>
                                            </li>
                                        </ul>
                                        <h2 class="entry-title">
                                            <a href="<?= $article['canonical_url'] ?>" rel="bookmark"><?= esc($article['title']) ?></a>
                                        </h2>
                                        <a class="ct-image-container" href="<?= $article['canonical_url'] ?>" aria-label="<?= esc($article['title']) ?>" tabindex="-1">
                                            <img 
                                                width="300" 
                                                height="300" 
                                                src="<?= esc($article['image']) ?>" 
                                                class="attachment-medium size-medium wp-post-image" 
                                                alt="<?= esc($article['title']) ?>" 
                                                decoding="async" 
                                                loading="lazy" 
                                                style="aspect-ratio: 4/3;">
                                        </a>
                                        <div class="entry-excerpt">
                                            <p><?= esc($article['excerpt']) ?></p>
                                        </div>
                                        <a class="entry-button" data-type="simple" data-alignment="left" href="<?= $article['canonical_url'] ?>">
                                            Read More
                                            <svg width="17px" height="17px" viewBox="0 0 32 32">
                                                <path d="M 21.1875 9.28125 L 19.78125 10.71875 L 24.0625 15 L 4 15 L 4 17 L 24.0625 17 L 19.78125 21.28125 L 21.1875 22.71875 L 27.90625 16 Z "></path>
                                            </svg>
                                        </a>
                                        <div class="ct-ghost"></div>
                                    </article>
                                <?php endforeach; ?>
