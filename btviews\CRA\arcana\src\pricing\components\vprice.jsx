import React, { Suspense, useState } from 'react';
import { motion } from "framer-motion";
import { getPricePlan, DailyPrice, PriceFormatted, displayTextFormatted, getPlanName } from '../../core/utils/main';
import { GetCookie } from '../../core/utils/cookies';
import { hexHash, hoverDarken } from '../../core/utils/helper';
import '../css/style.css';
import 'font-awesome/css/font-awesome.min.css';
import { useTranslation } from 'react-i18next';

const TpReviews = React.lazy(() => import('../../features/tpreviews'));

function VPrice(props) {
  const data = props.data ? props.data : null;
  const setPricing = props.setPricing ? props.setPricing : ()=>{};
  var ppg = GetCookie("ppg");
  if (ppg==='') {
    ppg =  process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : '14';
  }

  const pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "2872FA";
  const ppgArrayWithToggle = ['40','48','52','97','101','109','110','114'];
  const showToggle = (ppgArrayWithToggle.includes(ppg) && props.showToggle) ? props.showToggle : false;
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : "";
  const enterprise = GetCookie("enterprise") ? GetCookie("enterprise") : "off";
  const ptoggle = GetCookie("p_toggle") ? GetCookie("p_toggle") : "";
  const [ desc_align ] = useState(GetCookie("desc_align"));
  const flow = GetCookie("flow") || "";
  const kt8typtb = GetCookie('kt8typtb') || '';
  var billedAnnualDisplay = false;
  const { t } = useTranslation();
  const pp_cta = GetCookie("pp_cta") ? GetCookie("pp_cta") : "";
  const acwp_ux = GetCookie("acwp_ux") ? GetCookie("acwp_ux") : "";
  
  if (ppg==='48'){
    billedAnnualDisplay = true;
  }

  if (ptoggle ==='01' || ptoggle === '03'){
    billedAnnualDisplay = true;
  }

  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? "yearly" : "monthly");
  
  const intervalChange = function() {
    if(planInterval === "monthly") {
      setPlanInterval("yearly");
    } else {
      setPlanInterval("monthly");
    }
  };

  const checkPlanInterval = function(plan) {
    if(!showToggle) return true;
    if(plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }

  const enterpriseTab = function() {
    return (
        <div className="price_col w-full :w-[330px] text-center px-4 mb-8 relative poppins">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden xl:w-[330px]">
            <div className="px-6 py-20 md:py-10 price-content">
              <h3 className="text-xl font-bold mb-4">{t('arcana.pricing.vprice_02.enterprise.title')}</h3>
              <p className={`text-4xl font-bold text-gray-800 ${planInterval === 'yearly' && ppg === '48'  ? "" :"mb-4"}`}>{t('arcana.pricing.vprice_02.enterprise.price')}</p>
              { planInterval === 'yearly' && ppg === '48' && GetCookie("daily") !== 'on' ? <div className='text-xs mb-4'>(billed yearly)</div> : '' }

              <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>
                <ul className="text-sm text-gray-600">
                  <li className="mb-2 font-bold"><div>{t('arcana.pricing.vprice_02.enterprise.desc1')}</div></li>
                  <li className="mb-2"><div>{t('arcana.pricing.vprice_02.enterprise.desc2')}</div></li>
                </ul>
              </div>
              <div className='py-4'>
                {pp_cta !== 'top' && (
                <motion.button
                  className={`text-white font-bold py-3 px-3 rounded-lg ${acwp_ux === 'on' ? 'mt-[10px]' : ''}`}
                  style={{ backgroundColor: hexHash(pp_ctaclr) }}
                  whileHover={{ scale: 1.1, backgroundColor: hoverDarken(pp_ctaclr) }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setPricing(62)}
                >
                  {t('arcana.pricing.vprice_02.enterprise.cta')}
                </motion.button>
                )}
              </div>
            </div>
          </div>
          {/* {planInterval === 'yearly' && showToggle ? <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight">
              <div>Up to <span className='font-bold underline-offset-1'>20% OFF</span> on an annual subscription</div>
          </div> : null} */}
        </div>
    );
  };  

  return (
      <div className={`v-pricing pricing bg-gray-100 poppins ${flow === '06' ? 'rounded py-4 px-8' : 'px-8 md:px-[0] md:min-h-[90vh]'} ${desc_align === 'left' ? '!px-4 md:!px-[0]' : ''}`}>
        <div className="container mx-auto">
          <div className={`pricing_columns mx-auto`}>
            <div className={`flex flex-col items-center ${flow === '06' ? 'pt-[30px]' : acwp_ux === 'on' ? 'md:pt-[40px]' : 'pt-[15px] md:pt-[70px]'}`}>
              <h1 className={`text-4xl lg:text-4xl font-semibold text-center min-h-[40px] ${kt8typtb === 'arcana_wp' ? (showToggle ? 'mb-[5px]' : 'mb-[40px]') : 'mb-8'}`}>
                {t('arcana.pricing.vprice.title')}
              </h1>
              {showToggle && (
                <div className={`${kt8typtb === 'arcana_wp' ? 'px-4 pb-4' : 'p-4'}`}>
                  <div className="text-1xl lg:text-1xl font-semibold text-center mb-4">
                    <div>Choose between our monthly and yearly options below</div>
                  </div>
                  <div className="flex items-center justify-center w-full mb-8 mt-[40px]">                
                    <label htmlFor="toggleB" className="flex items-center cursor-pointer">
                      <div className={`${planInterval === 'monthly' ? "text-[#2872FA] font-semibold" : "text-black"} mr-3 uppercase`}>
                      {t('arcana.pricing.vprice.monthlyText')}
                      </div>
                      <div className="relative">
                        <input
                          type="checkbox"
                          id="toggleB"
                          className="sr-only toggle"
                          onChange={intervalChange}
                          defaultChecked={billedAnnualDisplay}
                        />
                        <div className="block bg-[#9CA3AF] w-12 h-6 rounded-full"></div>
                        <div
                          className={`dot absolute top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 ease-in-out ${planInterval === 'yearly' ? 'left-[0.3rem]' : 'left-1'
                            }`}
                        ></div>
                      </div>
                      <div className={`${planInterval === 'yearly' ? "text-[#2872FA] font-semibold" : "text-black"} ml-3 uppercase`}>
                      {t('arcana.pricing.vprice.yearlyText')}
                      </div>
                    </label>
                  </div>
                </div>
              )}
              { showToggle ? (
                <div className="pricing-toggle flex flex-col md:flex-row justify-center w-full">
                  {data?.map((plan, index) => (
                    checkPlanInterval(plan) ? (
                      <div key={index} className={`price_col text-center relative w-full md:w-auto max-w-[500px] mx-auto md:mx-[0] sm:px-4 mb-8`}>
                        {planInterval === 'monthly' && getPlanName(plan).toLowerCase() === 'pro' && kt8typtb === 'arcana_wp' && ppg !== '119' && (
                          <div className="popu absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-medium py-1 px-4 text-xs span-highlight z-10">
                            <span>{t('arcana.pricing.vprice.mostPopularText')}</span>
                          </div>
                        )}
                        <div className={`bg-white rounded-lg overflow-hidden xl:w-[330px] relative ${planInterval === 'yearly' && showToggle ? 'pt-8' : ''}`} style={{ boxShadow: '0px 0px 9px 0px rgba(0, 0, 0, 0.1)' }}>
                          { planInterval === 'yearly' && showToggle ? 
                            <div className="bg-blue-100 text-blue-500 font-semibold py-1 px-4 text-xs span-highlight absolute top-0 left-0 right-0 w-full">
                              <div>Up to <span className='font-bold underline-offset-1'>20% OFF</span> on an annual subscription</div>
                            </div> 
                          : null }
                          <div className="price-content">
                            <h3 className={`text-xl font-semibold pt-[30px] px-5 pb-[9px] ${kt8typtb === 'arcana_wp' ? 'text-gray-800' : ''}`}>
                              {getPlanName(plan)}
                            </h3>
                            {pp_cta === 'top' && (
                              <motion.button
                                className={`text-white font-bold p-3 rounded-lg w-[140px] !static !transform-none my-[10px] ${acwp_ux === 'on' ? '!bottom-0 mt-[10px]' : ''}`}
                                style={{ backgroundColor: hexHash(pp_ctaclr) }}
                                whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                                onClick={() => setPricing(plan.plan_id)}
                              >
                                {plan.label.toLowerCase() === 'enterprise' ? 'Build My Plan' : t('arcana.pricing.vprice.ctaButtonText')}
                              </motion.button>
                            )}
                            {GetCookie("daily") === 'on' ? (<DailyPrice plan={plan}/>) : 
                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && (ppg === '48' || ptoggle === '02') ?
                                (
                                  <p className={`text-4xl font-semibold text-gray-800 ${ppg === '48' && ptoggle === '01' ? 'mb-4' : ''}`}>
                                    {getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))}
                                    <span className="text-sm"> /month</span>
                                  </p>
                                ) : plan.trial_price ?
                                  (
                                    <p className="text-4xl font-semibold text-gray-800">
                                      {getPricePlan(plan.currency, plan.trial_price)}
                                    </p>
                                  ) :
                                  (
                                    <p className={`text-4xl font-semibold text-gray-800 ${(ptoggle === '02' || ptoggle === '03') ? '' : 'mb-4'}`}>
                                      {(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === "Yearly"
                                        ? getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))
                                        : getPricePlan(plan.currency, plan.price)}
                                      <span className="text-sm">
                                        /{(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === "Yearly"
                                          ? " month"
                                          : plan.payment_interval === "Monthly"
                                            ? " month"
                                            : " year"}
                                      </span>
                                    </p>
                                  )
                              )
                            }
                            {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === 'Yearly' && GetCookie("daily") !== 'on' && (
                              <span className="text-sm text-gray-600 mb-4">
                                or {getPricePlan(plan.currency, plan.price)} annual
                              </span>
                            )}
                            {(
                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && !['01', '02', '03'].includes(ptoggle) && GetCookie("daily") !== 'on') ||
                              (!['01', '02', '03'].includes(ptoggle) && ppg === 48 && GetCookie("daily") !== 'on')
                            ) && (
                                <div className='text-xs mb-4'>(billed yearly)</div>
                              )}
                            <div className={`pricing-description pt-[5px] px-6 pb-[25px] ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg} ${(ptoggle === '02' || ptoggle === '03') ? 'mt-4' : ''}`}>
                              <ul className={`text-sm text-gray-600 ${(pp_cta === 'top' || acwp_ux === 'on') ? '' : 'min-h-[550px]'} ${kt8typtb === 'arcana_wp' ? 'text-gray-800' : ''}`}>
                                {plan.display_txt2 && (
                                  <li className={`mb-[3.5rem] leading-7`} dangerouslySetInnerHTML={{ __html: displayTextFormatted(plan) }}></li>
                                )}
                              </ul>
                            </div>
                          {pp_cta !== 'top' && (
                            <motion.button
                              className={`text-white font-bold p-3 rounded-lg w-[140px] ${acwp_ux === 'on' ? ' !bottom-0 mt-[10px]' : ''}`}
                              style={{ backgroundColor: hexHash(pp_ctaclr) }}
                              whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                              onClick={() => setPricing(plan.plan_id)}
                            >
                              {t('arcana.pricing.vprice.ctaButtonText')}
                            </motion.button>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : ""
                  ))}
                  {(enterprise === 'on' && ppg !== '46' && planInterval === 'yearly' && data[0].currency === 'USD') ?
                    <>
                    {enterpriseTab()}
                    </>
                  : null }
                </div>
              ) : (
                <div className="flex flex-col md:flex-row justify-center w-full">
                  {data?.map((plan, index) => (
                    <div key={index} className={`price_col text-center relative w-full md:w-auto max-w-[500px] mx-auto md:mx-[0] sm:px-4 mb-8`}>
                      {planInterval === 'monthly' && getPlanName(plan).toLowerCase() === 'pro' && kt8typtb === 'arcana_wp' && ppg !== '119' && (
                        <div className="popu absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-medium py-1 px-4 text-xs span-highlight z-10">
                          <span>{t('arcana.pricing.vprice.mostPopularText')}</span>
                        </div>
                      )}
                      <div className={`bg-white rounded-lg overflow-hidden xl:w-[330px] relative ${planInterval === 'yearly' && showToggle ? 'pt-8' : ''}`} style={{ boxShadow: '0px 0px 9px 0px rgba(0, 0, 0, 0.1)' }}>
                        { planInterval === 'yearly' && showToggle ? 
                          <div className="bg-blue-100 text-blue-500 font-semibold py-1 px-4 text-xs span-highlight absolute top-0 left-0 right-0 w-full">
                            <div>Up to <span className='font-bold underline-offset-1'>20% OFF</span> on an annual subscription</div>
                          </div> 
                        : null }
                        <div className="price-content">
                          <h3 className={`text-xl font-semibold pt-[30px] px-5 pb-[9px] ${kt8typtb === 'arcana_wp' ? 'text-gray-800' : ''}`}>{getPlanName(plan)}</h3>

                          <PriceFormatted plan={plan}/>
                          {pp_cta === 'top' && (
                            <motion.button
                              className={`text-white font-bold p-3 rounded-lg w-[140px] !static !transform-none my-[10px] ${acwp_ux === 'on' ? '!bottom-0 mt-[10px]' : ''}`}
                              style={{ backgroundColor: hexHash(pp_ctaclr) }}
                              whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                              onClick={() => setPricing(plan.plan_id)}
                            >
                              {t('arcana.pricing.vprice.ctaButtonText')}
                            </motion.button>
                          )}
                          <div className={`pricing-description pt-[5px] px-6 pb-[25px] ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>
                            <ul className={`text-sm text-gray-600 ${(pp_cta === 'top' || acwp_ux === 'on') ? '' : 'min-h-[550px]'} ${kt8typtb === 'arcana_wp' ? 'text-gray-800' : ''}`}>
                              {plan.display_txt2 && (
                                <li className={`mb-[3.5rem] leading-7`} dangerouslySetInnerHTML={{ __html: displayTextFormatted(plan) }}></li>
                              )}
                            </ul>
                          </div>
                          {pp_cta !== 'top' && (
                            <motion.button
                              className={`text-white font-bold p-3 rounded-lg w-[140px] ${acwp_ux === 'on' ? '!bottom-0 mt-[10px]' : ''}`}
                              style={{ backgroundColor: hexHash(pp_ctaclr) }}
                              whileHover={{ backgroundColor: hoverDarken(pp_ctaclr) }}
                              onClick={() => setPricing(plan.plan_id)}
                            >
                              {plan.label.toLowerCase() === 'enterprise' ? 'Build My Plan' : t('arcana.pricing.vprice.ctaButtonText')}
                            </motion.button>
                          )}
                        </div>
                      </div>
                      { index === 1 ? <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-medium py-1 px-4 text-xs span-highlight">
                        <span>{t('arcana.pricing.vprice.mostPopularText')}</span>
                      </div> : null }
                    </div>
                  ))}
                  {(enterprise === 'on' && ppg !== '46' && data[0].currency === 'USD') ?
                    <>
                    {enterpriseTab()}
                    </>
                  : null }
                </div>
              )}

              <p className="text-xs text-center leading-relaxed mb-10 lg:mb-12 p-4">
                {t('arcana.pricing.vprice.note')}
              </p>

              { tpreviews === 'on' ?
                <> 
                <Suspense fallback={null}>
                  <TpReviews/>
                </Suspense>
                </>
              : null }
            </div>
          </div>
        </div>
      </div>
  )
}

export default VPrice;