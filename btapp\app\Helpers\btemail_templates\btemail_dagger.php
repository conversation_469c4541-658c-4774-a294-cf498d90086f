<?php

/**
 * BTEmail Dagger.
 */

function dagger_defaultEmailHead($_content = 'Welcome to ai-pro.org!')
{
    return '<head>
        <title>' . $_content . '</title>
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;400;500;700&display=swap" rel="stylesheet">
        <style type="text/css">
            h4 {margin-bottom: 2px;}
            button {cursor: pointer;}
            #mmail .yhoo a, a {color:#ffffff !important;}
            a.doc_owner {color:#ffffff !important; text-decoration:none !important; font-size: 20px;}
            body {margin: 10px;  background: #336699 !important; background-color: #336699 !important;}
            html { background: #336699 !important; background-color: #336699 !important;}
            button {color:#336699; text-decoration: none;}
            h5.sendme span a { color: #fff !important; text-decoration: none !important;}
            span.im, body .im, div.im { color: #ffcc68 !important;}
            .yhoo { background: #336699 !important; background-color: #336699 !important; color:#ffffff; margin auto 10px; padding: 10px !important;}
        </style>
        </head>
    ';
}

function dagger_defaultEmailContentHeader()
{
    // $url = getenv("UI_URL") ? getenv("UI_URL") : "https://start.ai-pro.org/";
    $url = base_url();
    $site_url = $url . '/assets/dagger';

    return '<header style="text-align: left;">
        <img src="' . $site_url . '/img/header_logo_email.png" style="width:200px; display:block; margin: 0 auto; text-align: left;"/>
        </header>
    ';
}

function dagger_defaultEmailContentFooter()
{
    return '<div style="text-align: justify;padding: 20px 0;">
        <h4>Do Not Share this Email</h4>
        <p>
            This email contains a secure link to ai-pro.org. If you received this by mistake or if you are not the intended recipient of this message, please inform the sender by an email reply and then delete the message and its attachments. Please do not share this email, link, or access code with others. It is forbidden to copy, forward, or in any way reveal the contents of this message to anyone.
        </p>

        <h4>About ai-pro.org</h4>
        <p>
            Sign documents electronically in minutes. ai-pro.org is safe, secure, and legally binding. Whether you\'re in the office, at home, on-the-go, or even across the globe, we provide a professional solution for digital transactions.
        </p>

        <h4>Questions about the Document?</h4>
        <p>
            If you need to modify the document or have questions about the details in the document, please reach out to the sender by emailing them directly.
        </p>
        <p>
            If you are having trouble signing the document, please contact our Support at <a style="color: #ffcc66ff;" href="mailto:<EMAIL>"><EMAIL></a>.
        </p>
        </div>
    ';
}

/**------------------------------------------------------------------------------
 * Email: Welcome Email
------------------------------------------------------------------------------*/
function dagger_btemailSendWelcomeEmail($_email_data = [])
{
    // $url = "https://start.ai-pro.org/";
    $url = base_url();
    $email = $_email_data['send_to'];
    $dear_name = htmlentities(urlencode($email));
    $login_link = '<a href="' . $url . 'login' .'"style="margin: auto; color: #3b82f6; text-decoration: none !important; ">here</a>';
    $dashboardlink = anchor_popup($url . 'my-account', '<button style="margin: auto; background-color: #3b82f6; text-align: center; color: #ffffff; text-decoration: none !important; font-size: 18px;padding: 10px 20px;border-radius: 10px;border: 0;">Get Started</button>');
    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">

          <p>Hi '. $dear_name .',</p>

          <p>Welcome to AI-Pro! Thank you for registering with us.</p>

          <p>Log in to your account '.$login_link .'.</p>

          <p>Your subscription details are as follows:</p>
          <div style="background-color:#ebf3ff; padding-top:20px; padding-bottom:20px; margin 35px; border-radius: 5px; width:500px;">
            <p style="margin:20px; color: #272828;">Username: <b>'. $dear_name .'</b></p>

          </div>

          <p>We regularly update and release new apps and features to provide you with a comprehensive resource for all your AI needs.</p>

          <p>If you need assistance or have questions, you may contact our <a style="color: #0043a9;" href="mailto:<EMAIL>">support team</a>.</p>
          <p>For Billing and Subscription concerns you may reach Customer Support toll-free at 1-888-2139008, from Monday to Friday, 7AM to 4PM and 10PM to 7AM Pacific Time</p>

          '. $dashboardlink .'

          <p>Sincerely,<br>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}

/**------------------------------------------------------------------------------
 * Email: Thank you Email
------------------------------------------------------------------------------*/
function dagger_btemailSendThankyouEmail($_email_data = [])
{
    // $url = "https://start.ai-pro.org/";
    $url = base_url();
    $email = $_email_data['send_to'];
    $plan = isset($_email_data['plan']) ? $_email_data['plan'] : "";
    $plan_details = isset($_email_data['plan_details']) ? $_email_data['plan_details'] : "";
    $dear_name = htmlentities(urlencode($email));
    $dashboardlink = anchor_popup($url . 'my-account', '<button style="margin: auto; background-color: #3b82f6; text-align: center; color: #ffffff; text-decoration: none !important; font-size: 18px;padding: 10px 20px;border-radius: 10px;border: 0;">Get Started</button>');
    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">

          <p>Hi '. $dear_name .',</p>

          <p>Thank you for subscribing to AI-PRO. We appreciate your support and look forward to sharing valuable updates with you. Get ready to unlock the true potential of AI!</p>

          <p>Your account details:</p>
          <div style="background-color:#ebf3ff; padding-top:20px; padding-bottom:20px; margin 35px; border-radius: 5px; width:500px;">
            <p style="margin:20px; color: #272828;">Username: <b>'. $dear_name .'</b></p>
            <p style="margin:20px; color: #272828;">Plan: <b>'. $plan .' - '. $plan_details .'</b></p>

          </div>

          <p>If you need assistance or have questions, you may contact our <a style="color: #0043a9;" href="mailto:<EMAIL>">support team</a>.</p>

          '. $dashboardlink .'

          <p>Sincerely,<br>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}

/**------------------------------------------------------------------------------
 * Email: Reset Password
------------------------------------------------------------------------------*/
function dagger_btemailSendResetPassword($_email_data = [])
{
    // $url = "https://start.ai-pro.org/";
    // $url = getenv('app.baseURL') . "/";
    $url = base_url();
    $token = $_email_data['token'];
    $email = $_email_data['send_to'];
    $resetlink = anchor_popup($url . 'resetpass?email=' . urlencode($email) . '&tk=' . $token, '
        <button style="margin: auto; background-color: #3b82f6; text-align: center; color: #ffffff; text-decoration: none !important; font-size: 18px;padding: 10px 20px;border-radius: 10px;border: 0;">Reset Password</button>');
    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    // $dashboardlink = anchor_popup($url . 'myaccount', '<button style="margin: auto; background-color: #3b82f6; text-align: center; color: #ffffff; text-decoration: none !important; font-size: 18px;padding: 10px 20px;border-radius: 10px;border: 0;">Get Started</button>');
    // $resetlink = anchor_popup($url . '/resetpass?email=' . urlencode($email) . '&t=' . $token, '
    // 	<button style="margin: auto; background-color: #FFCC66; text-align: center;color: #003366; text-decoration: none !important; font-size: 18px;padding: 20px;border-radius: 10px;border: 0;">CHANGE MY PASSWORD</button>');

    // $dear_name = htmlentities(urlencode($email));

    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">

          <p>Hi,</p>

          <p>We have received your request to reset your password.</p>

          <p>Your current information is:</p>
          <p style="background-color:#f3f4f6; padding 5px; border-radius: 3px;"></p>
          <div style="background-color:#ebf3ff; padding-top:20px; padding-bottom:20px; margin 35px; border-radius: 5px; width:500px;">
            <span style="margin:20px; color: #272828;">Email: <b>'. $email .'</b></span>
          </div>

          <p>If you wish to reset your password visit the following link, otherwise ignore this email to keep your current account information:</p>
          '. $resetlink .'

          <p>Thank you for using AI-PRO!</p>
          ' . $imageLogo . '
        </html>

    ';

    return $message;
}
/**------------------------------------------------------------------------------
 * Email: Delete Acount Email
------------------------------------------------------------------------------*/
function dagger_btemailSendDeleteAccountEmail($_email_data = [])
{
    $url = "https://start.ai-pro.org/";
    $email = $_email_data['send_to'];
    $dear_name = htmlentities(urlencode($email));

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">

          <p>Please be advised that your account has been deleted.</p>
          <p>Your account details:</p>
          <div style="background-color:#ebf3ff; padding-top:20px; padding-bottom:20px; margin 35px; border-radius: 5px; width:500px;">
            <p style="margin:20px; color: #272828;">Username: <b>'. $email .'</b></p>
          </div>
          <br>
          <p>We are sorry to see you go.</p>
          <p>We hope you come back soon to explore our other current and new AI solutions.</p>
          <br>
          <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Payment Information
------------------------------------------------------------------------------*/
function dagger_btemailSendEnterprisePaymentInfo($_email_data = [])
{
    $url = "https://start.ai-pro.org/";
    $members = $_email_data['members'];
    $total_amount = $_email_data['total_amount'];
    $user_id = $_email_data['user_id'];

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">
						<p>Hi,</p>

						<p>
						We see that you need more time to think. No worries!
						</p>

						<p>
						Whenever you\'re ready to move forward with setting up your AI-PRO Enterprise account, please refer to the bank details provided below to facilitate your payment:
						</p>

						<p>
						<strong>Beneficiary:</strong> TELECOM BUSINESS SOLUTIONS, INC.
						<br>
						<strong>SWIFT:</strong>  BOFAUS3N
						<br>
						<strong>Bank Name:</strong>  Bank of America
						<br>
						<strong>Routing (Wire):</strong>  *********
						<br>
						<strong>Routing Number (paper & electronic):</strong>  *********
						<br>
						<strong>Account Number:</strong> 3810-6766-2647
						<br>
						<strong>Customer Number:</strong> '.$user_id.'
						</p>

						<p>
						(Please ensure that you include your Customer Number '.$user_id.' in the payment reference to ensure proper allocation.)
						</p>

						<p>
						<strong>Number of users:<strong> '.$members.'
                        <br>
						<strong>Total amount due:</strong> $'.$total_amount.'
						</p>

						<p>
						Once the payment has been completed, please confirm by clicking the link below to validate your transaction:
						</p>

						<p>
						<a href="'.$url.'payment-reference">'.$url.'payment-reference</a>
						</p>

						Should you have any queries or require further assistance, please do not hesitate to contact our customer support <NAME_EMAIL>

						<br>
        <p>Sincerely,</p>
        <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}


/**------------------------------------------------------------------------------
 * Email: Enterprise Payment Information
------------------------------------------------------------------------------*/
function dagger_btemailSendEnterprisePaymentConfirmation($_email_data = [])
{
    $url = "https://start.ai-pro.org/";
    $email = $_email_data['email'];
    $reference_number = $_email_data['reference_number'];
    $amount = $_email_data['amount'];

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">
					<p>
					This is to notify you that '.$email.' with a payment of $'.$amount.' and reference number: '.$reference_number.' has been initiated.
					Kindly acknowledge receipt when payment is credited to the account.
					</p>

					<p>
					Please be advised that it may take 2-3 banking days for the payment to fully reflect in the account.
					</p>

					<br>
        <p>Sincerely,</p>
        <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}


/**------------------------------------------------------------------------------
 * Email: Enterprise Welcome Email for Members
------------------------------------------------------------------------------*/
function dagger_btemailSendEnterpriseWelcomeMembers($_email_data = [])
{
    $url = "https://start.ai-pro.org/";
    $first_name = $_email_data['first_name'];
    $email = $_email_data['email'];
    $temp_password = $_email_data['temp_password'];
    $owner_email = $_email_data['owner_email'];

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">
					<p>Welcome to AI-Pro!</p>
					<p>'.$owner_email.' added you as an Enterprise account member.</p>
					<br>
					<p>Your login details:</p>
					Username: '.$email.'
					<br>
					Temporary Password: '.trim($temp_password).'<br>
					<p><a href="'.$url.'login">Log in to your account here.</a></p>
					<br>
					<p>If you need assistance or have questions, you may contact our <a href="mailto:<EMAIL>">support team</a>.</p>
					<br>

					<p>Sincerely,</p>
          <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Resend Password for Members
------------------------------------------------------------------------------*/
function dagger_btemailResendPasswordEnterpriseMembers($_email_data = [])
{
    $url = "https://start.ai-pro.org/";
    $temp_password = $_email_data['temp_password'];

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">
					<p>To assist you in accessing your enterprise member account, we\'ve generated a new temporary password for you:</p>
					<p>&nbsp;</p>
					<p>Temporary Password: '.$temp_password.'</p>
					<p>&nbsp;</p>
					<p><a href="'.$url.'login">Log in to your account here.</a></p>
					<p>&nbsp;</p>
					<p>Once logged in, you can reset your password on the “Manage My Account” page.</p>
					<p>&nbsp;</p>
					<p>If you need assistance or have questions, you may contact our <a style="color: #ffcc66ff;" href="mailto:<EMAIL>">support team</a>.</p>
          <br>
					<p>Sincerely,</p>
          <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
			';

    return $message;
}

/**------------------------------------------------------------------------------
 * Email: Acount Temporary Locked Email
------------------------------------------------------------------------------*/
function dagger_btemailSendAccountTempLockedEmail($_email_data = [])
{
    $url = base_url();
    $email = $_email_data['send_to'];
    $token = $_email_data['token'];
    $ent_pricing = $url . 'pricing?ppg=46';
    $resetlink = $url . 'resetpass?email=' . urlencode($email) . '&tk=' . $token;
    $resetbutton = anchor_popup($resetlink, '
        <button style="margin: auto; background-color: #3b82f6; text-align: center; color: #ffffff; text-decoration: none !important;padding: 10px 20px;border-radius: 10px;border: 0;">Reset Password</button>');

    $imageLogo = '<img src="' . $url . 'assets/images/AIPRO.svg' .'" style="width:200px; display:block; text-align: left;"/>';
    $message = '
        <!DOCTYPE html>
        <html lang="en">
        <body style="font-family: Arial, sans-serif; line-height: 1.8;">
          <p>Dear valued customer,</p>
          <p>We have detected multiple active sessions on your account, indicating potential unauthorized access or a violation of our Terms of Service. To protect your data, we have temporarily locked your account. It will be reactivated automatically upon your <a href="'. $resetlink .'">password reset.</a></p>
          <p>Security is a top priority at AI-PRO, and our Terms of Service dictate that access to your account data is exclusive to you unless specified otherwise in your subscription plan.</p>
          <p>To ensure security and regulatory compliance, it is crucial for each user to log in with their unique credentials and avoid sharing passwords. For a secure method of providing access to AI-PRO for multiple individuals, we recommend <a href="'. $ent_pricing .'">adding additional users</a> to your account.</a></p>
          '. $resetbutton .'
          <br>
          <p>Thank you for your understanding and cooperation.</p>
          <p>AI-Pro Team</p>
         ' . $imageLogo . '
        </body>
        </html>
    ';

    return $message;
}