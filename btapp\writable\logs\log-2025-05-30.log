INFO - 2025-05-30 06:32:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:32:13 --> btdbFindBy ---> 
DEBUG - 2025-05-30 06:32:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748586733100/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586733100'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748586733100/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586733100'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:14 --> REQ ----------->
INFO - 2025-05-30 06:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:32:24 --> REQ ----------->
INFO - 2025-05-30 06:32:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 06:32:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:32:27 --> btdbFindBy ---> 
DEBUG - 2025-05-30 06:32:27 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748586747504/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:27 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586747504'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:27 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748586747504/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:27 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586747504'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:32:27 --> REQ ----------->
INFO - 2025-05-30 06:32:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:33:04 --> REQ ----------->
INFO - 2025-05-30 06:33:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 06:33:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:33:04 --> btdbFindBy ---> 
DEBUG - 2025-05-30 06:33:04 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748586784696/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:33:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586784696'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:33:04 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748586784696/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:33:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748586784696'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 06:33:04 --> REQ ----------->
INFO - 2025-05-30 06:33:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 06:35:57 --> REQ ----------->
INFO - 2025-05-30 06:35:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 07:10:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:10:51 --> btdbFindBy ---> 
DEBUG - 2025-05-30 07:10:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748589051491/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:10:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589051491'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:10:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748589051491/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:10:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589051491'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:10:51 --> REQ ----------->
INFO - 2025-05-30 07:10:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:18:44 --> REQ ----------->
INFO - 2025-05-30 07:18:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 07:18:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:18:50 --> btdbFindBy ---> 
DEBUG - 2025-05-30 07:18:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748589530234/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589530234'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748589530234/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589530234'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:50 --> REQ ----------->
INFO - 2025-05-30 07:18:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> 
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748589530234/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589530234'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748589530234/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748589530234'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> 
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:18:57 --> REQ ----------->
INFO - 2025-05-30 07:18:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:19:29 --> REQ ----------->
INFO - 2025-05-30 07:19:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 07:38:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:38:29 --> btdbFindBy ---> 
DEBUG - 2025-05-30 07:38:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748590708975/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:38:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748590708975'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:38:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748590708975/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:38:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748590708975'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 07:38:29 --> REQ ----------->
INFO - 2025-05-30 07:38:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:39:34 --> REQ ----------->
INFO - 2025-05-30 07:39:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:40:35 --> REQ ----------->
INFO - 2025-05-30 07:40:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:40:36 --> REQ ----------->
INFO - 2025-05-30 07:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:43:33 --> REQ ----------->
INFO - 2025-05-30 07:43:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 07:43:46 --> REQ ----------->
INFO - 2025-05-30 07:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:52:04 --> REQ ----------->
INFO - 2025-05-30 08:52:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:52:05 --> REQ ----------->
INFO - 2025-05-30 08:52:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:54:06 --> REQ ----------->
INFO - 2025-05-30 08:54:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:54:07 --> REQ ----------->
INFO - 2025-05-30 08:54:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:54:14 --> REQ ----------->
INFO - 2025-05-30 08:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:56:17 --> REQ ----------->
INFO - 2025-05-30 08:56:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:56:18 --> REQ ----------->
INFO - 2025-05-30 08:56:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:56:21 --> REQ ----------->
INFO - 2025-05-30 08:56:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:56:21 --> REQ ----------->
INFO - 2025-05-30 08:56:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 08:58:06 --> REQ ----------->
INFO - 2025-05-30 08:58:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:32 --> REQ ----------->
INFO - 2025-05-30 09:49:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:34 --> REQ ----------->
INFO - 2025-05-30 09:49:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:35 --> REQ ----------->
INFO - 2025-05-30 09:49:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:35 --> REQ ----------->
INFO - 2025-05-30 09:49:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:37 --> REQ ----------->
INFO - 2025-05-30 09:49:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 09:49:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:37 --> btdbFindBy ---> 
DEBUG - 2025-05-30 09:49:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748598577709/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598577709'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748598577709/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598577709'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:38 --> REQ ----------->
INFO - 2025-05-30 09:49:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 09:49:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:41 --> btdbFindBy ---> 
DEBUG - 2025-05-30 09:49:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748598581821/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598581821'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:41 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748598581821/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598581821'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:49:42 --> REQ ----------->
INFO - 2025-05-30 09:49:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:49:44 --> REQ ----------->
INFO - 2025-05-30 09:49:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:51:45 --> REQ ----------->
INFO - 2025-05-30 09:51:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:52:08 --> REQ ----------->
INFO - 2025-05-30 09:52:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-30 09:52:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:52:32 --> btdbFindBy ---> 
DEBUG - 2025-05-30 09:52:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748598752551/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:52:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598752551'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:52:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748598752551/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:52:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598752551'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:52:32 --> REQ ----------->
INFO - 2025-05-30 09:52:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:53:02 --> REQ ----------->
INFO - 2025-05-30 09:53:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> 
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748598752551/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598752551'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748598752551/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748598752551'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> 
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:53:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-30 09:58:53 --> REQ ----------->
INFO - 2025-05-30 09:58:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:58:54 --> REQ ----------->
INFO - 2025-05-30 09:58:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:59:28 --> REQ ----------->
INFO - 2025-05-30 09:59:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:59:29 --> REQ ----------->
INFO - 2025-05-30 09:59:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 09:59:57 --> REQ ----------->
INFO - 2025-05-30 09:59:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-30 10:01:22 --> REQ ----------->
INFO - 2025-05-30 10:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
