import React from "react";
import { motion } from "framer-motion";

function DesktopDropdownMenu({ items, menuItemVariants }) {
  return (
    <motion.div
      className="absolute left-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50"
      role="menu"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.2 }}
    >
      <div className="py-1">
        {items.map((item, index) => (
          <motion.div
            key={item.name}
            variants={menuItemVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            custom={index}
          >
            <a
              href={item.url}
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors border-b border-gray-100 last:border-0"
              role="menuitem"
              target="_blank"
              rel="noopener noreferrer"
            >
              {item.name}
            </a>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}

export default DesktopDropdownMenu;