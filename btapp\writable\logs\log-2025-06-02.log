DEBUG - 2025-06-02 01:11:57 --> REQ ----------->
INFO - 2025-06-02 01:11:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:16:15 --> REQ ----------->
INFO - 2025-06-02 01:16:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:16:23 --> REQ ----------->
INFO - 2025-06-02 01:16:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:16:25 --> REQ ----------->
INFO - 2025-06-02 01:16:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:16:29 --> REQ ----------->
INFO - 2025-06-02 01:16:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:18:32 --> REQ ----------->
INFO - 2025-06-02 01:18:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:19:32 --> REQ ----------->
INFO - 2025-06-02 01:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:20:36 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:20:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:20:41 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:20:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:20:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:20:41 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:20:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:21:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:40:00 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:40:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:40:01 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:40:01 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:40:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:40:01 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:40:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:41:20 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:41:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:41:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:41:21 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:41:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:41:21 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:41:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:42:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:23 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:42:23 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:23 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:42:27 --> REQ ----------->
INFO - 2025-06-02 01:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatpdf-the-revolutionary-ai-tool-that-lets-you-chat-with-your-pdfs'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:43:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:50:16 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:50:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatpdf-the-revolutionary-ai-tool-that-lets-you-chat-with-your-pdfs'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 01:56:24 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:56:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 01:56:24 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Models\WPPostModel.php on line 61.
 1 APPPATH\Models\WPPostModel.php(61): json_decode(null)
 2 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('4963')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-02 01:56:24 --> Attempt to read property "url" on null
in APPPATH\Views\articles\index.php on line 407.
 1 APPPATH\Views\articles\index.php(407): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on null', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 407)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 01:59:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:59:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 01:59:13 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Models\WPPostModel.php on line 61.
 1 APPPATH\Models\WPPostModel.php(61): json_decode(null)
 2 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('4963')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-02 01:59:13 --> Attempt to read property "alt" on null
in APPPATH\Views\articles\index.php on line 409.
 1 APPPATH\Views\articles\index.php(409): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "alt" on null', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 409)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 01:59:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 01:59:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 01:59:43 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Models\WPPostModel.php on line 61.
 1 APPPATH\Models\WPPostModel.php(61): json_decode(null)
 2 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('4963')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-02 01:59:43 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 409.
 1 APPPATH\Views\articles\index.php(409): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:01:23 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:01:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:02:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:02:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:02:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:02:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:03:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:03:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-06-02 02:03:21 --> json_decode(): Argument #1 ($json) must be of type string, stdClass given
in APPPATH\Models\WPPostModel.php on line 44.
 1 APPPATH\Models\WPPostModel.php(44): json_decode(Object(stdClass))
 2 APPPATH\Controllers\Articles.php(36): App\Models\WPPostModel->getPreviousPost('4963')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:04:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:04:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:04:19 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:04:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-06-02 02:04:19 --> Attempt to read property "url" on string
in APPPATH\Views\articles\index.php on line 381.
 1 APPPATH\Views\articles\index.php(381): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on string', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 381)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:04:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:04:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:05:25 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:05:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 02:05:25 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Models\WPPostModel.php on line 61.
 1 APPPATH\Models\WPPostModel.php(61): json_decode(null)
 2 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('4963')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-02 02:05:25 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 409.
 1 APPPATH\Views\articles\index.php(409): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:06:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:06:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:07:00 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:07:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:08:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:08:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-06-02 02:08:15 --> json_decode(): Argument #1 ($json) must be of type string, array given
in APPPATH\Models\WPPostModel.php on line 82.
 1 APPPATH\Models\WPPostModel.php(82): json_decode([...])
 2 APPPATH\Models\WPPostModel.php(40): App\Models\WPPostModel->decodeOgImageMeta([...])
 3 APPPATH\Controllers\Articles.php(36): App\Models\WPPostModel->getPreviousPost('4963')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:08:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:08:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 02:08:45 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 383.
 1 APPPATH\Views\articles\index.php(383): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-02 02:08:45 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 409.
 1 APPPATH\Views\articles\index.php(409): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:08:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:08:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 02:08:57 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 383.
 1 APPPATH\Views\articles\index.php(383): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
WARNING - 2025-06-02 02:08:57 --> [DEPRECATED] mb_convert_encoding(): Passing null to parameter #1 ($string) of type array|string is deprecated in APPPATH\Views\articles\index.php on line 409.
 1 APPPATH\Views\articles\index.php(409): mb_convert_encoding(null, 'UTF-8', 'ISO-8859-1')
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:10:00 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:10:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:12:41 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:12:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:12:53 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:12:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:14:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:14:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:14:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:14:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:16:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:16:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-06-02 02:16:22 --> Undefined property: stdClass::$alt
in APPPATH\Views\articles\index.php on line 412.
 1 APPPATH\Views\articles\index.php(412): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$alt', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 412)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 02:16:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:16:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:17:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:17:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:19:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:19:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'microsoft-announces-to-add-chatgpt-to-azure-openai-services-soon'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:17 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:33:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'glimpsing-tomorrow-the-fascinating-future-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:33:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'glimpsing-tomorrow-the-fascinating-future-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:33:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:33:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'glimpsing-tomorrow-the-fascinating-future-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:34:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'glimpsing-tomorrow-the-fascinating-future-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 02:37:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:55:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:56:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:56:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:56:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:56:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:56:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:56:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:56:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:59:27 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:59:27 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:59:28 --> btdbFindBy ---> 
DEBUG - 2025-06-02 04:59:28 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:59:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:59:28 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 04:59:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:00:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:00:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:00:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:00:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:00:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:15:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:15:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'co-creator-of-stable-diffusion-releases-ai-that-generates-videos-from-text'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:15:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:57:26 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:57:27 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:57:27 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'introduction-to-dream-studio-by-stable-diffusion'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 05:57:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 05:57:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'introduction-to-dream-studio-by-stable-diffusion'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:10:51 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:10:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:10:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:10:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:10:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/global.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'global.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/global.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'global.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style-index.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style-index.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style-index.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style-index.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/frontend.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/frontend.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/templates.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'templates.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/templates.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'templates.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/elementor-icons.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'elementor-icons.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/elementor-icons.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'elementor-icons.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/custom-frontend-lite.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'custom-frontend-lite.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/custom-frontend-lite.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'custom-frontend-lite.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/post-2410.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'post-2410.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/post-2410.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'post-2410.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/post-3122.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'post-3122.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/post-3122.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'post-3122.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/main.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/main.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/main.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/main.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/back-to-top.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'back-to-top.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/back-to-top.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'back-to-top.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/elementor-frontend.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'elementor-frontend.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/elementor-frontend.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'elementor-frontend.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/stackable.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'stackable.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/stackable.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'stackable.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wpforms.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wpforms.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wpforms.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wpforms.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/general.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'general.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/general.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'general.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/frontend.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/frontend.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/js.cookie.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'js.cookie.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/js.cookie.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'js.cookie.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-ui.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-ui.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-ui.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-ui.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/functions2.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'functions2.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/functions2.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'functions2.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/analytics.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'analytics.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/analytics.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'analytics.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/bootstrap.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'bootstrap.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/bootstrap.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'bootstrap.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/main.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/main.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'main.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/general.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'general.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/general.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'general.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/webpack.runtime.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'webpack.runtime.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/webpack.runtime.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'webpack.runtime.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/frontend-modules.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend-modules.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/frontend-modules.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend-modules.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/waypoints.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'waypoints.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/waypoints.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'waypoints.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/frontend.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/frontend.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/frontend.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/frontend.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'frontend.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/dialog.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'dialog.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/dialog.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'dialog.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/share-link.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'share-link.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/share-link.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'share-link.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/AIPRO-200x45-1.svg/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'AIPRO-200x45-1.svg'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/AIPRO-200x45-1.svg/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'AIPRO-200x45-1.svg'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/3d-image-1.gif/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-1.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/3d-image-1.gif/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-1.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/3d-image-3.gif/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-3.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/3d-image-3.gif/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-3.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/3d-image-2.gif/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-2.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/3d-image-2.gif/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '3d-image-2.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/chatgpt-conversation-long-light-comp-150x.gif/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatgpt-conversation-long-light-comp-150x.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/chatgpt-conversation-long-light-comp-150x.gif/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatgpt-conversation-long-light-comp-150x.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/chatgpt-chat-1.gif/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatgpt-chat-1.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/chatgpt-chat-1.gif/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatgpt-chat-1.gif'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cropped-ai-logo-square-black-32x32.png/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cropped-ai-logo-square-black-32x32.png'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cropped-ai-logo-square-black-32x32.png/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cropped-ai-logo-square-black-32x32.png'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cropped-ai-logo-square-black-192x192.png/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cropped-ai-logo-square-black-192x192.png'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cropped-ai-logo-square-black-192x192.png/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:11:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cropped-ai-logo-square-black-192x192.png'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:13:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:15:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:38 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/style.min.css/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/style.min.css/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'style.min.css'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/jquery-migrate.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/jquery-migrate.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'jquery-migrate.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/Action.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/Action.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'Action.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/core.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/core.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'core.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/underscore.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/underscore.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'underscore.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/wp-util.min.js/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/wp-util.min.js/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 06:17:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'wp-util.min.js'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'revolutionizing-conversational-ai-an-in-depth-look-at-chatgpt-3-5-and-chatgpt-4-0'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:02:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:07:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:07:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:07:03 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
CRITICAL - 2025-06-02 07:07:03 --> Attempt to read property "type" on null
in APPPATH\Controllers\Articles.php on line 58.
 1 APPPATH\Controllers\Articles.php(58): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "type" on null', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 58)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:08:02 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:08:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:08:02 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:08:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:08:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:08:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:08:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:08:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:10:03 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:16 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:10:16 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:10:17 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:17 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:17 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:10:32 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:10:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:10:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:10:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:12:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:12:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:12:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:13:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:13:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:13:40 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:13:40 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:13:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:13:40 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:13:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:28 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:14:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:29 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:14:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:14:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-proven-ways-to-use-chatgpt-to-make-money'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
WARNING - 2025-06-02 07:15:07 --> [DEPRECATED] json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): json_decode(null)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', '25-proven-ways-to-use-chatgpt-to-make-money')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:15:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:18:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:18:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:18:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-viral-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:18:36 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:18:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:18:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:18:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:18:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:29:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:29:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'chatpdf-the-revolutionary-ai-tool-that-lets-you-chat-with-your-pdfs'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:34:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:34:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:34:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:09 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:35:09 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:09 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:35:21 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:21 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:35:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:07 --> REQ ----------->
INFO - 2025-06-02 07:36:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:36:38 --> REQ ----------->
INFO - 2025-06-02 07:36:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:36:38 --> REQ ----------->
INFO - 2025-06-02 07:36:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:36:38 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:39 --> REQ ----------->
INFO - 2025-06-02 07:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-06-02 07:36:42 --> syntax error, unexpected identifier "s"
in APPPATH\Views\articles\index.php on line 666.
 1 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 2 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 3 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('tutorials', 'most-common-chatgpt-prompts')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:36:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:08 --> REQ ----------->
INFO - 2025-06-02 07:37:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:09 --> REQ ----------->
INFO - 2025-06-02 07:37:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:35 --> REQ ----------->
INFO - 2025-06-02 07:37:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:42 --> REQ ----------->
INFO - 2025-06-02 07:37:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:43 --> REQ ----------->
INFO - 2025-06-02 07:37:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:44 --> REQ ----------->
INFO - 2025-06-02 07:37:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:44 --> REQ ----------->
INFO - 2025-06-02 07:37:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:49 --> REQ ----------->
INFO - 2025-06-02 07:37:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:37:50 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:51 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:37:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:49 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:38:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:50 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:38:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:38:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:34 --> REQ ----------->
INFO - 2025-06-02 07:39:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:39:40 --> REQ ----------->
INFO - 2025-06-02 07:39:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:39:47 --> REQ ----------->
INFO - 2025-06-02 07:39:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 07:39:50 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-action-figures-create-your-own-custom-toy-with-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:50 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-action-figures-create-your-own-custom-toy-with-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:51 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'most-common-chatgpt-prompts'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> 
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 07:39:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:33:05 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:33:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:33:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:33:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:33:20 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:33:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:37:28 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:37:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-for-teachers-to-enhance-teaching-skills'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:39:21 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:39:21 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:39:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:39:21 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:39:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-for-teachers-to-enhance-teaching-skills'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:43:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-06-02 08:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 08:54:09 --> btdbFindBy ---> 
DEBUG - 2025-06-02 08:54:09 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748854449798/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:54:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748854449798'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:54:09 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748854449798/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:54:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748854449798'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 08:54:10 --> REQ ----------->
INFO - 2025-06-02 08:54:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 08:55:43 --> REQ ----------->
INFO - 2025-06-02 08:55:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 08:56:10 --> REQ ----------->
INFO - 2025-06-02 08:56:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 08:56:12 --> REQ ----------->
INFO - 2025-06-02 08:56:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 08:56:21 --> REQ ----------->
INFO - 2025-06-02 08:56:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:00:47 --> REQ ----------->
INFO - 2025-06-02 09:00:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:00:48 --> REQ ----------->
INFO - 2025-06-02 09:00:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:00:51 --> REQ ----------->
INFO - 2025-06-02 09:00:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:01:09 --> REQ ----------->
INFO - 2025-06-02 09:01:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:01:17 --> REQ ----------->
INFO - 2025-06-02 09:01:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:01:33 --> REQ ----------->
INFO - 2025-06-02 09:01:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748854449798/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748854449798'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748854449798/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748854449798'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:01:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:02:06 --> REQ ----------->
INFO - 2025-06-02 09:02:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:02:41 --> REQ ----------->
INFO - 2025-06-02 09:02:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:02:52 --> REQ ----------->
INFO - 2025-06-02 09:02:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:03:05 --> REQ ----------->
INFO - 2025-06-02 09:03:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:03:24 --> REQ ----------->
INFO - 2025-06-02 09:03:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:03:31 --> REQ ----------->
INFO - 2025-06-02 09:03:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:03:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:03:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:03:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748855014485/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:03:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748855014485'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:03:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748855014485/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:03:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748855014485'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:03:34 --> REQ ----------->
INFO - 2025-06-02 09:03:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:04:26 --> REQ ----------->
INFO - 2025-06-02 09:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:04 --> REQ ----------->
INFO - 2025-06-02 09:07:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:11 --> REQ ----------->
INFO - 2025-06-02 09:07:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:15 --> REQ ----------->
INFO - 2025-06-02 09:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:19 --> REQ ----------->
INFO - 2025-06-02 09:07:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:21 --> REQ ----------->
INFO - 2025-06-02 09:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:22 --> REQ ----------->
INFO - 2025-06-02 09:07:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:27 --> REQ ----------->
INFO - 2025-06-02 09:07:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:30 --> REQ ----------->
INFO - 2025-06-02 09:07:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:33 --> REQ ----------->
INFO - 2025-06-02 09:07:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:42 --> REQ ----------->
INFO - 2025-06-02 09:07:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:07:54 --> REQ ----------->
INFO - 2025-06-02 09:07:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:08:20 --> REQ ----------->
INFO - 2025-06-02 09:08:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:13:50 --> REQ ----------->
INFO - 2025-06-02 09:13:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:13:51 --> REQ ----------->
INFO - 2025-06-02 09:13:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:13:52 --> REQ ----------->
INFO - 2025-06-02 09:13:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:13:54 --> REQ ----------->
INFO - 2025-06-02 09:13:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748855014485/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748855014485'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748855014485/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748855014485'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:13:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:14:00 --> REQ ----------->
INFO - 2025-06-02 09:14:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:08 --> REQ ----------->
INFO - 2025-06-02 09:14:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:19 --> REQ ----------->
INFO - 2025-06-02 09:14:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:25 --> REQ ----------->
INFO - 2025-06-02 09:14:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:40 --> REQ ----------->
INFO - 2025-06-02 09:14:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:49 --> REQ ----------->
INFO - 2025-06-02 09:14:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:49 --> REQ ----------->
INFO - 2025-06-02 09:14:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:14:53 --> REQ ----------->
INFO - 2025-06-02 09:14:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:16:02 --> REQ ----------->
INFO - 2025-06-02 09:16:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:16:09 --> REQ ----------->
INFO - 2025-06-02 09:16:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:17:02 --> REQ ----------->
INFO - 2025-06-02 09:17:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:17:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:17:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:17:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:17:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:17:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:17:09 --> REQ ----------->
INFO - 2025-06-02 09:17:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:20:36 --> REQ ----------->
INFO - 2025-06-02 09:20:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:20:37 --> REQ ----------->
INFO - 2025-06-02 09:20:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:20:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856037895/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856037895'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856037895/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856037895'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:20:38 --> REQ ----------->
INFO - 2025-06-02 09:20:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856078009/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856078009'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856078009/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856078009'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:18 --> REQ ----------->
INFO - 2025-06-02 09:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:21:33 --> REQ ----------->
INFO - 2025-06-02 09:21:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:21:39 --> REQ ----------->
INFO - 2025-06-02 09:21:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:21:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:21:39 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:21:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:40 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:21:40 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856099899/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856099899'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:40 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856099899/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856099899'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:21:40 --> REQ ----------->
INFO - 2025-06-02 09:21:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:22:01 --> REQ ----------->
INFO - 2025-06-02 09:22:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:23:05 --> REQ ----------->
INFO - 2025-06-02 09:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:23:45 --> REQ ----------->
INFO - 2025-06-02 09:23:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:23:55 --> REQ ----------->
INFO - 2025-06-02 09:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856235401/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856235401'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856235401/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856235401'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:23:55 --> REQ ----------->
INFO - 2025-06-02 09:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:23:59 --> REQ ----------->
INFO - 2025-06-02 09:23:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:28:21 --> REQ ----------->
INFO - 2025-06-02 09:28:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:29:13 --> REQ ----------->
INFO - 2025-06-02 09:29:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:29:13 --> REQ ----------->
INFO - 2025-06-02 09:29:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:29:25 --> REQ ----------->
INFO - 2025-06-02 09:29:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:30:03 --> REQ ----------->
INFO - 2025-06-02 09:30:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:30:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856607893/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856607893'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856607893/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856607893'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:30:08 --> REQ ----------->
INFO - 2025-06-02 09:30:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:30:14 --> REQ ----------->
INFO - 2025-06-02 09:30:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:31:59 --> REQ ----------->
INFO - 2025-06-02 09:31:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:32:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856731999/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856731999'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856731999/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856731999'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:32:12 --> REQ ----------->
INFO - 2025-06-02 09:32:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:34:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856854824/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856854824'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856854824/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856854824'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:15 --> REQ ----------->
INFO - 2025-06-02 09:34:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:34:20 --> REQ ----------->
INFO - 2025-06-02 09:34:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:34:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856862072/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856862072'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856862072/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856862072'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:34:22 --> REQ ----------->
INFO - 2025-06-02 09:34:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:35:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856912531/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856912531'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856912531/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856912531'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:12 --> REQ ----------->
INFO - 2025-06-02 09:35:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:35:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856915779/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856915779'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856915779/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856915779'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:16 --> REQ ----------->
INFO - 2025-06-02 09:35:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 09:35:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> 
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748856919083/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856919083'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748856919083/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748856919083'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 09:35:19 --> REQ ----------->
INFO - 2025-06-02 09:35:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:23 --> REQ ----------->
INFO - 2025-06-02 09:35:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:24 --> REQ ----------->
INFO - 2025-06-02 09:35:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:35:27 --> REQ ----------->
INFO - 2025-06-02 09:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:37:06 --> REQ ----------->
INFO - 2025-06-02 09:37:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:37:09 --> REQ ----------->
INFO - 2025-06-02 09:37:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:41:06 --> REQ ----------->
INFO - 2025-06-02 09:41:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:41:08 --> REQ ----------->
INFO - 2025-06-02 09:41:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:42:18 --> REQ ----------->
INFO - 2025-06-02 09:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:42:52 --> REQ ----------->
INFO - 2025-06-02 09:42:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:42:54 --> REQ ----------->
INFO - 2025-06-02 09:42:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:43:15 --> REQ ----------->
INFO - 2025-06-02 09:43:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:43:48 --> REQ ----------->
INFO - 2025-06-02 09:43:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:44:08 --> REQ ----------->
INFO - 2025-06-02 09:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:44:32 --> REQ ----------->
INFO - 2025-06-02 09:44:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:45:08 --> REQ ----------->
INFO - 2025-06-02 09:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:45:14 --> REQ ----------->
INFO - 2025-06-02 09:45:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:45:15 --> REQ ----------->
INFO - 2025-06-02 09:45:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:49:00 --> REQ ----------->
INFO - 2025-06-02 09:49:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:49:01 --> REQ ----------->
INFO - 2025-06-02 09:49:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:49:08 --> REQ ----------->
INFO - 2025-06-02 09:49:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 09:49:12 --> REQ ----------->
INFO - 2025-06-02 09:49:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:31 --> REQ ----------->
INFO - 2025-06-02 10:21:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:33 --> REQ ----------->
INFO - 2025-06-02 10:21:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:21:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:34 --> REQ ----------->
INFO - 2025-06-02 10:21:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748859694391/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859694391'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748859694391/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859694391'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:21:34 --> REQ ----------->
INFO - 2025-06-02 10:21:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:44 --> REQ ----------->
INFO - 2025-06-02 10:21:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:53 --> REQ ----------->
INFO - 2025-06-02 10:21:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:21:56 --> REQ ----------->
INFO - 2025-06-02 10:21:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:22:03 --> REQ ----------->
INFO - 2025-06-02 10:22:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:22:10 --> REQ ----------->
INFO - 2025-06-02 10:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:22:14 --> REQ ----------->
INFO - 2025-06-02 10:22:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:23:38 --> REQ ----------->
INFO - 2025-06-02 10:23:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:23:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748859825103/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859825103'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748859825103/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859825103'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:45 --> REQ ----------->
INFO - 2025-06-02 10:23:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:23:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748859832663/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859832663'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748859832663/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859832663'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:53 --> REQ ----------->
INFO - 2025-06-02 10:23:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:23:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748859837358/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859837358'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748859837358/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748859837358'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:23:57 --> REQ ----------->
INFO - 2025-06-02 10:23:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:24:08 --> REQ ----------->
INFO - 2025-06-02 10:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:24:18 --> REQ ----------->
INFO - 2025-06-02 10:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:24:19 --> REQ ----------->
INFO - 2025-06-02 10:24:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:25:31 --> REQ ----------->
INFO - 2025-06-02 10:25:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:26:18 --> REQ ----------->
INFO - 2025-06-02 10:26:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:26:19 --> REQ ----------->
INFO - 2025-06-02 10:26:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:28:11 --> REQ ----------->
INFO - 2025-06-02 10:28:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:28:27 --> REQ ----------->
INFO - 2025-06-02 10:28:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:29:56 --> REQ ----------->
INFO - 2025-06-02 10:29:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:30:00 --> REQ ----------->
INFO - 2025-06-02 10:30:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:35:56 --> REQ ----------->
INFO - 2025-06-02 10:35:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:35:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748860556371/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860556371'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748860556371/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860556371'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:35:57 --> REQ ----------->
INFO - 2025-06-02 10:35:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:36:07 --> REQ ----------->
INFO - 2025-06-02 10:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:36:20 --> REQ ----------->
INFO - 2025-06-02 10:36:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:36:22 --> REQ ----------->
INFO - 2025-06-02 10:36:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:36:32 --> REQ ----------->
INFO - 2025-06-02 10:36:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:43:09 --> REQ ----------->
INFO - 2025-06-02 10:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:43:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:43:11 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:43:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:12 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:43:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748860991458/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860991458'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748860991458/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860991458'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:12 --> REQ ----------->
INFO - 2025-06-02 10:43:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:43:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748860997397/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860997397'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748860997397/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748860997397'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:43:17 --> REQ ----------->
INFO - 2025-06-02 10:43:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:43:24 --> REQ ----------->
INFO - 2025-06-02 10:43:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:43:54 --> REQ ----------->
INFO - 2025-06-02 10:43:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:06 --> REQ ----------->
INFO - 2025-06-02 10:44:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:12 --> REQ ----------->
INFO - 2025-06-02 10:44:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:25 --> REQ ----------->
INFO - 2025-06-02 10:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:30 --> REQ ----------->
INFO - 2025-06-02 10:44:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:34 --> REQ ----------->
INFO - 2025-06-02 10:44:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:37 --> REQ ----------->
INFO - 2025-06-02 10:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:46 --> REQ ----------->
INFO - 2025-06-02 10:44:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:44:51 --> REQ ----------->
INFO - 2025-06-02 10:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:46:14 --> REQ ----------->
INFO - 2025-06-02 10:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:48:13 --> REQ ----------->
INFO - 2025-06-02 10:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:48:14 --> REQ ----------->
INFO - 2025-06-02 10:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:48:54 --> REQ ----------->
INFO - 2025-06-02 10:48:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:48:56 --> REQ ----------->
INFO - 2025-06-02 10:48:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:02 --> REQ ----------->
INFO - 2025-06-02 10:49:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:08 --> REQ ----------->
INFO - 2025-06-02 10:49:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:40 --> REQ ----------->
INFO - 2025-06-02 10:49:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:52 --> REQ ----------->
INFO - 2025-06-02 10:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748861392641/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861392641'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748861392641/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861392641'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:49:52 --> REQ ----------->
INFO - 2025-06-02 10:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:49:59 --> REQ ----------->
INFO - 2025-06-02 10:49:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748861403000/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861403000'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748861403000/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861403000'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:50:03 --> REQ ----------->
INFO - 2025-06-02 10:50:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:50:07 --> REQ ----------->
INFO - 2025-06-02 10:50:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:51:51 --> REQ ----------->
INFO - 2025-06-02 10:51:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:51:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748861516207/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861516207'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748861516207/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861516207'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:51:56 --> REQ ----------->
INFO - 2025-06-02 10:51:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:52:10 --> REQ ----------->
INFO - 2025-06-02 10:52:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:52:26 --> REQ ----------->
INFO - 2025-06-02 10:52:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:52:41 --> REQ ----------->
INFO - 2025-06-02 10:52:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:57:18 --> REQ ----------->
INFO - 2025-06-02 10:57:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 10:57:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:57:29 --> REQ ----------->
INFO - 2025-06-02 10:57:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> 
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748861849725/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861849725'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748861849725/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748861849725'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 10:57:30 --> REQ ----------->
INFO - 2025-06-02 10:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:11:40 --> REQ ----------->
INFO - 2025-06-02 11:11:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:11:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:11:42 --> REQ ----------->
INFO - 2025-06-02 11:11:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748862702662/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862702662'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748862702662/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862702662'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:43 --> REQ ----------->
INFO - 2025-06-02 11:11:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:11:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:11:48 --> REQ ----------->
INFO - 2025-06-02 11:11:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748862708566/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862708566'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748862708566/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862708566'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:11:49 --> REQ ----------->
INFO - 2025-06-02 11:11:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:12:17 --> REQ ----------->
INFO - 2025-06-02 11:12:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:15:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:15:11 --> REQ ----------->
INFO - 2025-06-02 11:15:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748862911243/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862911243'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748862911243/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748862911243'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:15:11 --> REQ ----------->
INFO - 2025-06-02 11:15:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:16:17 --> REQ ----------->
INFO - 2025-06-02 11:16:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:16:18 --> REQ ----------->
INFO - 2025-06-02 11:16:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:16:23 --> REQ ----------->
INFO - 2025-06-02 11:16:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:16:39 --> REQ ----------->
INFO - 2025-06-02 11:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:16:45 --> REQ ----------->
INFO - 2025-06-02 11:16:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:17:36 --> REQ ----------->
INFO - 2025-06-02 11:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:17:37 --> REQ ----------->
INFO - 2025-06-02 11:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:17:56 --> REQ ----------->
INFO - 2025-06-02 11:17:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:18:06 --> REQ ----------->
INFO - 2025-06-02 11:18:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:18:07 --> REQ ----------->
INFO - 2025-06-02 11:18:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:18:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:18:09 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:18:09 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:09 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:10 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:18:10 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863089997/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863089997'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:10 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863089997/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863089997'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:10 --> REQ ----------->
INFO - 2025-06-02 11:18:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863094356/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863094356'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863094356/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863094356'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:18:14 --> REQ ----------->
INFO - 2025-06-02 11:18:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:21:40 --> REQ ----------->
INFO - 2025-06-02 11:21:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:21:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:21:42 --> REQ ----------->
INFO - 2025-06-02 11:21:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863302257/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863302257'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863302257/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863302257'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:21:42 --> REQ ----------->
INFO - 2025-06-02 11:21:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:21:52 --> REQ ----------->
INFO - 2025-06-02 11:21:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:21:54 --> REQ ----------->
INFO - 2025-06-02 11:21:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:22:16 --> REQ ----------->
INFO - 2025-06-02 11:22:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:22:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863336617/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863336617'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863336617/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863336617'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:22:16 --> REQ ----------->
INFO - 2025-06-02 11:22:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:22:23 --> REQ ----------->
INFO - 2025-06-02 11:22:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:22:36 --> REQ ----------->
INFO - 2025-06-02 11:22:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:23:05 --> REQ ----------->
INFO - 2025-06-02 11:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:24:14 --> REQ ----------->
INFO - 2025-06-02 11:24:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:25:33 --> REQ ----------->
INFO - 2025-06-02 11:25:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:25:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863536631/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863536631'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863536631/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863536631'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:25:37 --> REQ ----------->
INFO - 2025-06-02 11:25:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:26:17 --> REQ ----------->
INFO - 2025-06-02 11:26:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:26:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:26:19 --> REQ ----------->
INFO - 2025-06-02 11:26:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863579214/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863579214'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863579214/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863579214'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:26:19 --> REQ ----------->
INFO - 2025-06-02 11:26:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:26:22 --> REQ ----------->
INFO - 2025-06-02 11:26:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:32:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:32:43 --> REQ ----------->
INFO - 2025-06-02 11:32:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748863963856/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863963856'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748863963856/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748863963856'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:32:44 --> REQ ----------->
INFO - 2025-06-02 11:32:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:33:24 --> REQ ----------->
INFO - 2025-06-02 11:33:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:33:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864005040/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864005040'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864005040/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864005040'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:33:25 --> REQ ----------->
INFO - 2025-06-02 11:33:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:34:14 --> REQ ----------->
INFO - 2025-06-02 11:34:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:34:17 --> REQ ----------->
INFO - 2025-06-02 11:34:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:34:25 --> REQ ----------->
INFO - 2025-06-02 11:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864065263/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864065263'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864065263/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864065263'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:34:25 --> REQ ----------->
INFO - 2025-06-02 11:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:34:32 --> REQ ----------->
INFO - 2025-06-02 11:34:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:36:17 --> REQ ----------->
INFO - 2025-06-02 11:36:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:36:19 --> REQ ----------->
INFO - 2025-06-02 11:36:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:40:59 --> REQ ----------->
INFO - 2025-06-02 11:40:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:06 --> REQ ----------->
INFO - 2025-06-02 11:41:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:41:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864468560/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864468560'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864468560/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864468560'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:09 --> REQ ----------->
INFO - 2025-06-02 11:41:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:21 --> REQ ----------->
INFO - 2025-06-02 11:41:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:41:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:31 --> REQ ----------->
INFO - 2025-06-02 11:41:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:31 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864491975/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864491975'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864491975/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864491975'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:32 --> REQ ----------->
INFO - 2025-06-02 11:41:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:36 --> REQ ----------->
INFO - 2025-06-02 11:41:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:41:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:48 --> REQ ----------->
INFO - 2025-06-02 11:41:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:48 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:49 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:41:49 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864508953/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864508953'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:49 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864508953/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864508953'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:41:49 --> REQ ----------->
INFO - 2025-06-02 11:41:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:41:59 --> REQ ----------->
INFO - 2025-06-02 11:41:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:42:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:01 --> REQ ----------->
INFO - 2025-06-02 11:42:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864521722/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864521722'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864521722/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864521722'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:42:02 --> REQ ----------->
INFO - 2025-06-02 11:42:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:06 --> REQ ----------->
INFO - 2025-06-02 11:42:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:19 --> REQ ----------->
INFO - 2025-06-02 11:42:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:41 --> REQ ----------->
INFO - 2025-06-02 11:42:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:42:58 --> REQ ----------->
INFO - 2025-06-02 11:42:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:43:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:06 --> REQ ----------->
INFO - 2025-06-02 11:43:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864586527/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864586527'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864586527/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864586527'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:06 --> REQ ----------->
INFO - 2025-06-02 11:43:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:29 --> REQ ----------->
INFO - 2025-06-02 11:43:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:43:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:35 --> REQ ----------->
INFO - 2025-06-02 11:43:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:35 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:36 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864615939/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864615939'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864615939/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864615939'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:36 --> REQ ----------->
INFO - 2025-06-02 11:43:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:47 --> REQ ----------->
INFO - 2025-06-02 11:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864627783/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864627783'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864627783/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864627783'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:43:48 --> REQ ----------->
INFO - 2025-06-02 11:43:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:43:54 --> REQ ----------->
INFO - 2025-06-02 11:43:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:05 --> REQ ----------->
INFO - 2025-06-02 11:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864645469/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864645469'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864645469/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864645469'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:44:05 --> REQ ----------->
INFO - 2025-06-02 11:44:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:08 --> REQ ----------->
INFO - 2025-06-02 11:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:15 --> REQ ----------->
INFO - 2025-06-02 11:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:34 --> REQ ----------->
INFO - 2025-06-02 11:44:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:35 --> REQ ----------->
INFO - 2025-06-02 11:44:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:44:58 --> REQ ----------->
INFO - 2025-06-02 11:44:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:02 --> REQ ----------->
INFO - 2025-06-02 11:46:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:12 --> REQ ----------->
INFO - 2025-06-02 11:46:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:17 --> REQ ----------->
INFO - 2025-06-02 11:46:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:28 --> REQ ----------->
INFO - 2025-06-02 11:46:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:29 --> REQ ----------->
INFO - 2025-06-02 11:46:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:53 --> REQ ----------->
INFO - 2025-06-02 11:46:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748864645469/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864645469'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748864645469/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748864645469'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:46:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:51:16 --> REQ ----------->
INFO - 2025-06-02 11:51:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:51:49 --> REQ ----------->
INFO - 2025-06-02 11:51:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:51:50 --> REQ ----------->
INFO - 2025-06-02 11:51:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:51:52 --> REQ ----------->
INFO - 2025-06-02 11:51:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:51:53 --> REQ ----------->
INFO - 2025-06-02 11:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:52:20 --> REQ ----------->
INFO - 2025-06-02 11:52:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:52:21 --> REQ ----------->
INFO - 2025-06-02 11:52:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:53:13 --> REQ ----------->
INFO - 2025-06-02 11:53:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:53:17 --> REQ ----------->
INFO - 2025-06-02 11:53:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:54:17 --> REQ ----------->
INFO - 2025-06-02 11:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:55:19 --> REQ ----------->
INFO - 2025-06-02 11:55:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:55:28 --> REQ ----------->
INFO - 2025-06-02 11:55:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:55:28 --> REQ ----------->
INFO - 2025-06-02 11:55:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:05 --> REQ ----------->
INFO - 2025-06-02 11:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:08 --> REQ ----------->
INFO - 2025-06-02 11:58:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 11:58:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> 
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748865490380/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865490380'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748865490380/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865490380'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 11:58:11 --> REQ ----------->
INFO - 2025-06-02 11:58:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:14 --> REQ ----------->
INFO - 2025-06-02 11:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:21 --> REQ ----------->
INFO - 2025-06-02 11:58:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:28 --> REQ ----------->
INFO - 2025-06-02 11:58:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:58:32 --> REQ ----------->
INFO - 2025-06-02 11:58:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 11:59:55 --> REQ ----------->
INFO - 2025-06-02 11:59:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:00:46 --> REQ ----------->
INFO - 2025-06-02 12:00:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:00:57 --> REQ ----------->
INFO - 2025-06-02 12:00:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748865659520/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865659520'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748865659520/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865659520'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:00:59 --> REQ ----------->
INFO - 2025-06-02 12:00:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748865663450/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865663450'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748865663450/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865663450'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:01:03 --> REQ ----------->
INFO - 2025-06-02 12:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:03:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748865835134/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865835134'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748865835134/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865835134'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:55 --> REQ ----------->
INFO - 2025-06-02 12:03:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:03:56 --> REQ ----------->
INFO - 2025-06-02 12:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748865836102/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865836102'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748865836102/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748865836102'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:03:56 --> REQ ----------->
INFO - 2025-06-02 12:03:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:03:59 --> REQ ----------->
INFO - 2025-06-02 12:03:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:04:02 --> REQ ----------->
INFO - 2025-06-02 12:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:07:40 --> REQ ----------->
INFO - 2025-06-02 12:07:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:07:45 --> REQ ----------->
INFO - 2025-06-02 12:07:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:11:08 --> REQ ----------->
INFO - 2025-06-02 12:11:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:11:40 --> REQ ----------->
INFO - 2025-06-02 12:11:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:10 --> REQ ----------->
INFO - 2025-06-02 12:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:26 --> REQ ----------->
INFO - 2025-06-02 12:22:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:27 --> REQ ----------->
INFO - 2025-06-02 12:22:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:29 --> REQ ----------->
INFO - 2025-06-02 12:22:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:22:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:32 --> REQ ----------->
INFO - 2025-06-02 12:22:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748866952786/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748866952786'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748866952786/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748866952786'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:33 --> REQ ----------->
INFO - 2025-06-02 12:22:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:22:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:37 --> REQ ----------->
INFO - 2025-06-02 12:22:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748866957177/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748866957177'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748866957177/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748866957177'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:22:37 --> REQ ----------->
INFO - 2025-06-02 12:22:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:22:45 --> REQ ----------->
INFO - 2025-06-02 12:22:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:23:04 --> REQ ----------->
INFO - 2025-06-02 12:23:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:23:24 --> REQ ----------->
INFO - 2025-06-02 12:23:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:23:51 --> REQ ----------->
INFO - 2025-06-02 12:23:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:23:55 --> REQ ----------->
INFO - 2025-06-02 12:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748867035461/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867035461'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748867035461/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867035461'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:23:55 --> REQ ----------->
INFO - 2025-06-02 12:23:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:12 --> REQ ----------->
INFO - 2025-06-02 12:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:12 --> REQ ----------->
INFO - 2025-06-02 12:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:20 --> REQ ----------->
INFO - 2025-06-02 12:24:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-02 12:24:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:22 --> REQ ----------->
INFO - 2025-06-02 12:24:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:22 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:24:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748867062144/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867062144'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748867062144/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867062144'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:22 --> REQ ----------->
INFO - 2025-06-02 12:24:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:46 --> REQ ----------->
INFO - 2025-06-02 12:24:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748867062144/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867062144'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748867062144/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748867062144'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/com.chrome.devtools.json/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/com.chrome.devtools.json/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:24:54 --> REQ ----------->
INFO - 2025-06-02 12:24:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:26:34 --> REQ ----------->
INFO - 2025-06-02 12:26:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:30:33 --> REQ ----------->
INFO - 2025-06-02 12:30:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:30:47 --> btdbFindBy ---> 
DEBUG - 2025-06-02 12:30:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-for-teachers-to-enhance-teaching-skills'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-06-02 12:31:39 --> REQ ----------->
INFO - 2025-06-02 12:31:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:32:15 --> REQ ----------->
INFO - 2025-06-02 12:32:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:33:39 --> REQ ----------->
INFO - 2025-06-02 12:33:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:36:22 --> REQ ----------->
INFO - 2025-06-02 12:36:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:37:12 --> REQ ----------->
INFO - 2025-06-02 12:37:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-02 12:37:12 --> REQ ----------->
INFO - 2025-06-02 12:37:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
