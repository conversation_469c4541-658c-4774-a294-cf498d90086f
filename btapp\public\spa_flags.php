<?php

// Add current domain to the domains array
$current_domain = $_SERVER['HTTP_HOST'];
$domains = array('.ai-pro.org', 'ai-pro.org'); // Both root domain formats

// Add current domain if it's not already in the array
if (!in_array($current_domain, $domains)) {
    $domains[] = $current_domain;
}


$parameters = array(
    'ppg',
    'keyword',
    'creative',
    'emailid',
    'adid',
    'mode',
    'flow',
    'splash',
    'pmt',
    'gateway',
    'force_gateway',
    'swipe',
    'qW1eMlya',
    'via',
    'vprice',
    'tp_reviews',
    'members',
    'reg',
    'regRedirectWP',
    'WcvYPABR',
    'reg_google',
    'kt8typtb',
    'cta_clr_lp',
    'cta_pmt',
    'poweredby',
    'mobDisplayPlan',
    'enterprise',
    'cmpny',
    'r_flow',
    'locales',
    'f_sle',
    'verify',
    'email_login',
    'login_token',
    'emailopt',
    'p_toggle',
    'mode_test_no_pcheck',
    'force_pmt',
    'rdct',
    'price_click',
    'reg_apple',
    'vwo',
    'pp_ctaclr',
    'pp_cta',
    'chatpdf',
    'cpdflink',
    'click_id',
    'daily',
    'acwp_ux',
    'rec2',
    'ty_rdct',
    'desc_align',
    'flux_app',
);

// Get the current request URI
$request_uri = $_SERVER['REQUEST_URI'];

// Define restricted URLs
$restricted_paths = [
    '/get-started/compare-plans',
    '/get-started/checkout',
    '/landing/pricing',
    '/landing/pay'];

// Check if the current URL matches the restricted paths
$is_restricted = false;
foreach ($restricted_paths as $path) {
    if (strpos($request_uri, $path) === 0) {
        $is_restricted = true;
        break;
    }
}

$allowed_flags = [
    '/ai-chat',
    '/ai-toolbox-enterprise-plan',
    '/start-chat-gpt-now',
    '/start-chat-gpt-4',
    '/start-chat-gpt',
    '/chatpdf-gpt',
    '/start-claude-ai',
    '/start-dall-e',
    '/start-stable-diffusion',
    '/landing-image',
    '/landing-aiart',
    '/landing-imagegen',
    '/landing',
    '/compare-plans',
    '/start-chat-gpt-c',
    '/start-dall-e-c',
    '/start-claude-ai-c',
    '/create-ai-content',
    '/get-started',
    '/flux-start',
    '/landing-image/register',
    '/landing-aiart/register',
    '/landing-imagegen/register',
    '/landing/register',
    '/',
    '/get-gpt',
    '/start-chat-gpt-c',
    '/start-deepseek-ai',
    '/start-deepseek-ai-c',
    '/start-dall-e-c',
    '/start-claude-ai-c',
    '/start-chat-gpt',
    '/start-chat-gpt-b',
    '/start-chatgpt-ar',
    '/start-grok-ai',
    '/start-dall-e',
    '/start-claude-ai',
    '/start-stable-diffusion',
    '/start-stable-diffusion-w'
];


$should_show_script = false;
$current_path = parse_url($request_uri, PHP_URL_PATH);
$current_path = rtrim($current_path, '/'); // Remove trailing slash for consistency

// Check for should_show_script
foreach ($allowed_flags as $flag) {
    if (strpos($request_uri, $flag) === 0) {
        $should_show_script = true;
        break;
    }
}

$js_set_flags = [];

foreach ($parameters as $param) {
    if ($is_restricted && $param === 'ppg') {
        continue;
    }

    if (isset($_GET[$param]) && $_GET[$param] !== '') {
        $value = htmlspecialchars($_GET[$param], ENT_QUOTES, 'UTF-8');

        $host = $_SERVER['HTTP_HOST'] ?? '';
        $mainDomain = 'ai-pro.org';
        $isSubdomain = (stripos($host, '.') !== false && $host !== $mainDomain);

        $domain = $isSubdomain ? null : '.' . $mainDomain;

        $options = array(
            'expires' => time() + (86400 * 30),
            'path' => '/',
            'secure' => false,
            'httponly' => false,
            'samesite' => 'Lax'
        );

        if (!is_null($domain)) {
            $options['domain'] = $domain;
        }

        setcookie($param, $value, $options);
        array_push($js_set_flags, array(
            'name' => $param,
            'value' => $value
        ));
    }
}


include __DIR__ . '/lp-analytics-settings-spa.php';

if ($should_show_script): ?>
<script>
  (function () {
    var locale = "<?= htmlspecialchars(
      isset($_GET['locales']) && $_GET['locales'] !== ''
        ? $_GET['locales']
        : (isset($_COOKIE['locales']) ? $_COOKIE['locales'] : 'en'),
      ENT_QUOTES,
      'UTF-8'
    ) ?>";
    document.documentElement.lang = locale === 'pt' ? 'pt-br' : locale;
  })();
</script>
<script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/8376674a75f51d7de2129067/script.js"></script>
<?php endif; ?>

<script>
    <?php foreach($js_set_flags as $flag) { ?>
        document.cookie = "<?php echo $flag['name'] ?>=<?php echo $flag['value'] ?>; path=/; domain=.ai-pro.org";
    <?php } ?>
</script>