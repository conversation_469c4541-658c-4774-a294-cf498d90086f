<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* includes/head_session.twig */
class __TwigTemplate_3873e4ad78190bb2d5344454169ab342 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if ((twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_session", [], "any", false, false, false, 1) == true)) {
            // line 2
            echo "  <script>
    function ctx() {
      var canvas = document.createElement('canvas');
      var ctx = canvas.getContext('2d');
      var txt = '";
            // line 6
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_session", [], "any", false, false, false, 6), "ctx", [], "any", false, false, false, 6), "html", null, true);
            echo "';
      ctx.textBaseline = \"top\";
      ctx.font = \"16px 'Arial'\";
      ctx.textBaseline = \"alphabetic\";
      ctx.rotate(.05);
      ctx.fillStyle = \"#f60\";
      ctx.fillRect(125,1,62,20);
      ctx.fillStyle = \"#069\";
      ctx.fillText(txt, 2, 15);
      ctx.fillStyle = \"rgba(102, 200, 0, 0.7)\";
      ctx.fillText(txt, 4, 17);
      ctx.shadowBlur=10;
      ctx.shadowColor=\"blue\";
      ctx.fillRect(-20,10,234,5);
      var strng=canvas.toDataURL();
      
      var hash=0;
      if (strng.length==0) return 'nothing!';
      for (i = 0; i < strng.length; i++) {
        char = strng.charCodeAt(i);
        hash = ((hash<<5)-hash)+char;
        hash = hash & hash;
      }

      return hash;
    }
    function tz() {
      var now = new Date();
      var offsetMinutes = now.getTimezoneOffset();
      var offsetHours = Math.abs(offsetMinutes / 60);
      var offsetMinutesPart = Math.abs(offsetMinutes % 60);
      var sign = offsetMinutes > 0 ? \"-\" : \"+\";
      var timezoneOffset = sign + (\"00\" + offsetHours).slice(-2) + \":\" + (\"00\" + offsetMinutesPart).slice(-2);

      return timezoneOffset;
    }
    document.cookie=\"__ctx__=\"+ctx()+\";domain=.ai-pro.org;path=/\";
    document.cookie=\"user_tz=\"+tz()+\";domain=.ai-pro.org;path=/\";
  </script>
  
";
        }
    }

    public function getTemplateName()
    {
        return "includes/head_session.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  45 => 6,  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "includes/head_session.twig", "C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\twig\\includes\\head_session.twig");
    }
}
