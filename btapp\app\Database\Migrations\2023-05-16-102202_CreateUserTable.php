<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateUserTable extends Migration
{
    private $table = "user";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'user_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'user_pid' => [
                'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'email' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'password' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'access_token' => [
				'type' => 'VARCHAR',
				'constraint' => 32,
				'null' => true,
			],
			'login_token' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'first_name' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'last_name' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'status' => [
				'type' => "SET('pending','active','inactive','declined')",
				'default' => 'pending',
            ],
			'mode' => [
				'type' => "SET('live','test')",
				'default' => 'live',
            ],
			'migrated' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => false,
				'default' => ''
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'inactived_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'declined_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('user_id', true);
		$this->forge->addKey(['email'], false, false, 'start_user_email_IDX');
		$this->forge->addKey(['first_name'], false, false, 'start_user_first_name_IDX');
		$this->forge->addKey(['last_name'], false, false, 'start_user_last_name_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
