/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Base styles */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --radius: 6px;
}

body {
  color: rgb(var(--foreground-rgb));
  background: white;
  font-family: 'Inter', sans-serif;
}

/* Gradient Button */
.gradient-button {
  background: linear-gradient(90deg, #1a5eee 0%, #1a5eee 40%, #5ea3fa 100%);
  transition: all 0.3s ease;
}

.gradient-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(27, 95, 238, 0.2);
}


/* Gradient Button */
.gradient-button-purp {
  background: linear-gradient(90deg, #7B2FF7 0%, #7B2FF7 40%, #A371F7 100%);
  transition: all 0.3s ease;
}

.gradient-button-purp:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(123, 47, 247, 0.2);
}

/* Hide scrollbar for Chrome, Safari and Opera */
/* ::-webkit-scrollbar {
  display: none;
} */

/* Mobile optimizations */
@media (max-width: 767px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  h1 {
    font-size: 2rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.75rem;
    line-height: 1.2;
  }
}

/* Custom select styling */
select {
  appearance: none;
  -webkit-appearance: none; /* Safari/Chrome */
  -moz-appearance: none;

  background-color: #fff;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 10 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='1,1 5,5 9,1' fill='none' stroke='%23666' stroke-width='1.5'/%3E%3C/svg%3E");
  /* background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m6 9 6 6 6-6'/%3E%3C/svg%3E"); */
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  /* background-size: 1.5em 1.5em; */
  background-size: 0.6rem;

  cursor: pointer;
}

/* Carousel animation */
@keyframes carousel {
  0% {
    transform: translateX(0);
  }
  100% {
    /* transform: translateX(-50%); */
    transform: translateX(-1820px);
  }
}

.animate-carousel {
  animation: carousel 91s linear infinite;
}

/* Testimonial card blur transition */
.testimonial-card-enter {
  opacity: 0;
  filter: blur(8px);
  transition: opacity 0.5s ease, filter 0.5s ease;
}

.testimonial-card-enter-active {
  opacity: 1;
  filter: blur(0);
}

.testimonial-card-exit {
  opacity: 1;
  filter: blur(0);
  transition: opacity 0.5s ease, filter 0.5s ease;
}

.testimonial-card-exit-active {
  opacity: 0;
  filter: blur(8px);
}
