{"name": "v1", "version": "0.1.0", "private": true, "dependencies": {"@aws-sdk/client-s3": "3.521.0", "@aws-sdk/s3-request-presigner": "3.521.0", "@fortawesome/fontawesome-free": "6.4.0", "@fortawesome/fontawesome-svg-core": "6.4.0", "@fortawesome/free-solid-svg-icons": "6.4.0", "@fortawesome/react-fontawesome": "0.2.0", "@headlessui/react": "1.7.15", "@stripe/react-stripe-js": "2.1.1", "@stripe/stripe-js": "1.54.1", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "autoprefixer": "10.4.14", "axios": "1.4.0", "brotli-webpack-plugin": "1.1.0", "crypto-js": "4.2.0", "css-minimizer-webpack-plugin": "7.0.0", "customize-cra": "1.0.0", "dompurify": "^3.2.6", "framer-motion": "10.12.4", "i18next": "23.4.6", "ladda": "2.0.3", "particles.js": "2.0.0", "postcss-cli": "10.1.0", "react": "18.3.1", "react-app-rewired": "2.2.1", "react-dom": "18.3.1", "react-helmet": "6.1.0", "react-i18next": "13.2.2", "react-icons": "4.12.0", "react-query": "3.39.3", "react-responsive-carousel": "3.2.23", "react-router-dom": "6.10.0", "react-scripts": "5.0.1", "react-slick": "0.29.0", "react-spring": "9.7.1", "resize-observer": "1.0.4", "slick-carousel": "1.8.1", "socket.io-client": "4.7.2", "tailwindcss": "3.3.1", "terser-webpack-plugin": "5.3.10", "toastr": "2.1.4", "underscore": "1.13.6", "universal-cookie": "4.0.4", "web-vitals": "2.1.4", "webpack": "5.90.0"}, "scripts": {"start_hot:LOCAL": "react-scripts start", "build_hot:LOCAL": "env-cmd -f .env react-scripts build", "start": "react-app-rewired start", "build:LOCAL": "env-cmd -f .env react-app-rewired build", "build:PROD": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "clearCache": "npm cache clear --force && npm install --legacy-peer-dep"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-script-url": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"compression-webpack-plugin": "11.1.0", "csp-html-webpack-plugin": "5.1.0", "env-cmd": "10.1.0", "html-webpack-plugin": "5.6.0", "sass": "1.62.0"}}