import React, { Suspense, lazy, useEffect } from "react";
import { <PERSON>rowserRouter as Router, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { getLocales } from './core/utils/app';
import { Get<PERSON><PERSON>ie, SetCookie} from './core/utils/cookies';
import { aiproFlags } from './hooks/flags';
import i18n from './i18n';
import './tailwind.scss';
import './index.css';
import 'segoe-fonts';

const Register = lazy(() => import("./register/index_flow_01"));
const RegisterD = lazy(() => import("./register/index_flow_04"));
const Pricing = lazy(() => import("./pricing/index_flow_01.jsx"));
const PricingD = lazy(() => import("./pricing/index_flow_04.jsx"));
const Pay = lazy(() => import("./pay"));
const PayV1 = lazy(() => import("./pay/index_01"));
const PayV2 = lazy(() => import("./pay/index_02"));
const PayV3 = lazy(() => import("./pay/index_03"));
const PayV4 = lazy(() => import("./pay/index_04"));
const PayV6 = lazy(() => import("./pay/index_06"));
const PayRef = lazy(() => import("./pay-reference/index"));
const PayConfirm = lazy(() => import("./pay-confirm/index"));
const Thankyou = lazy(() => import("./thankyou"));
const StartChatGPT = lazy(() => import("./lp/start-chat-gpt"));
const StartChatGPT3 = lazy(() => import("./lp/start-chatgpt-v3"));
const StartChatGPT4 = lazy(() => import("./lp/start-chatgpt-v4"));
const SplashPage1 = lazy(() => import("./splashscreen/wp_splash"));
const Disclaimer = lazy(() => import("./footer/disclaimer"));
const SelectAccount = lazy(() => import("./select-account/index"));
const NotFoundPage = lazy(() => import("./404"));


const queryClient = new QueryClient();

function App() {

  useEffect(() => {     
      if(window.flags) aiproFlags.flags = window.flags;
      // Initial setup
      getLocales();
      if(aiproFlags.flags['mode'] === 'test') SetCookie("mode", aiproFlags.flags['mode'], {path: '/', domain: '.ai-pro.org'});
      // Check for URL parameters and update i18n if needed
      const existingCookie = GetCookie('locales');
      console.log("App checking cookie, found:", existingCookie);
      console.log("i18n.language before change:", i18n.language);

      if (existingCookie && ['en', 'es', 'fr', 'de', 'it', 'pl', 'pt', 'tr'].includes(existingCookie)) {
        // Only update if different from current language
          console.log("Changing language to:", existingCookie);
          i18n.changeLanguage(existingCookie);
      }
    }, []);
  return (

    <Router>
      <div>
        <QueryClientProvider client={queryClient}>
          <Suspense fallback={null}>
            <div className="md:min-h-[85vh]">
              <Routes>
                <Route path="/register" element={<Register />} />
                <Route path="/register-d" element={<RegisterD />} />
                <Route path="/subscription-plan" element={<Pricing />} />
                <Route path="/plans-and-pricing-d" element={<PricingD />} />
                <Route path="/pay" element={<Pay />} />
                <Route path="/pay/1LCXJMZNX6" element={<Pay />} />
                <Route path="/pay/bH0w05VJXk" element={<PayV1 />} />
                <Route path="/pay/VS6lni4hKx" element={<PayV2 />} />
                <Route path="/pay/uHTinVqsUl" element={<PayV3 />} />
                <Route path="/pay/checkout-page-apple-google" element={<PayV3 />} />
                <Route path="/checkout-page-apple-google" element={<PayV3 />} />
                <Route path="/checkout-multi-lang" element={<PayV3 />} />
                <Route path="/checkout-page-rec" element={<Pay />} />
                <Route path="/pay/mcWiDilmgQ" element={<PayV4 />} />
                <Route path="/pay/FSQ6N8V2A" element={<PayV6 />} />
                <Route path="/payment-reference" element={<PayRef />} />
                <Route path="/payment-confirm" element={<PayConfirm />} />
                <Route path="/thankyou" element={<Thankyou />} />
                <Route path="/start-chat-gpt" element={<StartChatGPT />} />
                <Route path="/start-chatgpt-v3" element={<StartChatGPT3 />} />
                <Route path="/start-chatgpt-v4" element={<StartChatGPT4 />} />
                <Route path="/splash" element={<SplashPage1 />} />
                <Route path="/select-account-type-d" element={<SelectAccount />} />

                {/* always last */}
                <Route path="/*" element={<NotFoundPage />} />
              </Routes>
            </div>
            <Disclaimer/>
          </Suspense>

        </QueryClientProvider>
        <div className="loader-container"><div className="overlay"></div><div className="lds-ai"><div></div><div></div><div></div></div></div>
      </div>
    </Router>
  );
}
export default App;