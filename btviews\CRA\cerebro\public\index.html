<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <title>{{ PAGE.page_title }}</title>

    <link rel="preload" as="script" href="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js" integrity="sha384-UG8ao2jwOWB7/oDdObZc6ItJmwUkR/PfMyt9Qs5AwX7PsnYn1CRKCTWyncPTWvaS" crossorigin="anonymous">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js" integrity="sha384-UG8ao2jwOWB7/oDdObZc6ItJmwUkR/PfMyt9Qs5AwX7PsnYn1CRKCTWyncPTWvaS" crossorigin="anonymous"></script>
    <script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/8376674a75f51d7de2129067/script.js" integrity="sha384-6/YR+c0hjutwhUBuh0MQ8Oq+klxQaL+44EVGRitYNW7xnndOZQBU1ZRYIcHm6AoJ" crossorigin="anonymous"></script>

    <script>
      var view_data = [];
      {% if DATA is defined %}
      view_data = {{ DATA|raw }};
      {% endif %}
      var user_data = [];
      {% if USER is defined %}
      user_data = {{ USER|raw }};
      {% endif %}
      var baseURL = "%REACT_APP_BASE_URL%";
      var start_URL = "%REACT_APP_BASE_URL%";

      // Asynchronously load Toastr stylesheet
      var toastrStylesheet = document.createElement('link');
      toastrStylesheet.rel = 'stylesheet';
      toastrStylesheet.href = 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css';
      document.head.appendChild(toastrStylesheet);

      // Asynchronously load Toastr script
      var toastrScript = document.createElement('script');
      toastrScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js';
      toastrScript.async = true;
      document.head.appendChild(toastrScript);

      // Initialize Toastr once the script is loaded
      toastrScript.onload = function () {
        toastr.options = {
          positionClass: 'toast-top-center'
        };
      };      
    </script>

    <!-- {% include 'includes/head_session.twig' %} -->
    {% include 'includes/head_mixpanel.twig' %}
    {% include 'includes/head_vwo.twig' %}
    {% include 'includes/head_quora.twig' %}
    {% include 'includes/head_fbmeta.twig' %}
    {% include 'includes/head_tiktok.twig' %}
    {% include 'includes/head_twitter.twig' %}
    {% include 'includes/head_gtag.twig' %}
    {% include 'includes/head_bing.twig' %}
    {% include 'includes/head_rewardful.twig' %}
    {% include 'includes/head_fullstory.twig' %}
    {% include 'includes/head_hubspot.twig' %}

    <link rel="preload" as="script" href="%REACT_APP_CHATBOTPRO_URL%assets/index.js" crossorigin>
    <link rel="preload" as="script" href="%REACT_APP_CHATBOTPRO_URL%assets/vendor.js" crossorigin>
    <link rel="preload" as="script" href="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" integrity="sha384-JcnsjUPPylna1s1fvi1u12X5qjY5OL56iySh75FdtrwhO/SWXgMjoVqcKyIIWOLk">
    <link rel="preload" as="script" href="https://cdn.jsdelivr.net/npm/html2canvas@1.3.2/dist/html2canvas.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" integrity="sha384-ZHqoDSJdE8wpN2damqZOsmgOoQsACQe+po725aR/V69WwwpZoHoJtAChKh1Yg40Y">
    <link rel="preload" as="script" href="https://cdn.jsdelivr.net/npm/dompurify@2.3.2/dist/purify.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" integrity="sha384-qGXjBIohNARm2NpEi29ppglRMoxFjrUp2yfMLzLA9ux7xJAM1LNN7Xe+cRQdZRa0">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <form data-rewardful></form>
  </body>
</html>
