import React, { useEffect, useState } from "react";
import classNames from "classnames";
import { Auth } from '../../core/utils/auth';

const Link = ({ href, children }) => {
  return (
    <li className="mb-1.5 list-item list-disc text-[#6c737d]">
      <a
        className="hover:underline"
        href={href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    </li>
  );
};

const Links = ({ title, links, children, className }) => {
  return (
    <div
      className={classNames(
        "flex flex-col md:w-1/4 md:px-2.5 md:first:pl-0 md:last:pr-0",
        className,
      )}
    >
      <p className="font-title mb-6 font-extrabold text-white">{title}</p>
      {links && links.length > 0 && (
        <ul className="pl-10">
          {links.map((link, i) => (
            <Link href={link.href} key={i}>
              {link.text}
            </Link>
          ))}
        </ul>
      )}
      {children}
    </div>
  );
};

function Footer() {
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
    const auth = Auth();

    useEffect(() => {
        setCurrentYear(new Date().getFullYear());
    }, []);

    const translations = {
        "about": "About AI-PRO.org",
        "aboutText1": "AI-PRO.org is an artificial intelligence resource website helping people navigate the world of AI.",
        "aboutText2": "Discover, learn, and innovate with AI at AI-PRO.org.",
        "usefulLinks": "Useful Links",
        "aboutUs": "About Us",
        "contactUs": "Contact Us",
        "login": "Login",
        "pricing": "Pricing",
        "affiliates": "Affiliates",
        "importantLinks": "Important Links",
        "registration": "Registration",
        "usagePolicy": "Usage Policy",
        "termsConditions": "Terms and Conditions",
        "privacyPolicy": "Privacy Policy",
        "disclaimer": "Disclaimer",
        "contactInfo": "Contact Info",
        "email": "Email:",
        "emailAdd": "<EMAIL>",
        "copyright": "Copyright © {{year}} AI-Pro.org",
        "disclaimerTitle": "Disclaimer",
        "readMore": "Read more here",
        "disclaimerText": "The information provided on this website is for general informational purposes only. While we strive to keep the information up to date and correct, we make no representations or warranties of any kind, express or implied, about the completeness, accuracy, reliability, suitability or availability with respect to the website or the information, products, services, or related graphics contained on the website for any purpose. Any reliance you place on such information is therefore strictly at your own risk. Furthermore, we are not in any way affiliated or related to OpenAI and Stability AI."
    };

    const usefulLinks = [
        { href: "https://ai-pro.org/about-us/", text: translations.aboutUs },
        { href: "https://ai-pro.org/contact-us/", text: translations.contactUs },
        { href: "https://ai-pro.org/pricing-redirect/", text: translations.pricing },
        { href: "https://ai-pro.org/affiliates", text: translations.affiliates },
    ];

    // Only add login link if user is not authenticated
    if (!auth) {
        usefulLinks.push({ href: "https://ai-pro.org/member-login/", text: translations.login });
    }

    return (
        <footer className="max-w-[1280px] pt-10 p-4 mx-auto text-gray md:flex md:flex-row md:flex-wrap [&_>_*:not(:last-child)]:mb-7 text-[#6c737d]">
            <Links title={translations.about}>
                <p>
                    {translations.aboutText1}
                </p><br/>
                <p>
                    {translations.aboutText2}
                </p>
            </Links>
            <Links
                title={translations.usefulLinks}
                links={usefulLinks}
            />
            <Links
                title={translations.importantLinks}
                links={[
                    { href: "https://ai-pro.org/register/", text: translations.registration },
                    { href: "https://ai-pro.org/fair-usage-policy/", text: translations.usagePolicy },
                    { href: "https://ai-pro.org/member-tos-page/", text: translations.termsConditions },
                    { href: "https://ai-pro.org/privacy-policy/", text: translations.privacyPolicy },
                    { href: "https://ai-pro.org/disclaimer/", text: translations.disclaimer },
                ]}
            />
            <Links title={translations.contactInfo}>
                <div className="flex w-full flex-row items-center pl-[22px] text-left">
                    <span className="mr-4 flex h-12 w-12 flex-none items-center justify-center rounded-[100%] border-2 border-white/50 pb-1 text-2xl font-semibold">
                        @
                    </span>
                    <p className="flex flex-col">
                        <strong>{translations.email}</strong>
                        <a href="mailto:<EMAIL>" className="hover:underline">
                            {translations.emailAdd}
                        </a>
                    </p>
                </div>
            </Links>
            <div className="flex w-full flex-row items-center justify-between py-10">
                <p>{translations.copyright.replace('{{year}}', currentYear)}</p>
                <div className="flex flex-row items-center [&_svg]:block [&_svg]:h-auto [&_svg]:w-full [&_svg]:fill-[#3A4F66]">
                    <a
                        className="mr-4 flex h-[17px] w-[17px] items-center"
                        href="https://twitter.com/AIProOfficial"
                        title="Twitter link"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                            <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
                        </svg>
                    </a>
                    <a
                        className="flex h-[17px] w-[17px] items-center"
                        href="https://twitter.com/AIProOfficial"
                        title="Instagram link"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path d="M459.4 151.7c.3 4.5 .3 9.1 .3 13.6 0 138.7-105.6 298.6-298.6 298.6-59.5 0-114.7-17.2-161.1-47.1 8.4 1 16.6 1.3 25.3 1.3 49.1 0 94.2-16.6 130.3-44.8-46.1-1-84.8-31.2-98.1-72.8 6.5 1 13 1.6 19.8 1.6 9.4 0 18.8-1.3 27.6-3.6-48.1-9.7-84.1-52-84.1-103v-1.3c14 7.8 30.2 12.7 47.4 13.3-28.3-18.8-46.8-51-46.8-87.4 0-19.5 5.2-37.4 14.3-53 51.7 63.7 129.3 105.3 216.4 109.8-1.6-7.8-2.6-15.9-2.6-24 0-57.8 46.8-104.9 104.9-104.9 30.2 0 57.5 12.7 76.7 33.1 23.7-4.5 46.5-13.3 66.6-25.3-7.8 24.4-24.4 44.8-46.1 57.8 21.1-2.3 41.6-8.1 60.4-16.2-14.3 20.8-32.2 39.3-52.6 54.3z" />
                        </svg>
                    </a>
                </div>
            </div>
            <p className="text-sm">
                <strong>{translations.disclaimerTitle}</strong>: {translations.disclaimerText}{" "}
                <a
                    className="text-blue-200 hover:text-blue-100"
                    href="https://ai-pro.org/disclaimer/"
                >
                    {translations.readMore}
                </a>
                .
            </p>
        </footer>
    );
};

export default Footer;