<?php

namespace App\Controllers\FlowPages;

use App\Controllers\BaseController;
use App\Models\AccountModel;

use BT<PERSON>ore\Mailer\HubSpot as BTHubSpot;
use <PERSON>Core\Mailer as BTMailer;

class ThankYou extends BaseController
{
    private $theme = FLAG_THEME_DEFAULT;
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename

    private $pageSlug = '';
    private $userData;
    private $mailer_key = [];

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct(){
        $this->mailer_key = [ 'api_key' => getenv("MAILER_KEY") ];
        // parent::__construct();
    }


    public function index(){
        // $apiUrl = base_url('api/v1/get-subscription');
        // var_dump($apiUrl);
        // $response = file_get_contents($apiUrl);
        // $responseData = json_decode($response, true);
        // $price = isset($responseData['data'][0]['price']) ? $responseData['data'][0]['price'] : null;

        $isProxy = btflag('isProxy','no');
        if ($isProxy=='yes'){
            header("Location: " . base_url('ty'));
            die;
        }

        if(!btsessionIsUserLoggedIn()) {
						$login_token = btflag("access", null);
						if($login_token) {
							$user = $this->getUserByToken($login_token);
							if($user['success']) {
									$email = $user['data']->email;
									$this->reInitCookies();
									$this->setAuthCookies($email, $user['raw'], $login_token);
									btsessionSetAccount($user['data'], 1);
							} else {
								header("Location: " . base_url('login'));
								die;
							}
						} else {
							header("Location: " . base_url('login'));
							die;
						}
        }

				if(!btsessionHas("PLAN")) {
            header("Location: " . base_url('pricing'));
            die;
				}
        // REMOVE PRICING
        btflag_remove('pricing');
        btflag_remove('pricing', null, ['domain' => '.ai-pro.org']);
        //
        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data();
        $this->theme_pageVersion();
        $this->hubspot_tracking();

        switch ($this->theme) {
            case 'basilisk-03':
            case 'basilisk-02':
            case 'basilisk':
                $this->theme_arcana();
                break;
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    public function index_fake()
    {

        // if(!btsessionIsUserLoggedIn()) {
        //     header("Location: " . base_url('login'));
        //     die;
        // }

        // REMOVE PRICING
        btflag_remove('pricing');
        btflag_remove('pricing', null, ['domain' => '.ai-pro.org']);
        //
        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data_fake();
        $this->theme_pageVersion();
        $this->theme_arcana();
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------
    private function hubspot_tracking(){
        // MAILERLITE
        $mailertype = new BTHubSpot($this->mailer_key);
        $mailertool = new BTMailer($mailertype);

        $ppcKeyword = btflag('keyword', '');
        $EmailId = btflag('emailid', '');

        if ($ppcKeyword == ''){
            $ppcKeyword = 'unknown';
        }

        if ($EmailId == ''){
            $EmailId = 'unknown';
        }

        $email = btsessionGet('USER')->email;
        if ($email==''){
            return;
        }

        if($this->isEmailTest($email)) {
        // Do nothing
        } else if(!$mailertool->checkExist($email)) {
            try {
                $subscriber = [
                'ppc_keyword' => $ppcKeyword,
                'email_id' => $EmailId
                ];
                $result = $mailertool->addSubscriber(getenv("MAILER_GROUP"), $subscriber);
            } catch (Exception $e) {
            }

        } else {
            try {
                $subscriber = [
                'ppc_keyword' => $ppcKeyword,
                'email_id' => $EmailId
                ];
                $result = $mailertool->updateSubscriber($email, $subscriber);
            } catch (Exception $e) {
            }
        }
        // END MAILERLITE
    }

    private function getSubscription(){
        $user = null;

        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
        } else return $this->response->setJSON($this->res_error);
        $accountModel = new AccountModel();
        $account = $accountModel->getSubscription($user_id);

        if($account['success'] && $account['res']) {
            $acct_user = $account['res'];
            foreach($acct_user as $key => $acct){
                $plan_type = strtolower($acct_user[$key]->plan_type);
                $price_per_member = strtolower($acct_user[$key]->price_per_member);
                $trial_days = strtolower($acct_user[$key]->trial_days);
                $start_date = strtolower($acct_user[$key]->start_date);

                if ($plan_type=='enterprise'){
                    $members = $acct_user[$key]->members;
                    $base_price = $acct_user[$key]->price;
                    $payment_interval = strtolower($acct_user[$key]->payment_interval);
                    if ($members>9){
                        $price = $base_price+(($members-9)*$price_per_member);
                        $acct_user[$key]->price = $price;
                        if ($payment_interval=='monthly'){
                            $acct_user[$key]->price_label = '$'.number_format($price).'/MONTH';
                        }else{
                            $acct_user[$key]->price_label = '$'.number_format($price).'/YEAR';
                        }
                    }
                }

                $acct_user[$key]->is_trial_end = 'yes';
                if ($trial_days!==null&&$trial_days!==''&&$trial_days>0){
                    $trial_end = date('Y-m-d', strtotime($start_date. ' + '.$trial_days.' days'));
                    $acct_user[$key]->is_trial_end = 'no';
                }
            }
            return ['data' => $acct_user];
        }
        return $this->response->setJSON($this->res_error);
    }

    private function isEmailTest($email)
    {
        if( strpos($email, "test") || strpos($email, "dispostable") || strpos($email, "mailinator")
        ) {
            return true;
        }
        return false;
    }

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'USER' => $this->userData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_basilisk()
    {

        switch($this->pageSlug) {
            case 'thankyou':
                $this->theme_arcana();
                return;
        }
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'USER' => $this->userData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_basilisk/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data(){
        $subscription = $this->getSubscription();
        $price = $subscription['data'][0]->price;

        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_quora' => true,
            'include_tiktok' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => 'thank-you-page',
                'keyword' => btflag('keyword', ''),
'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan_display', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'rewardful_via' => btflag_cookie('via', ''),
                'currency' => btflag_cookie('currency', btsessionGet('PLAN')->currency),
                'value' => btflag_cookie('upg', '') === '1' ? '' : strtolower(btsessionGet('PLAN')->trial_price !== null && btsessionGet('PLAN')->trial_price !== '' ? btsessionGet('PLAN')->trial_price : $price),
                'planid' => date('Ymd_His') . '_' . btsessionGet('PLAN')->plan_id,
                'amount' => btflag_cookie('amount', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => true,
            'include_gtag_GA4' => true,
            'include_bing' => true,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag_cookie('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);
        }

        $accountModel = new AccountModel();
        $account = $accountModel->getActiveSubscription(btsessionGet('USER')->user_id);
        if ($account['success']==1 && $account['res']){
          btsessionGet('USER')->expired = $account['res'][0]->expired;
        }else{
          btsessionGet('USER')->expired = 'no';
        }
        $plan_restriction = $this->getPlanRestriction(btsessionHas('PLAN') ? btsessionGet('PLAN')->plan_id : null);

        $this->userData = json_encode([
            'user_pid' => btsessionGet('USER')->user_pid,
            'email' => btsessionGet('USER')->email,
            'status' => btsessionGet('USER')->status,
            'plan' => strtolower(btsessionGet('PLAN')->plan_type),
            'plan_id' => strtolower(btsessionGet('PLAN')->plan_id),
            'currency' => strtolower(btsessionGet('PLAN')->currency),
            'expired' => strtolower(btsessionGet('USER')->expired),
            'authversion' => $this->getUserAuthVersion(btsessionGet('USER')->created_at),
            'plan_restriction' => $plan_restriction
        ]);
    }

    private function theme_data_fake()
    {

        $email = btflag_cookie('user_email', '');
        $data = btdbFindBy('UserModel', 'email', $email);
        $user_data = $data['res'][0];

        btsessionSetAccount($user_data, 1);
        btsessionGet('USER')->expired = 'yes';

        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
            ],
            'include_vwo' => true,
            'include_fbmeta' => true,
            'include_quora' => true,
            'include_tiktok' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => 'thank-you-page',
                'keyword' => btflag('keyword', ''),
                'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan_display', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'rewardful_via' => btflag_cookie('via', ''),
                'currency' => btflag_cookie('currency', ''),
                'amount' => btflag_cookie('amount', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => true,
            'include_gtag_GA4' => true,
            'include_bing' => false,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        $this->userData = json_encode([
            'user_pid' => btsessionGet('USER')->user_pid,
            'email' => btsessionGet('USER')->email,
            'status' => btsessionGet('USER')->status,
            'plan' => '',
            'plan_id' => '',
            'currency' => '',
            'expired' => strtolower(btsessionGet('USER')->expired),
            'authversion' => $this->getUserAuthVersion(btsessionGet('USER')->created_at),
            'plan_restriction' => null
        ]);

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}
