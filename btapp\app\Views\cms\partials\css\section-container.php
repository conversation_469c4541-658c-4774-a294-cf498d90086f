<style nonce="<?= $nonce ?>">
    .section-container {
        display: flex;
        align-items: center;
        padding: 4rem 2rem;
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }
    .section-container:nth-child(odd) {
        flex-direction: row-reverse;
    }
    .content-block {
        flex: 1;
    }
    .image-block {
        flex: 1;
        position: relative;
    }
    .quote-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .section-image {
        width: 100%;
        height: auto;
        display: block;
    }
    .section-title {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
        color: #333;
    }
    .section-description {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #666;
    }
    .quote-text {
        font-size: 1.2rem;
        font-style: italic;
        margin-bottom: 1rem;
    }
    .quote-author {
        font-size: 1rem;
        text-align: right;
    }
    @media (max-width: 768px) {
        .section-container {
            flex-direction: column !important;
            padding: 2rem 1rem;
        }
        .section-title {
            font-size: 2rem;
        }
    }
</style>