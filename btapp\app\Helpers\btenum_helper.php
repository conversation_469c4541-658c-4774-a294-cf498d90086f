<?php

function btenum()
{
    $enum = [
        'user_log' => [
            'logout' => [
                'activity' => 'User Signed Out',
                'event' => 'logout',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' signed out. ',
            ],
            'login' => [
                'activity' => 'User Signed In',
                'event' => 'login',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' signed in. ',
            ],
            'register' => [
                'activity' => 'Register User',
                'event' => 'register',
                'event_data' => '',
                'description' => 'New user ' . btutilAffixKey('key_user_pid') . ' created. ',
            ],
            'forgot-password' => [
                'activity' => 'User Request Password Reset',
                'event' => 'forgot-password',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' requested password reset. ',
            ],
            'change-card' => [
                'activity' => 'User change card',
                'event' => 'change-card',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  change card. ',
            ],
            'create-subscription' => [
                'activity' => 'User create subscription',
                'event' => 'create-subscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  create subscription. ',
            ],
            'update-account' => [
                'activity' => 'User update account',
                'event' => 'update-account',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  update account. ',
            ],
            'update-subscription' => [
                'activity' => 'User update subscription',
                'event' => 'update-subscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  update subscription. ',
            ],
            'subcription-add-member' => [
                'activity' => 'Subscription add member',
                'event' => 'subcription-add-member',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  add new member to subscription. ',
            ],
            'cancel-subscription' => [
                'activity' => 'User cancel subscription',
                'event' => 'cancel-subscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  cancel subscription. ',
            ],
            'change-email' => [
                'activity' => 'User change email',
                'event' => 'change-email',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  change email. ',
            ],
            'change-password' => [
                'activity' => 'User change password',
                'event' => 'change-password',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  change password. ',
            ],
            'reactivateSubscription' => [
                'activity' => 'User reactivate subscription',
                'event' => 'reactivateSubscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  reactivate subscription. ',
            ],
            'deleteAccount' => [
                'activity' => 'User delete account',
                'event' => 'deleteAccount',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  delete account. ',
            ],
            'addEnterpriseMember' => [
                'activity' => 'Add Enterprise Member',
                'event' => 'add-enterprise-member',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  add enterprise member. ',
            ],
            'deleteEnterpriseMember' => [
                'activity' => 'Delete Enterprise Member',
                'event' => 'delete-enterprise-member',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  delete enterprise member. ',
            ],
            'resetPasswordEnterpriseMember' => [
                'activity' => 'Reset Password Enterprise Member',
                'event' => 'reset-password-enterprise-member',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  reset password enterprise member. ',
            ],
            'updateEnterpriseMember' => [
                'activity' => 'Update Enterprise Member',
                'event' => 'update-enterprise-member',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  update enterprise member. ',
            ],
            'sendEnterprisePaymentConfirmation' => [
                'activity' => 'Send Enterprise Payment Confirmation',
                'event' => 'send-enterprise-payment-confirmation',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  send enterprise payment confirmation. ',
            ],
            'auto_logout' => [
                'activity' => 'Auto logout on Delete account',
                'event' => 'auto_logout',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . '  Auto logout on Delete account ',
            ],
            'click-app-upgrade' => [
                'activity' => 'Upgrade modal appear upon clicking the app',
                'event' => 'click-app-upgrade',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' Upgrade modal appear upon clicking the app.'
            ],
            'page-access' => [
                'activity' => 'User access My Apps page',
                'event' => 'page-access',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' access My Apps page.'
            ],
            'pause-subscription' => [
                'activity' => 'Pause user subscription',
                'event' => 'pause-subscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' paused subscription.'
            ],
            'resume-subscription' => [
                'activity' => 'Resume user subscription',
                'event' => 'resume-subscription',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' resume subscription.'
            ],
             'set-mood-rating' =>  [
                'activity' => 'User set mood rating',
                'event' => 'set-mood-rating',
                'event_data' => '',
                'description' => 'User ' . btutilAffixKey('key_user_pid') . ' set mood rating. '
            ],

            // // 'access-apps'
            // 'direct-access' => [
            //     'activity' => 'User direct access the app link',
            //     'event' => 'direct-access',
            //     'event_data' => '',
            //     'description' => 'User'
            // ],
            // 'clicking-the-link' =>[
            //      'activity' => 'User click the link to access the app',
            //      'event' => 'clicking-the-link',
            //      'event_data' => '',
            //      'description' => 'User'
            // ]
        ],
    ];

    return $enum;
}
