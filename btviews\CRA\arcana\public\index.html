<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>{{ PAGE.page_title }}</title>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- <link rel="preconnect" href="https://tr.outbrain.com/" crossorigin> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/8376674a75f51d7de2129067/script.js" integrity="sha384-6/YR+c0hjutwhUBuh0MQ8Oq+klxQaL+44EVGRitYNW7xnndOZQBU1ZRYIcHm6AoJ" crossorigin="anonymous"></script>

    <style>#modal-container{font-family:'Alegreya Sans',sans-serif !important;z-index:9999999}button.close{float:right;font-size:25px}#modal-container-enterprise{display:none;position:fixed;z-index:9999;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,.5)}
      header.openchat-relative {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}header.openchat-p-2 {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}
      header.openchat-py-4 {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}.openchat-bg-accent2 {background-color: gray !important;}.appbox{--tw-border-opacity: 1 !important;border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;}.openchat-text-base.openchat-font-semibold {max-width: 75%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;}
      .fade-in.openchat-px-2.openchat-py-1.openchat-font-bold.openchat-text-center.openchat-text-lg.openchat-overflow-hidden.openchat-truncate.openchat-absolute {max-width: 75%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;} .openchat-w-full.openchat-bg-white.openchat-flex.openchat-flex-col.openchat-gap-2.openchat-p-2.openchat-text-base.openchat-leading-6.openchat-mt-5.openchat-rounded-lg.openchat-shadow{display:none;}
      .openchat-fixed.openchat-bottom-5.openchat-right-5.z-50{z-index:8889 !important;}
    </style>
    {% include 'includes/head_session.twig' %}
    {% include 'includes/head_mixpanel.twig' %}
    {% include 'includes/head_vwo.twig' %}
    {% include 'includes/head_quora.twig' %}
    {% include 'includes/head_fbmeta.twig' %}
    {% include 'includes/head_tiktok.twig' %}
    {% include 'includes/head_twitter.twig' %}
    {% include 'includes/head_analytics.twig' %}
    {% include 'includes/head_bing.twig' %}
    {% include 'includes/head_rewardful.twig' %}
    {% include 'includes/head_fullstory.twig' %}
    {% include 'includes/head_hubspot.twig' %}
    {% include 'includes/head_gtag.twig' %}
    {% include 'includes/head_flags.twig' %}
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <form data-rewardful></form>
    <div id="modal-container" class="no-subtitle" style="display: none;">
      <div id="modal-content">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h2 id="modal-title">Switch to <span class="plan-name">PRO</span> to continue using <span class="app-name">ChatGPTPro</span></h2>
        <p id="modal-desc">Switch now to <span class="plan-name">PRO</span> and get access to <span class="app-total">14</span> different creativity and productivity AI tools.</p>
        <a id="modal-cta" href="#" target="_parent">Continue</a>
      </div>
    </div>
    <div id="modal-container-enterprise" class="no-subtitle" style="display: none;">
      <div id="modal-content">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h2 id="modal-title">Active Subscription Required</span></h2>
        <p id="modal-desc">Your subscription is either expired or currently inactive.<br>For continuous access, kindly reach out to our support team.
        </p>
        <a id="modal-cta" href="https://ai-pro.org/contact-us/" target="_blank">Contact Support</a>
      </div>
    </div>
  </body>
  <script defer> var view_data = []; {% if DATA is defined %} view_data = {{ DATA|raw }}; {% endif %} var user_data = []; {% if USER is defined %} user_data = {{ USER|raw }}; {% endif %} var baseURL = "%REACT_APP_BASE_URL%"; var start_URL = "%REACT_APP_BASE_URL%"; </script>
  <script defer>
    let ver = new Date().getTime();
    let btutilAssetCSS = document.createElement('link');
    btutilAssetCSS.setAttribute("rel", "stylesheet");
    btutilAssetCSS.setAttribute("href", "%REACT_APP_BTUTIL_CSS_URL%" + ver);
    document.head.appendChild(btutilAssetCSS);
  </script>
  <script defer>
    document.querySelector('#modal-container').addEventListener('click', function(e){
      if(e.target && e.target.id == 'modal-container') {
        document.getElementById('modal-container').style.display = 'none';
      }
    });
    document.querySelector('#modal-container .close').addEventListener('click', function(){
      document.getElementById('modal-container').style.display = 'none';
    });

    document.querySelector('#modal-container-enterprise').addEventListener('click', function(e){
      if(e.target && e.target.id == 'modal-container-enterprise') {
        document.getElementById('modal-container-enterprise').style.display = 'none';
      }
    });
    document.querySelector('#modal-container-enterprise .close').addEventListener('click', function(){
      document.getElementById('modal-container-enterprise').style.display = 'none';
    });

    document.addEventListener("DOMContentLoaded", function () {
      if (window.location.pathname === "/checkout-page-rec") {
        document.body.style.backgroundColor = "white";
      }
    });
  </script>
</html>
