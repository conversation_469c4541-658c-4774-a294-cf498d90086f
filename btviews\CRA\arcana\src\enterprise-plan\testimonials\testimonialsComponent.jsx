"use client"

import { useEffect, useRef, useState } from "react"
import devonImg from '../images/testimonials/devon-rodriguez.jpg';
import jaxonImg from '../images/testimonials/jaxon-mcallister.jpg';
import elaraImg from '../images/testimonials/elara-finnigan.jpg';
import ariadneImg from '../images/testimonials/ariadne-holt.jpg';
import marcusImg from '../images/testimonials/marcus-chen.jpg';

export function TestimonialsComponent() {
  const [isPaused, setIsPaused] = useState(false)
  const sliderRef = useRef(null)

  const testimonials = [
    {
        id: 1,
        text: "Built an interactive FAQ bot for our website—works great!",
        name: "<PERSON>",
        image: devonImg,
        title: "Amazing!",
    },
    {
        id: 2,
        text: "We use Chatbot Pro for customer support—it's fast and reliable.",
        name: "<PERSON><PERSON>",
        image: jaxonImg,
        title: "Game-changing",
    },
    {
        id: 3,
        text: "Perfect for helping students with homework and explanations.",
        name: "<PERSON><PERSON>",
        image: elaraImg,
        title: "Brilliant!",
    },
    {
        id: 4,
        text: "I create AI-generated content for my blog in seconds.",
        name: "Ariadne <PERSON>",
        image: ariadneImg,
        title: "Incredible",
    },
    {
        id: 5,
        text: "Used it to build an onboarding assistant for new hires.",
        name: "Marcus Chen",
        image: marcusImg,
        title: "Super easy!",
    }
  ]

  const TestimonialCard = ({ testimonial }) => (
    <div className="flex-shrink-0 w-[340px] mx-3 bg-white rounded-[40px] p-6 shadow-sm relative">
      <div className="flex h-full">
        <div className="left-col mr-4 flex flex-col justify-between">
          <div className="w-12 h-12 rounded-full overflow-hidden relative">
            <img
              src={testimonial.image || "/placeholder.svg"}
              alt={testimonial.name}
              width={48}
              height={48}
              className="rounded-full"
            />
          </div>
          <div className="w-px bg-gray-200 mx-auto mt-2 flex-1"></div>
        </div>

        <div className="flex-1">
            <div className="flex items-center mb-2 justify-between min-h-[52px]">
                <h3 className="font-bold mr-2 text-[18px]">{testimonial.title}</h3>
                <div className="text-yellow-400 ml-auto text-[24px]">★★★★★</div>
            </div>
            <div className="flex flex-col justify-between min-h-[105px]">
                <p className="text-gray-600">{testimonial.text}</p>
                <p className="text-gray-500 text-right">- {testimonial.name}</p>
            </div>
        </div>
      </div>
    </div>
  )

  useEffect(() => {
    const slider = sliderRef.current
    if (!slider) return

    // Set the animation play state based on isPaused
    slider.style.animationPlayState = isPaused ? "paused" : "running"
  }, [isPaused])

  return (
    <div
      className="w-full overflow-hidden py-12 mx-auto max-w-[1154px] relative"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      {/* Left blur gradient overlay */}
      <div
        className="absolute left-0 top-0 bottom-0 w-16 z-10 pointer-events-none max-w-640:hidden"
        style={{
          // background: "linear-gradient(to right, #F3F8FF 0%, rgba(248, 250, 252, 0.5) 100%)",
          background: "linear-gradient(to right, #F3F8FF 0%, rgba(243, 248, 255, 0) 100%)",
          backdropFilter: "blur(0.75px)",
        }}
      ></div>

      {/* Right blur gradient overlay */}
      <div
        className="absolute right-0 top-0 bottom-0 w-16 z-10 pointer-events-none max-w-640:hidden"
        style={{
          // background: "linear-gradient(to left, #F3F8FF 0%, rgba(248, 250, 252, 0.5) 100%)",
          background: "linear-gradient(to left, #F3F8FF 0%, rgba(243, 248, 255, 0) 100%)",
          backdropFilter: "blur(0.75px)",
        }}
      ></div>

      <div className="relative w-full overflow-hidden">
        <div
          ref={sliderRef}
          className="flex animate-carousel"
          style={{
            animationDuration: "91s",
            animationTimingFunction: "linear",
            animationIterationCount: "infinite",
          }}
        >
          {/* First set of testimonials */}
          {testimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}

          {/* Duplicate set for seamless looping */}
          {testimonials.map((testimonial) => (
            <TestimonialCard key={`dup-${testimonial.id}`} testimonial={testimonial} />
          ))}

          {/* Duplicate set for seamless looping */}
          {testimonials.map((testimonial) => (
            <TestimonialCard key={`dup2-${testimonial.id}`} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </div>
  )
}
