<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreatePlanTable extends Migration
{
    private $table = "plan";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'plan_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'plan_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'plan_name' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'plan_type' => [
				'type' => "SET('Pro','Basic')",
				'null' => true,
            ],
			'details' => [
				'type' => 'VARCHAR',
				'constraint' => 100,
				'null' => true,
			],
			'label' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'ppg' => [
				'type' => 'VARCHAR',
				'constraint' => 5,
				'null' => true,
			],
			'recurly_plan_id' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'stripe_plan_id' => [
				'type' => 'VARCHAR',
				'constraint' => 250,
				'null' => true,
			],
			'trial_days' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true
			],
			'payment_interval' => [
				'type' => "SET('Monthly','Yearly')",
				'null' => true
            ],
			'price' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true
			],
			'trial_price' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true
			],
			'currency' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => true
			],
			'display_txt1' => [
				'type' => 'VARCHAR',
				'constraint' => 500,
				'null' => true
			],
			'display_txt2' => [
				'type' => 'VARCHAR',
				'constraint' => 500,
				'null' => true
			],
			'display_txt3' => [
				'type' => 'VARCHAR',
				'constraint' => 500,
				'null' => true
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('plan_id', true);

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
