import React, { Suspense, useEffect, lazy } from "react";
import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { Loading } from "./aiapps/loading";
import { getLocales } from './core/utils/app';
import { GetCookie } from './core/utils/cookies';
import { aiproFlags } from './hooks/flags';
import i18n from './i18n';

const queryClient = new QueryClient();

// Lazy-loaded components
const Home = lazy(() => import("./home"));
const Pricing = lazy(() => import("./pricing"));
const PricingE = lazy(() => import("./pricing/flow06"));
const Upgrade = lazy(() => import("./upgrade"));
const UpgradePay = lazy(() => import("./pay-upgrade"));
const UpgradePayEnt = lazy(() => import("./pay-upgrade/index_ent"));
const Downgrade = lazy(() => import("./downgrade"));
const Register = lazy(() => import("./register"));
const RegisterE =  lazy(() => import("./register/flow06")); 
const Login = lazy(() => import("./login"));
const Forgot = lazy(() => import("./forgot"));
const PasswordSent = lazy(() => import("./password-sent"));
const Reset = lazy(() => import("./resetpass"));
const Pay = lazy(() => import("./pay"));
const PayV1 = lazy(() => import("./pay/index_01"));
const PayV2 = lazy(() => import("./pay/index_02"));
const PayV3 = lazy(() => import("./pay/index_03"));
const PayV4 = lazy(() => import("./pay/index_04"));
const PayV5 = lazy(() => import("./pay/index_05"));
const PayV6 = lazy(() => import("./pay/index_05"));
const PayV7 = lazy(() => import("./pay/index_06"));
const PayRef = lazy(() => import("./pay-reference/index"));
const PayConfirm = lazy(() => import("./pay-confirm/index"));
const Thankyou = lazy(() => import("./thankyou"));
const ThankYouFake = lazy(() => import("./thankyou/index_fake"));
// const ThankYouFakeBot = lazy(() => import("./thankyou/index_fake_bot"));
const MyAccount = lazy(() => import("./my-account"));
const Manage = lazy(() => import("./manage-account"));
const ChangeCard = lazy(() => import("./change-card"));
const EmailTemplates = lazy(() => import("./emailtemplates"));
const ActiveSession = lazy(() => import("./active-session"));
const NotFoundPage = lazy(() => import("./404"));
const RedirectAccount = lazy(() => import("./redirect-account-required"));
const SplashPage = lazy(() => import("./splashscreen"));
// LPs
const StartStableDiffusion = lazy(() => import("./lp/start-stable-diffusion"));
const StartChatGPT = lazy(() => import("./lp/start-chat-gpt"));
const StartChatGPTgo = lazy(() => import("./lp/start-chatgpt-go"));
const StartChatGPTv2 = lazy(() => import("./lp/start-chatgpt-v2"));
const Text2Image = lazy(() => import("./lp/text-to-image"));
const Survey = lazy(() => import("./survey"));
const Disclaimer = lazy(() => import("./footer/disclaimer"));
const Resume = lazy(() => import("./resume-subscription"));
const Review = lazy(() => import("./review"));
const EnterprisePlan = lazy(() => import("./enterprise-plan"));

// legal pages
const Affiliates = lazy(() => import("./affiliates"));
const DisclaimerPage = lazy(() => import("./disclaimer-page"));
const PrivacyPolicy = lazy(() => import("./privacy-policy"));
const SelectAccount = lazy(() => import("./select-account/index"));

function InnerApp() {
  const loc = useLocation();
  const currentLocation = loc.pathname;

  useEffect(() => {
    if(window.flags) aiproFlags.flags = window.flags;
    import("./tailwind.scss");
    import("./index.css");
    
    // Initial setup
    getLocales();
    
    // Check for URL parameters and update i18n if needed
    const existingCookie = GetCookie('locales');
    
    if (existingCookie && ['en', 'es', 'fr', 'de', 'it', 'pl', 'pt', 'tr'].includes(existingCookie)) {
      // Only update if different from current language
      if (i18n.language !== existingCookie) {
        i18n.changeLanguage(existingCookie);
      }
    }
  }, []);

  return (
    <div>
      <QueryClientProvider client={queryClient}>
        <Suspense fallback={<Loading />}>
          <div className="md:min-h-[85vh]">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/home" element={<Home />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/subscription" element={<Pricing />} />
              <Route path="/subscription-e" element={<PricingE />} />
              <Route path="/upgrade" element={<Upgrade />} />
              <Route path="/upgrade/:id" element={<UpgradePay />} />
              <Route path="/upgrade-ent/:member" element={<UpgradePayEnt />} />
              <Route path="/downgrade" element={<Downgrade />} />
              <Route path="/downgrade/:id" element={<UpgradePay />} />
              <Route path="/register" element={<Register />} />
              <Route path="/signup" element={<Register />} />
              <Route path="/register-e" element={<RegisterE />} />
              <Route path="/pay" element={<Pay />} />
              <Route path="/checkout-page-rec" element={<Pay />} />
              <Route path="/pay/1LCXJMZNX6" element={<Pay />} />
              <Route path="/pay/bH0w05VJXk" element={<PayV1 />} />
              <Route path="/pay/VS6lni4hKx" element={<PayV2 />} />
              <Route path="/checkout-page-apple-google" element={<PayV3 />} />
              <Route path="/checkout-multi-lang" element={<PayV3 />} />
              <Route path="/pay/uHTinVqsUl" element={<PayV3 />} />
              <Route path="/pay/mcWiDilmgQ" element={<PayV4 />} />
              <Route path="/pay/2FQH9T5Y7P" element={<PayV5/>} />
              <Route path="/pay/LZQ6N8V2A" element={<PayV6/>} />
              <Route path="/pay/FSQ6N8V2A" element={<PayV7/>} />
              <Route path="/checkout-page-fsg" element={<PayV7/>} />
              <Route path="/payment-reference" element={<PayRef />} />
              <Route path="/payment-confirm" element={<PayConfirm />} />
              <Route path="/login" element={<Login />} />
              <Route path="/forgot" element={<Forgot />} />
              <Route path="/password-sent" element={<PasswordSent />} />
              <Route path="/resetpass" element={<Reset />} />
              <Route path="/thankyou" element={<Thankyou />} />
              <Route path="/ty" element={<ThankYouFake />} />
              {/* <Route path="/typage" element={<ThankYouFakeBot />} /> */}
              <Route path="/my-account" element={<MyAccount />} />
              <Route path="/manage-account" element={<Manage />} />
              <Route path="/change-card" element={<ChangeCard />} />
              <Route path="/emailtemplates" element={<EmailTemplates />} />
              <Route path="/resume" element={<Resume />} />
              <Route
                path="/redirect-account-required"
                element={<RedirectAccount />}
              />
              <Route
                path="/start-stable-diffusion"
                element={<StartStableDiffusion />}
              />
              <Route path="/start-chat-gpt" element={<StartChatGPT />} />
              <Route path="/start-chatgpt-go" element={<StartChatGPTgo />} />
              <Route path="/start-chatgpt-v2" element={<StartChatGPTv2 />} />
              <Route path="/text-to-image" element={<Text2Image />} />
              <Route path="/splash" element={<SplashPage />} />
              <Route path="/survey" element={<Survey />} />
              <Route path="/active-session" element={<ActiveSession />} />
              <Route path="/review" element={<Review />} />
              <Route path="/affiliates" element={<Affiliates />} />
              <Route path="/disclaimer" element={<DisclaimerPage />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/select-account-type-d" element={<SelectAccount />} />
              <Route path="/enterprise-plan" element={<EnterprisePlan />} />

              <Route path="/*" element={<NotFoundPage />} />
            </Routes>
          </div>
          {!currentLocation.includes('enterprise-plan') && <Disclaimer/>}
        </Suspense>
      </QueryClientProvider>
      <div className="loader-container">
        <div className="overlay"></div>
        <div className="lds-ai">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <InnerApp />
    </Router>
  );
}

export default App;
