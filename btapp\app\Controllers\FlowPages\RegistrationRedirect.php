<?php

namespace App\Controllers\FlowPages;

use App\Controllers\BaseController;

class RegistrationRedirect extends BaseController
{
    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct() {}

    public function index()
    {
        $flow = btflag('flow');
        $theme = btflag('kt8typtb');

        $this->trackMixpanelEvent($flow, $theme);

        switch ($flow) {
            case '04':
                setcookie('kt8typtb', 'basilisk', time() + (86400 * 30), '/');
                setcookie('kt8typtb', 'basilisk', time() + (86400 * 30), '/','.ai-pro.org');
                die(header("Location: /select-account-type-d/"));
            case '06':
                setcookie('kt8typtb', 'arcana', time() + (86400 * 30), '/');
                setcookie('kt8typtb', 'arcana', time() + (86400 * 30), '/','.ai-pro.org');
                die(header("Location: /select-account-type-d/"));
            default:
                if ($theme == 'arcana_wp') {
                    die(header("Location: /signup/"));
                }
                die(header("Location: /register/"));
        }
    }

    private function trackMixpanelEvent($flow, $theme)
    {
        if (btflag_cookie('admin','0') == '1') {
            return;
        }

        $mixpanelData = [
            'token' => CONFIG_MIXPANEL_TOKEN ?? '',
            'event' => 'Registration Redirect',
            'properties' => [
                'flow' => $flow,
                'theme' => $theme,
                'page' => 'registration-redirect',
                'keyword' => btflag('keyword', ''),
                'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'ctx' => base_url(),
                'timestamp' => time()
            ]
        ];

        // Send to Mixpanel via server-side tracking
        $this->sendMixpanelEvent($mixpanelData);
    }

    private function sendMixpanelEvent($data)
    {
        if (empty($data['token'])) {
            return;
        }

        $postData = json_encode($data);
        $encoded = base64_encode($postData);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.mixpanel.com/track/');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'data=' . $encoded);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_exec($ch);
        curl_close($ch);
    }
}
