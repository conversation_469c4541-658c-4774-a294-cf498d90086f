<?php

namespace App\Controllers\User;
use App\Models\AccountModel;

use App\Controllers\BaseController;

class Home extends BaseController
{
    private $theme = 'arcana';
    private $themeSlug = '';
    private $themePageVersion = 'v1';

    private $pageSlug = '';

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        if(!btsessionIsUserLoggedIn()) {
            header("Location: " . base_url('login'));
            die;
        }

        $start_url = getenv("start.baseURL") ? getenv("start.baseURL") : "https://start.ai-pro.org";
        header("Location: " . $start_url . "/my-account"); // Redirect to start
        die;

        $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
        if( $user['success'] && $user['res'] && $user['res'][0]->login_token ) btsessionSetAccount($user['res'][0], 1);
        else $this->destroySession();
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data();
        $this->theme_pageVersion();

				//This is where it disables the page-access log when using iframe (https://start.ai-pro.org/my-account?KDDbh=on) from wordpress
				$disablePageAccess = isset($_GET['KDDbh']) ? $_GET['KDDbh'] : '';

				if ($disablePageAccess != 'on') {
						$event_data = [
								'page_URL' => base_url() . uri_string(),
								'page_title' => "AI Pro | Member's Area",
						];
						logUserActivity('page-access', $event_data);
				}

        btflag_set('unpFlow', '1'); // #28596

        switch ($this->theme) {
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

		// public function redirectAccountRequired()
    // {
    //     if(btsessionIsUserLoggedIn()) {
    //         $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
    //         if(!$user['success'] || !$user['res']) {
    //             $this->destroySession();
    //             header("Location: " . base_url('redirect-account-required'));
    //             exit;
    //         }
    //         if(!$user['res'][0]->login_token) {
    //             $this->destroySession();
    //             header("Location: " . base_url('login'));
    //             exit;
    //         }
    //     }
    //     $this->theme = btflag('theme', $this->theme);
    //     $uri = current_url(true);
    //     $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

    //     $this->theme_data();
    //     $this->theme_pageVersion();

    //     switch ($this->theme) {
    //         case 'arcana':
    //         default:
    //             $this->theme_arcana();
    //     }
    // }

    public function redirectAccountRequired()
    {
			if(btsessionIsUserLoggedIn()) {
				$user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
				if($user['res'][0]->login_token ||
					($user['success'] && $user['res'])
				) {
					$accountModel = new AccountModel();
					$account = $accountModel->getActiveSubscription(btsessionGet('USER')->user_id);
					if ($account['success']==1 && $account['res']){
						if($account['res'][0]->expired === 'no') {
							btsessionSetAccount($user['res'][0], 1);
							if(btsessionHas("PLAN")) {
								if(strtolower(btsessionGET("PLAN")->plan_type) === "pro") {
									header("Location: " . base_url('my-account'));
									exit;
								} else if(strtolower(btsessionGET("PLAN")->plan_type) === "basic") {
									header("Location: " . base_url('upgrade'));
									exit;
								}
							}
						}
					}
          header("Location: " . base_url('pricing'));
          exit;
				} else {
					$this->destroySession();
				}
			}
      $this->theme = btflag('theme', $this->theme);
      $uri = current_url(true);
      $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

      $this->theme_data();
      $this->theme_pageVersion();

      switch ($this->theme) {
          case 'arcana':
          default:
              $this->theme_arcana();
      }
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        if(btsessionIsUserLoggedIn()) {
            $viewData['USER'] = json_encode([
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'status' => btsessionGet('USER')->status
            ]);
        }
        $viewData['DATA'] = json_encode([
            'downAppBanner' => getenv('DOWNAPPBANNER') ? getenv('DOWNAPPBANNER') : ''
        ]);

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_session' => true,
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => false,
            // 'include_fullstory' => [
            //     'email' => btflag_cookie('user_email', ''),
            // ],
        ];

        if (btflag('admin','0')=='1'){
            // unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }
        
    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}
