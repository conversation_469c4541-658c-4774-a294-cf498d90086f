<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTable extends Migration
{
    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('website', $this->table)) return;
        $this->db->disableForeignKeyChecks();

        $fields = [
            'website' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'default' => 'start.ai-pro.org',
                'after' => 'mode'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);

        $this->db->enableForeignKeyChecks();

    }

    public function down()
    {
        //
    }
}
