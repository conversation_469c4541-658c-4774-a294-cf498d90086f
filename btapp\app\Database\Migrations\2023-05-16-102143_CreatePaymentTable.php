<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreatePaymentTable extends Migration
{
    private $table = "payment";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'acctpmt_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'acctpmt_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'account_id' => [
				'type' => 'INT',
				'constraint' => 11
			],
			'user_id' => [
				'type' => 'INT',
				'constraint' => 11
			],
			'charge_id' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'internal_id' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'amount' => [
				'type' => 'VARCHAR',
				'constraint' => 11,
				'null' => true,
			],
			'currency' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'merchant' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'mode' => [
				'type' => "SET('live','test')",
				'default' => 'live',
            ],
			'refund_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('acctpmt_id', true);
		$this->forge->addKey(['user_id'], false, false, 'start_payment_user_id_IDX');
		$this->forge->addKey(['account_id'], false, false, 'start_payment_account_id_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
