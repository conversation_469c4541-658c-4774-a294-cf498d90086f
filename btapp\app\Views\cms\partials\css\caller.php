    <style nonce="<?= $nonce ?>">
        /* Modal Backdrop Styles */
        .caller-modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #00000033;
            z-index: 9998;
        }

        /* Modal Container Styles */
        .caller-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #eaeaea;
            border-radius: 12px;
            z-index: 9999;
            width: calc(90% - 16px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            padding: 24px;
        }

        @media only screen and (min-width: 768px) {
            .caller-modal {
                width: 440px;
                margin: 0;
            }
        }

        /* Modal Header */
        .caller-modal h2 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
            color: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .caller-modal h2::after {
            content: "x";
            cursor: pointer;
            font-size: 22px;
            color: #777;
            font-weight: normal;
        }

        /* Subtitle */
        .caller-modal p {
            margin: 0 0 20px 0;
            font-size: 14px;
            color: #333;
        }

        /* Form Labels */
        .caller-modal label {
            display: block;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 500;
        }

        /* Form Inputs */
        .caller-modal input[type="text"],
        .caller-modal input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #00000033;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }

        /* Hide break elements */
        .caller-modal br {
            display: none;
        }

        /* Continue Button */
        .caller-modal button[type="submit"] {
            width: 100%;
            padding: 14px;
            background: linear-gradient(to right, #4169e1, #4b9cf5);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 5px;
        }

        /* Hide close button */
        .caller-modal button[type="button"] {
            display: none;
        }
    </style>