                  <style nonce="<?= $nonce ?>">
                    .search-container {
                      display: flex;
                      align-items: center;
                      width: 100%;
                      max-width: 400px;
                      background-color: #fff;
                      border: 1px solid #ccc;
                      border-radius: 4px;
                      overflow: hidden;
                      height: 40px;
                    }
                  
                    .search-container:focus-within {
                      border-color: gray;  /* Highlight entire box on focus */
                    }
                  
                    .searchme {
                      display: flex !important;
                      align-items: center !important;
                      width: 100% !important;
                      max-width: 400px !important;
                      background-color: #fff !important;
                      border: 1px solid transparent !important;
                      border-radius: 4px !important;
                      overflow: hidden !important;
                      height: 40px !important;
                    }
                  
                    .search-button {
                      border: none;
                      outline: none;
                      background-color: transparent;
                      padding: 10px;
                      cursor: pointer;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      box-shadow: none;
                      height: 100%;
                    }
                  
                    .search-button svg {
                      stroke: #333;
                    }
                  
                    .search-results {
                      display: none;
                      position: absolute;
                      top: 100%;
                      left: 0;
                      right: 0;
                      background: white;
                      border: 1px solid #ccc;
                      z-index: 1000;
                    }
                  </style>




<?php /* ajax search functionality (to do)
                <script>
                              document.addEventListener('DOMContentLoaded', () => {
                                const input = document.getElementById('search-input');
                                const results = document.getElementById('search-results');
                                let timer;
                              
                                input.addEventListener('input', () => {
                                  clearTimeout(timer);
                                  const term = input.value.trim();
                                
                                  if (term.length < 2) {
                                    results.style.display = 'none';
                                    return;
                                  }
                                
                                  timer = setTimeout(() => {
                                    fetch(`?ajax=1&q=${encodeURIComponent(term)}`)
                                      .then(res => res.json())
                                      .then(data => {
                                        if (data.results && data.results.length > 0) {
                                          results.innerHTML = data.results.map(item => `
                                            <div>
                                              <a href="${item.url}">${item.title}</a>
                                            </div>
                                          `).join('');
                                          results.style.display = 'block';
                                        } else {
                                          results.innerHTML = '<div>No results found</div>';
                                          results.style.display = 'block';
                                        }
                                      })
                                      .catch(console.error);
                                  }, 300);
                                });
                              });
                </script>
*/?>
                    <header class="entry-header">            

                    <h1 class="page-title" title="<?php echo $title ?>" itemprop="headline"><?php echo $title ?></h1>

                    <form method="get" id="search-form" class="search-container">
                      <input
                        type="search"
                        class="searchme"
                        required
                        placeholder="Search"
                        name="q"
                        autocomplete="off"
                        aria-label="Search for..."
                        id="search-input"
                      />
                      <button type="submit" class="search-button" aria-label="Search">
                        <!-- Magnifying Glass SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <circle cx="11" cy="11" r="8"></circle>
                          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                      </button>

                      <div id="search-results" class="search-results"></div>
                      <div class="screen-reader-text" aria-live="polite" role="status">No results</div>
                    </form>


<?php /*

// old serach box code

                          </header>

                                <div class="page-description ct-hidden-sm">
                                    <div style="width: 100%">
                                        <form role="search" method="get" class="search-form" aria-haspopup="listbox" data-live-results="thumbs">
                                            <input type="search"  class="searchme" required placeholder="Search" value="" name="s" autocomplete="off" title="Search for..." aria-label="Search for..." id="search-input">
                                            <span id="clear-input-btn" class="clear-icon" style="display:none;">X</span>
                                            <button type="submit" class="search-submit" aria-label="Search button">
                                                <svg class="ct-icon" aria-hidden="true" width="15" height="15" viewBox="0 0 15 15"><path d="M14.8,13.7L12,11c0.9-1.2,1.5-2.6,1.5-4.2c0-3.7-3-6.8-6.8-6.8S0,3,0,6.8s3,6.8,6.8,6.8c1.6,0,3.1-0.6,4.2-1.5l2.8,2.8c0.1,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2C15.1,14.5,15.1,14,14.8,13.7z M1.5,6.8c0-2.9,2.4-5.2,5.2-5.2S12,3.9,12,6.8S9.6,12,6.8,12S1.5,9.6,1.5,6.8z"/></svg>
                                                <span data-loader="circles"><span></span><span></span><span></span></span>
                                            </button>

                                            <div class="screen-reader-text" aria-live="polite" role="status">
                                                No results</div>
                                        </form>
                                    </div>
                                </div>



5. Full Result View (search_results.php)
<h1>Search results for "<?= esc($query) ?>"</h1>
<?php if (empty($posts)): ?>
  <p>No results found.</p>
<?php else: ?>
  <ul>
    <?php foreach ($posts as $p): ?>
      <?php $a = $p['attributes']; ?>
      <li><a href="/posts/<?= $a['slug'] ?>"><?= esc($a['title']) ?></a></li>
    <?php endforeach; ?>
  </ul>
<?php endif; ?>


$routes->get('search', 'Search::index');

namespace App\Controllers;

class Search extends BaseController
{
    public function index()
    {
        $q = $this->request->getGet('q');
        $isAjax = $this->request->getGet('ajax') === '1';

        // Query Strapi for matching posts
        $client = \Config\Services::curlrequest();
        $response = $client->get('https://your-strapi/api/posts', [
            'query' => [
                'filters[title][$containsi]' => $q,
                'pagination[limit]' => $isAjax ? 5 : 20,
                'fields' => ['title','slug']
            ]
        ]);

        $apiData = json_decode($response->getBody(), true);
        $posts = $apiData['data'] ?? [];

        if ($isAjax) {
            $results = array_map(fn($p) => [
                'title' => $p['attributes']['title'],
                'url' => '/posts/' . $p['attributes']['slug']
            ], $posts);
            return $this->response->setJSON(['results' => $results]);
        }

        // Normal page load
        return view('search_results', ['posts' => $posts, 'query' => $q]);
    }
}




<script>
document.addEventListener('DOMContentLoaded', () => {
  const input = document.getElementById('search-input');
  const results = document.getElementById('search-results');
  let timer;

  input.addEventListener('input', () => {
    clearTimeout(timer);
    const term = input.value.trim();

    if (term.length < 2) {
      results.style.display = 'none';
      return;
    }

    timer = setTimeout(() => {
      fetch(`/search?ajax=1&q=${encodeURIComponent(term)}`)
        .then(res => res.json())
        .then(data => {
          if (data.results && data.results.length > 0) {
            results.innerHTML = data.results.map(item => `
              <div>
                <a href="${item.url}">${item.title}</a>
              </div>
            `).join('');
            results.style.display = 'block';
          } else {
            results.innerHTML = '<div>No results found</div>';
            results.style.display = 'block';
          }
        })
        .catch(console.error);
    }, 300);
  });
});
</script>

<form id="live-search-form" action="/search" method="get" autocomplete="off">
  <input type="search" name="q" id="search-input" placeholder="Search…" required />
  <button type="submit">Search</button>
</form>
<div id="search-results" style="display:none;"></div>
*/ ?>