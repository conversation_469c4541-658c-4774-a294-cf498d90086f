DEBUG - 2025-05-29 01:10:08 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:10:08 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cdn-cgi/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:08 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cdn-cgi/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:15 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:10:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:23 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:10:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:29 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:10:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/a-complete-guide-to-ai-text-generators/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:10:29 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:10:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 01:15:21 --> btdbFindBy ---> 
DEBUG - 2025-05-29 01:15:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:09:41 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:09:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 02:09:41 --> Attempt to read property "type" on array
in APPPATH\Controllers\Articles.php on line 65.
 1 APPPATH\Controllers\Articles.php(65): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "type" on array', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 65)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'the-power-of-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:10:12 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:10:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:10:38 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:10:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:10:45 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:10:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:11:49 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:11:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:12:05 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:12:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 02:12:05 --> Attempt to read property "type" on array
in APPPATH\Controllers\Articles.php on line 65.
 1 APPPATH\Controllers\Articles.php(65): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "type" on array', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 65)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'the-power-of-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:12:13 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:12:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:12:33 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:12:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:14:19 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:14:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:14:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:14:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:15:10 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:15:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:15:29 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:15:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:16:00 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:16:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:16:09 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:16:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:16:23 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:16:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:16:59 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:16:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:17:14 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:17:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:19:39 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:19:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:20:39 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:20:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:22:08 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:22:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 02:22:08 --> date_format(): Argument #1 ($object) must be of type DateTimeInterface, string given
in APPPATH\Controllers\Articles.php on line 63.
 1 APPPATH\Controllers\Articles.php(63): date_format('2024-03-01 08:46:27', 'c')
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:22:49 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:22:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:15 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:35 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/the-power-of-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-101-a-beginners-guide-to-understanding-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:35:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:35:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:36:05 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:36:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:36:06 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:36:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:40:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:40:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:40:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:40:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:40:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:40:32 --> REQ ----------->
INFO - 2025-05-29 02:40:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 02:40:46 --> REQ ----------->
INFO - 2025-05-29 02:40:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 02:55:02 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:55:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-29 02:55:02 --> mysqli_sql_exception: Column 'id' in where clause is ambiguous in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-p...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-p...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-p...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-p...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guid...')
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < :id:
AND `post_status` = :post_status:
AND `post_type` = :post_type:
 LIMIT 1', [...], false)
 5 APPPATH\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:55:20 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:55:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-29 02:55:20 --> mysqli_sql_exception: Column 'id' in where clause is ambiguous in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-p...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-p...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-p...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-p...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guid...')
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `id` < :id:
AND `post_status` = :post_status:
AND `post_type` = :post_type:
 LIMIT 1', [...], false)
 5 APPPATH\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:55:33 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:55:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-29 02:55:33 --> mysqli_sql_exception: Column 'post_status' in where clause is ambiguous in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-p...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-p...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-p...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-p...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guid...')
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` < \'20978\'
AND `post_status` = \'publish\'
AND `post_type` = \'post\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-posts`.*, `start-yoast_indexable`.`open_graph_image_meta` as `og_image_meta`
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` < :posts.id:
AND `post_status` = :post_status:
AND `post_type` = :post_type:
 LIMIT 1', [...], false)
 5 APPPATH\Models\WPPostModel.php(35): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getPreviousPost('20978')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:55:56 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:55:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:56:19 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:56:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 02:56:19 --> Attempt to read property "og_image_meta" on array
in APPPATH\Controllers\Articles.php on line 70.
 1 APPPATH\Controllers\Articles.php(70): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "og_image_meta" on array', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 70)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 02:56:49 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:56:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 02:57:14 --> btdbFindBy ---> 
DEBUG - 2025-05-29 02:57:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:10:22 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:10:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:10:22 --> Attempt to read property "url" on string
in APPPATH\Views\articles\index.php on line 377.
 1 APPPATH\Views\articles\index.php(377): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on string', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 377)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(68): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:10:40 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:10:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:10:41 --> Attempt to read property "url" on string
in APPPATH\Controllers\Articles.php on line 66.
 1 APPPATH\Controllers\Articles.php(66): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on string', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 66)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:11:23 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:11:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-29 03:11:23 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '("url", `start-open_graph_image_meta->>"$`.`url"`, "width", `start-open_graph...' at line 1 in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-p...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-p...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-p...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-p...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\WPPostModel.php(51): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('20978')
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guid...')
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT `start-posts`.*, yoast_indexable.open_graph_image_meta as JSON_OBJECT("url", `start-open_graph_image_meta->>"$`.`url"`, "width", `start-open_graph_image_meta->>"$`.`width"`, "height", `start-open_graph_image_meta->>"$`.`height"`, "type", `start-open_graph_image_meta->>"$`.`type"`, "alt", open_graph_image_meta->>"$.alt") as og_image_meta
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` > \'20978\'
AND `start-posts`.`post_status` = \'publish\'
AND `start-posts`.`post_type` = \'post\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `start-posts`.*, yoast_indexable.open_graph_image_meta as JSON_OBJECT("url", `start-open_graph_image_meta->>"$`.`url"`, "width", `start-open_graph_image_meta->>"$`.`width"`, "height", `start-open_graph_image_meta->>"$`.`height"`, "type", `start-open_graph_image_meta->>"$`.`type"`, "alt", open_graph_image_meta->>"$.alt") as og_image_meta
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` > \'20978\'
AND `start-posts`.`post_status` = \'publish\'
AND `start-posts`.`post_type` = \'post\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `start-posts`.*, yoast_indexable.open_graph_image_meta as JSON_OBJECT("url", `start-open_graph_image_meta->>"$`.`url"`, "width", `start-open_graph_image_meta->>"$`.`width"`, "height", `start-open_graph_image_meta->>"$`.`height"`, "type", `start-open_graph_image_meta->>"$`.`type"`, "alt", open_graph_image_meta->>"$.alt") as og_image_meta
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` > \'20978\'
AND `start-posts`.`post_status` = \'publish\'
AND `start-posts`.`post_type` = \'post\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT `start-posts`.*, yoast_indexable.open_graph_image_meta as JSON_OBJECT("url", `start-open_graph_image_meta->>"$`.`url"`, "width", `start-open_graph_image_meta->>"$`.`width"`, "height", `start-open_graph_image_meta->>"$`.`height"`, "type", `start-open_graph_image_meta->>"$`.`type"`, "alt", open_graph_image_meta->>"$.alt") as og_image_meta
FROM `start-posts`
LEFT JOIN `start-yoast_indexable` ON `start-yoast_indexable`.`object_id` = `start-posts`.`id`
WHERE `start-posts`.`id` > :posts.id:
AND `start-posts`.`post_status` = :posts.post_status:
AND `start-posts`.`post_type` = :posts.post_type:
 LIMIT 1', [...], false)
 5 APPPATH\Models\WPPostModel.php(51): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Controllers\Articles.php(37): App\Models\WPPostModel->getNextPost('20978')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:12:08 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:12:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:12:08 --> Attempt to read property "url" on string
in APPPATH\Controllers\Articles.php on line 67.
 1 APPPATH\Controllers\Articles.php(67): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on string', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 67)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'a-complete-guide-to-ai-text-generators')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:14:23 --> REQ ----------->
INFO - 2025-05-29 03:14:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:14:24 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:14:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:14:42 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:14:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:14:59 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:14:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:15:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:15:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-101-a-beginners-guide-to-understanding-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:15:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:56 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:15:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/the-power-of-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:15:56 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:15:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:15:56 --> Undefined array key 0
in APPPATH\Controllers\Articles.php on line 38.
 1 APPPATH\Controllers\Articles.php(38): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key 0', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 38)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'the-power-of-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:16:13 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:16:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:16:13 --> Undefined array key 0
in APPPATH\Controllers\Articles.php on line 38.
 1 APPPATH\Controllers\Articles.php(38): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key 0', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 38)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'the-power-of-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:16:25 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:16:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:16:25 --> Attempt to read property "url" on string
in APPPATH\Views\articles\index.php on line 403.
 1 APPPATH\Views\articles\index.php(403): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "url" on string', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 403)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(71): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'the-power-of-ai')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:16:34 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:16:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:16:55 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:16:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:17:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:17:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:18:01 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:18:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:20:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:20:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:20:12 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:20:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:20:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:20:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:20:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/introduction-to-chatgpt/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'introduction-to-chatgpt'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/introduction-to-chatgpt/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'introduction-to-chatgpt'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'introduction-to-chatgpt'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/the-future-of-work-how-ai-is-changing-the-way-we-approach-routine-tasks/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-future-of-work-how-ai-is-changing-the-way-we-approach-routine-tasks'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-future-of-work-how-ai-is-changing-the-way-we-approach-routine-tasks'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:21:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:21:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:22:15 --> REQ ----------->
INFO - 2025-05-29 03:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:22:26 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:22:26 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/poem-generator-guide-everything-you-need-to-know/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:22:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:22:26 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:22:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:22:43 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:22:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:22:43 --> Undefined property: stdClass::$og_image_meta
in APPPATH\Models\WPPostModel.php on line 40.
 1 APPPATH\Models\WPPostModel.php(40): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$og_image_meta', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Models\\WPPostModel.php', 40)
 2 APPPATH\Controllers\Articles.php(36): App\Models\WPPostModel->getPreviousPost('21020')
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'poem-generator-guide-everything-you-need-to-know')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:22:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:22:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:22:57 --> Undefined property: stdClass::$og_image_meta
in APPPATH\Views\articles\index.php on line 377.
 1 APPPATH\Views\articles\index.php(377): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$og_image_meta', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 377)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'poem-generator-guide-everything-you-need-to-know')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:23:24 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:23:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 03:23:24 --> Undefined property: stdClass::$og_image_meta
in APPPATH\Views\articles\index.php on line 377.
 1 APPPATH\Views\articles\index.php(377): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$og_image_meta', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 377)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(64): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'poem-generator-guide-everything-you-need-to-know')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 03:23:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:23:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:23:53 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:23:53 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-the-difference-between-llms-and-generative-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:23:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-the-difference-between-llms-and-generative-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:23:53 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:23:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-the-difference-between-llms-and-generative-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:24:22 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:24:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-the-difference-between-llms-and-generative-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:24:24 --> REQ ----------->
INFO - 2025-05-29 03:24:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:24:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:24:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/maximizing-time-with-the-help-of-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:24:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'maximizing-time-with-the-help-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:24:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:24:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'maximizing-time-with-the-help-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:26 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:26:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'maximizing-time-with-the-help-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:26:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/transforming-the-way-we-communicate-ai-and-its-impact-on-language-and-communication/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'transforming-the-way-we-communicate-ai-and-its-impact-on-language-and-communication'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:26:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'transforming-the-way-we-communicate-ai-and-its-impact-on-language-and-communication'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:34 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:26:34 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'artificial-intelligence-vs-machine-learning-whats-the-difference'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:26:51 --> REQ ----------->
INFO - 2025-05-29 03:26:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:28:15 --> REQ ----------->
INFO - 2025-05-29 03:28:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:28:26 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:28:26 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:28:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:28:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:31:00 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:31:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:32:08 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:32:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:41:26 --> REQ ----------->
INFO - 2025-05-29 03:41:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:41:51 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:41:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tutorials/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:41:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:41:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tutorials/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:41:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:41:51 --> REQ ----------->
INFO - 2025-05-29 03:41:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:41:58 --> REQ ----------->
INFO - 2025-05-29 03:41:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:42:10 --> REQ ----------->
INFO - 2025-05-29 03:42:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:42:12 --> REQ ----------->
INFO - 2025-05-29 03:42:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:43:46 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:43:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tutorials/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:43:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:43:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tutorials/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:43:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:43:46 --> REQ ----------->
INFO - 2025-05-29 03:43:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:43:47 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:43:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/multiple-ai-response-generator/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'multiple-ai-response-generator'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/multiple-ai-response-generator/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'multiple-ai-response-generator'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/search-tool/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'search-tool'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/search-tool/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'search-tool'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:16 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:16 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/toolbox/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'toolbox'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:16 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/toolbox/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'toolbox'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:16 --> REQ ----------->
INFO - 2025-05-29 03:59:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:16 --> REQ ----------->
INFO - 2025-05-29 03:59:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:16 --> REQ ----------->
INFO - 2025-05-29 03:59:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:17 --> REQ ----------->
INFO - 2025-05-29 03:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:18 --> REQ ----------->
INFO - 2025-05-29 03:59:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:18 --> REQ ----------->
INFO - 2025-05-29 03:59:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:19 --> REQ ----------->
INFO - 2025-05-29 03:59:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:19 --> REQ ----------->
INFO - 2025-05-29 03:59:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:19 --> REQ ----------->
INFO - 2025-05-29 03:59:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:20 --> REQ ----------->
INFO - 2025-05-29 03:59:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:21 --> REQ ----------->
INFO - 2025-05-29 03:59:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:28 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:28 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/recipe-maker/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'recipe-maker'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:28 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/recipe-maker/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'recipe-maker'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:28 --> REQ ----------->
INFO - 2025-05-29 03:59:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/trip-planner/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'trip-planner'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/trip-planner/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'trip-planner'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> REQ ----------->
INFO - 2025-05-29 03:59:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/homework-helper/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'homework-helper'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/homework-helper/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'homework-helper'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/grammar-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'grammar-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/grammar-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'grammar-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> REQ ----------->
INFO - 2025-05-29 03:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/pdf-reader/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'pdf-reader'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/pdf-reader/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'pdf-reader'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:30 --> REQ ----------->
INFO - 2025-05-29 03:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:30 --> REQ ----------->
INFO - 2025-05-29 03:59:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:31 --> REQ ----------->
INFO - 2025-05-29 03:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:31 --> REQ ----------->
INFO - 2025-05-29 03:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:31 --> REQ ----------->
INFO - 2025-05-29 03:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:31 --> REQ ----------->
INFO - 2025-05-29 03:59:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:32 --> REQ ----------->
INFO - 2025-05-29 03:59:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:33 --> REQ ----------->
INFO - 2025-05-29 03:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:33 --> REQ ----------->
INFO - 2025-05-29 03:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:33 --> REQ ----------->
INFO - 2025-05-29 03:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:34 --> REQ ----------->
INFO - 2025-05-29 03:59:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:34 --> REQ ----------->
INFO - 2025-05-29 03:59:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:35 --> REQ ----------->
INFO - 2025-05-29 03:59:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:36 --> REQ ----------->
INFO - 2025-05-29 03:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:37 --> REQ ----------->
INFO - 2025-05-29 03:59:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:37 --> REQ ----------->
INFO - 2025-05-29 03:59:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:37 --> REQ ----------->
INFO - 2025-05-29 03:59:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 03:59:56 --> btdbFindBy ---> 
DEBUG - 2025-05-29 03:59:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/pdf-reader/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'pdf-reader'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/pdf-reader/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'pdf-reader'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 03:59:57 --> REQ ----------->
INFO - 2025-05-29 03:59:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:00:08 --> REQ ----------->
INFO - 2025-05-29 04:00:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:00:09 --> REQ ----------->
INFO - 2025-05-29 04:00:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:00:14 --> REQ ----------->
INFO - 2025-05-29 04:00:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:00:24 --> REQ ----------->
INFO - 2025-05-29 04:00:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:00:54 --> REQ ----------->
INFO - 2025-05-29 04:00:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:01:01 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:01:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:32 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:02:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:55 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:02:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:02:58 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tutorials/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:58 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tutorials/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:02:59 --> REQ ----------->
INFO - 2025-05-29 04:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:03:00 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:03:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:03:24 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:03:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 04:03:27 --> Invalid file: "articles/course.php"
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(214): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('articles/course.php')
 2 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/course.php', [], true)
 3 APPPATH\Controllers\Articles.php(19): view('articles/course.php')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('course')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 04:04:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:04:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:04:59 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:04:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:05:05 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:05:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-image'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:05:05 --> REQ ----------->
INFO - 2025-05-29 04:05:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:05:17 --> REQ ----------->
INFO - 2025-05-29 04:05:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:05:19 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:05:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:07 --> REQ ----------->
INFO - 2025-05-29 04:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:22:10 --> REQ ----------->
INFO - 2025-05-29 04:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:22:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:22:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:22:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:37 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:22:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:56 --> REQ ----------->
INFO - 2025-05-29 04:23:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:23:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:24:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:38 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:25:38 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:38 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:42 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:25:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:42 --> btdbFindBy ---> 
DEBUG - 2025-05-29 04:25:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 04:25:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:15 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/contact/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/contact/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tutorials/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tutorials/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tutorials'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL48502198392/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48502198392'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL48502198392/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48502198392'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL48502198392/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48502198392'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL48502198392/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48502198392'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:28 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> 
CRITICAL - 2025-05-29 07:03:37 --> include(../../global.php): Failed to open stream: No such file or directory
in APPPATH\Views\articles\tutorials.php on line 28.
 1 APPPATH\Views\articles\tutorials.php(28): CodeIgniter\Debug\Exceptions->errorHandler(2, 'include(../../global.php): Failed to open stream: No such file or directory', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\tutorials.php', 28)
 2 APPPATH\Views\articles\tutorials.php(28): include()
 3 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\tutorials.php')
 4 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 5 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/tutorials.php', [], true)
 6 APPPATH\Controllers\Articles.php(19): view('articles/tutorials.php')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('tutorials')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:03:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:04:38 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:06:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:37 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:07:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:24 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:24 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:24 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:25 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/tag/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/tag/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'tag'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:51 --> btdbFindBy ---> 
DEBUG - 2025-05-29 07:08:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 07:08:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/contact/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/contact/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL48508650745/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48508650745'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL48508650745/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48508650745'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:51 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:50:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:50:58 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:58 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:50:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:52:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:57:45 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:57:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 08:58:04 --> btdbFindBy ---> 
DEBUG - 2025-05-29 08:58:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-29 08:58:13 --> include(../../global.php): Failed to open stream: No such file or directory
in APPPATH\Views\articles\articles.php on line 113.
 1 APPPATH\Views\articles\articles.php(113): CodeIgniter\Debug\Exceptions->errorHandler(2, 'include(../../global.php): Failed to open stream: No such file or directory', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\articles.php', 113)
 2 APPPATH\Views\articles\articles.php(113): include()
 3 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\articles.php')
 4 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 5 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/articles.php', [], true)
 6 APPPATH\Controllers\Articles.php(19): view('articles/articles.php')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('articles')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 10:01:09 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:01:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:01:20 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:01:20 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/asdadads/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asdadads'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:20 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/asdadads/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asdadads'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:22 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:01:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL48512882297/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48512882297'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL48512882297/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL48512882297'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:01:22 --> REQ ----------->
INFO - 2025-05-29 10:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:02:02 --> REQ ----------->
INFO - 2025-05-29 10:02:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:02:05 --> REQ ----------->
INFO - 2025-05-29 10:02:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:02:25 --> REQ ----------->
INFO - 2025-05-29 10:02:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:26:54 --> btdbFindBy ---> 
CRITICAL - 2025-05-29 10:37:51 --> Too few arguments to function App\Controllers\Redirect::page(), 0 passed in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php on line 932 and exactly 1 expected
in APPPATH\Controllers\Redirect.php on line 36.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page()
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 10:38:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:38:11 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:38:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:20 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:38:20 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '//'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:20 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles//'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:20 --> REQ ----------->
INFO - 2025-05-29 10:38:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:38:34 --> REQ ----------->
INFO - 2025-05-29 10:38:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:38:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:38:48 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '//'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:48 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-make-a-twitter-thread-any-topic/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:38:48 --> REQ ----------->
INFO - 2025-05-29 10:38:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:38:48 --> REQ ----------->
INFO - 2025-05-29 10:38:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:38:55 --> REQ ----------->
INFO - 2025-05-29 10:38:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:38:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:38:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:39:48 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:39:48 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:39:51 --> btdbFindBy ---> 
CRITICAL - 2025-05-29 10:39:51 --> Array to string conversion
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Array to string conversion', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page()
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 10:39:56 --> btdbFindBy ---> 
CRITICAL - 2025-05-29 10:39:57 --> Array to string conversion
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Array to string conversion', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page()
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 10:40:52 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:49:37 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:49:38 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:49:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:42 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:49:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:51 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:49:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '//'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles//'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:49:51 --> REQ ----------->
INFO - 2025-05-29 10:49:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:51:28 --> REQ ----------->
INFO - 2025-05-29 10:51:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:51:28 --> REQ ----------->
INFO - 2025-05-29 10:51:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:51:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:51:41 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:41 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:51:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:59 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:51:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-make-a-twitter-thread-any-topic'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:09 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:52:09 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:09 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:09 --> REQ ----------->
INFO - 2025-05-29 10:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:52:12 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:52:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:12 --> REQ ----------->
INFO - 2025-05-29 10:52:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:52:12 --> REQ ----------->
INFO - 2025-05-29 10:52:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:52:16 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:52:16 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:16 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:52:16 --> REQ ----------->
INFO - 2025-05-29 10:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:52:16 --> REQ ----------->
INFO - 2025-05-29 10:52:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:52:25 --> REQ ----------->
INFO - 2025-05-29 10:52:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-29 10:52:26 --> Undefined variable $slug
in APPPATH\Controllers\Redirect.php on line 47.
 1 APPPATH\Controllers\Redirect.php(47): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $slug', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 47)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('learn-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-29 10:52:26 --> REQ ----------->
INFO - 2025-05-29 10:52:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:54:13 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:54:13 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:13 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:13 --> REQ ----------->
INFO - 2025-05-29 10:54:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:54:17 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:54:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:17 --> REQ ----------->
INFO - 2025-05-29 10:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:54:57 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:54:57 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:57 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:57 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:54:57 --> REQ ----------->
INFO - 2025-05-29 10:54:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:55:38 --> REQ ----------->
INFO - 2025-05-29 10:55:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:55:40 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:55:40 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/learn-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:55:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:55:40 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/learn-ai/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:55:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:55:40 --> REQ ----------->
INFO - 2025-05-29 10:55:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:55:40 --> REQ ----------->
INFO - 2025-05-29 10:55:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:56:02 --> REQ ----------->
INFO - 2025-05-29 10:56:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:56:03 --> REQ ----------->
INFO - 2025-05-29 10:56:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:58:25 --> REQ ----------->
INFO - 2025-05-29 10:58:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:58:27 --> REQ ----------->
INFO - 2025-05-29 10:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:58:31 --> REQ ----------->
INFO - 2025-05-29 10:58:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:58:36 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:58:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:58:36 --> REQ ----------->
INFO - 2025-05-29 10:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 10:58:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:58:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:58:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:58:58 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:58:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 10:59:05 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:59:05 --> btdbFindBy ---> 
DEBUG - 2025-05-29 10:59:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-05-29 12:29:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:29:59 --> btdbFindBy ---> 
DEBUG - 2025-05-29 12:29:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748521798589/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:29:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748521798589'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:29:59 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748521798589/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:29:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748521798589'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:29:59 --> REQ ----------->
INFO - 2025-05-29 12:29:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:30:13 --> REQ ----------->
INFO - 2025-05-29 12:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:30:15 --> REQ ----------->
INFO - 2025-05-29 12:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:30:16 --> REQ ----------->
INFO - 2025-05-29 12:30:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:33:39 --> REQ ----------->
INFO - 2025-05-29 12:33:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:33:44 --> REQ ----------->
INFO - 2025-05-29 12:33:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-29 12:33:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:33:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 12:33:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748522024295/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:33:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748522024295'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:33:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748522024295/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:33:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748522024295'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 12:33:44 --> REQ ----------->
INFO - 2025-05-29 12:33:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:34:17 --> REQ ----------->
INFO - 2025-05-29 12:34:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:38:03 --> REQ ----------->
INFO - 2025-05-29 12:38:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:45:04 --> REQ ----------->
INFO - 2025-05-29 12:45:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 12:45:06 --> REQ ----------->
INFO - 2025-05-29 12:45:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-29 14:20:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:20:31 --> btdbFindBy ---> 
DEBUG - 2025-05-29 14:20:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748528430782/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528430782'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748528430782/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528430782'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:31 --> REQ ----------->
INFO - 2025-05-29 14:20:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:20:38 --> REQ ----------->
INFO - 2025-05-29 14:20:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:20:41 --> REQ ----------->
INFO - 2025-05-29 14:20:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-29 14:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:20:44 --> btdbFindBy ---> 
DEBUG - 2025-05-29 14:20:44 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748528444714/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528444714'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:44 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748528444714/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528444714'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:44 --> REQ ----------->
INFO - 2025-05-29 14:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-29 14:20:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:20:50 --> btdbFindBy ---> 
DEBUG - 2025-05-29 14:20:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748528450745/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528450745'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:50 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748528450745/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528450745'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:20:50 --> REQ ----------->
INFO - 2025-05-29 14:20:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:22:10 --> REQ ----------->
INFO - 2025-05-29 14:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-05-29 14:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:22:12 --> REQ ----------->
INFO - 2025-05-29 14:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-29 14:22:12 --> btdbFindBy ---> 
DEBUG - 2025-05-29 14:22:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/%REACT_APP_BTUTIL_CSS_URL%1748528532482/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:22:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528532482'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:22:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/%REACT_APP_BTUTIL_CSS_URL%1748528532482/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:22:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '%REACT_APP_BTUTIL_CSS_URL%1748528532482'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-29 14:22:12 --> REQ ----------->
INFO - 2025-05-29 14:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
