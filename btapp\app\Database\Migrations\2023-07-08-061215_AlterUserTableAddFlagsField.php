<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlterUserTableAddFlagsField extends Migration
{
    private $table = "user";

    public function up()
    {
        if ($this->db->fieldExists('flags', $this->table)) return;

        $this->db->disableForeignKeyChecks();
        $fields = [
            'flags' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'migrated'
            ]
        ];
        $this->forge->addColumn($this->table, $fields);
        $this->db->enableForeignKeyChecks();
    }

    public function down()
    {

    }

}
