<script nonce="<?= $nonce ?>" type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/identify_935b0d03.js"></script>
<script nonce="<?= $nonce ?>" type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/main.MWNiZTQ2MTFhMQ.js" data-id="CMB3VLJC77UDE1VA04E0"></script>
<script nonce="<?= $nonce ?>" type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=CMB3VLJC77UDE1VA04E0&amp;lib=ttq"></script>
<script nonce="<?= $nonce ?>" async="" crossorigin="anonymous" src="https://edge.fullstory.com/s/fs.js"></script>

<!-- Fullstory Code Start -->
	<script nonce="<?= $nonce ?>">
		window['_fs_host'] = 'fullstory.com';
		window['_fs_script'] = 'edge.fullstory.com/s/fs.js';
		window['_fs_org'] = '129M5P';
		window['_fs_namespace'] = 'FS';
		(function(m,n,e,t,l,o,g,y){
			if (e in m) {if(m.console && m.console.log) { m.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].');} return;}
			g=m[e]=function(a,b,s){g.q?g.q.push([a,b,s]):g._api(a,b,s);};g.q=[];
			o=n.createElement(t);o.async=1;o.crossOrigin='anonymous';o.src='https://'+_fs_script;
			y=n.getElementsByTagName(t)[0];y.parentNode.insertBefore(o,y);
			g.identify=function(i,v,s){g(l,{uid:i},s);if(v)g(l,v,s)};g.setUserVars=function(v,s){g(l,v,s)};g.event=function(i,v,s){g('event',{n:i,p:v},s)};
			g.anonymize=function(){g.identify(!!0)};
			g.shutdown=function(){g("rec",!1)};g.restart=function(){g("rec",!0)};
			g.log = function(a,b){g("log",[a,b])};
			g.consent=function(a){g("consent",!arguments.length||a)};
			g.identifyAccount=function(i,v){o='account';v=v||{};v.acctId=i;g(o,v)};
			g.clearUserCookie=function(){};
			g.setVars=function(n, p){g('setVars',[n,p]);};
			g._w={};y='XMLHttpRequest';g._w[y]=m[y];y='fetch';g._w[y]=m[y];
			if(m[y])m[y]=function(){return g._w[y].apply(this,arguments)};
			g._v="1.3.0";
		})(window,document,window['_fs_namespace'],'script','user');

		FS.identify("<?php echo isset($_COOKIE['user_email']) ? $_COOKIE['user_email'] : '' ?>", {email: "<?php echo isset($_COOKIE['user_email']) ? $_COOKIE['user_email'] : '' ?>"});
	</script>
<!-- Fullstory Code End-->

<!-- Facebook Pixel Code -->
	<script nonce="<?= $nonce ?>">
	!function(f,b,e,v,n,t,s)
	{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
	n.callMethod.apply(n,arguments):n.queue.push(arguments)};
	if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
	n.queue=[];t=b.createElement(e);t.async=!0;
	t.src=v;s=b.getElementsByTagName(e)[0];
	s.parentNode.insertBefore(t,s)}(window, document,'script',
	'https://connect.facebook.net/en_US/fbevents.js');
	fbq('init', '975784236794755');
	fbq('track', 'PageView');
	</script>
	<noscript><img height="1" width="1" style="display:none"
	src="https://www.facebook.com/tr?id=975784236794755&ev=PageView&noscript=1"
	/></noscript>
<!-- Facebook Pixel Code -->

<!-- Twitter conversion tracking base code -->
	<script nonce="<?= $nonce ?>">
    !function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
    },s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
    a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
    twq('config','oebtr');
	</script>
<!-- End Twitter conversion tracking base code -->

<!-- Tiktok conversion tracking base code -->
	<script nonce="<?= $nonce ?>">
    !function (w, d, t) {
    w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};

    ttq.load('CMB3VLJC77UDE1VA04E0');
    ttq.page();
    }(window, document, 'ttq');
	</script>
<!-- END Tiktok conversion tracking base code -->

<!-- Rewardful Code Start -->
	<script nonce="<?= $nonce ?>" defer>(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');</script>
	<script nonce="<?= $nonce ?>" defer async src='https://r.wdfl.co/rw.js' data-rewardful='c8db04'></script>
<!-- Rewardful Code End -->