<?php

namespace App\Controllers;

class Redirect extends BaseController
{
    public function register()
    {
        $flow = btflag('flow');
        $theme = btflag('kt8typtb');

        switch ($flow) {
            case '04':
            case '06':
                die(header("Location: /select-account-type-d/"));
            default:
                if ($theme == 'arcana_wp') {
                    die(header("Location: /signup/"));
                }

                die(header("Location: /register/"));
        }
    }

    public function pricing()
    {
        $ppg = btflag('ppg');
        $flow = btflag('flow');

        $theme = btflag('kt8typtb');
        $query_params = 'emailid=&ihc_register=create_message&utm_source=';

        if ($theme == 'basilisk') {
            if ($ppg == '101' || $ppg == '109') {
                if (btsessionIsUserLoggedIn()) {
                    die(header("Location: /subscription-plan/?$query_params"));
                } else {
                    die(header("Location: /registration-redirect"));
                }
            } else {
                if (btsessionIsUserLoggedIn()) {
                    $selected_plan = btflag('pricing');
                    if ($selected_plan) {
                        die(header("Location: /pay"));
                    }
                }
            }
        }

        if ($flow === '04') {
            die(header("Location: /plans-and-pricing-d/?$query_params"));
        } else if ($flow === '06') {
            if ($ppg == '97') {
                die(header("Location: /subscription-plan"));
            } else {
                die(header("Location: /subscription-e/?$query_params"));
            }
        } else {
            if ($theme == 'arcana_wp') {
                die(header("Location: /subscription"));
            }

            die(header("Location: /subscription-plan/?$query_params"));
        }
    }

    //public function page()
    //{
        // $uri_string = uri_string();
        // $slugs = explode('/', $uri_string);
        // $last_slug = end($slugs);
        // if ($last_slug == '') {
        //     $slug = prev($slugs);
        // } else {
        //     $slug = $last_slug;
        // }
        // $redirection_items = btdbFindBy("WPRedirectionItemsModel", ["url"], ['/' . $slug . '/']);
        // if (
        //     $redirection_items['success'] && !empty($redirection_items['res'])
        // ) {
        //     $redirectUrl = $redirection_items['res'][0]->action_data;
        //     if (strpos($redirectUrl, base_url()) === false) {
        //         $redirectUrl = str_replace('https://ai-pro.org/', base_url(), $redirectUrl);
        //     }

        //     header("Location: " . $redirectUrl);
        //     exit;
        // }

        // $paths = ['articles', 'tutorials'];
        // foreach ($paths as $path) {
        //     $pageSlug = "https://ai-pro.org/learn-ai/{$path}/{$slug}/";

        //     $posts = btdbFindBy("WPPostModel", ["post_name", "post_status"], [$slug, "publish"]);
        //     $yoastIndex = btdbFindBy("WPYoastIndexModel", ["permalink", "object_sub_type"], [$pageSlug, "post"]);

        //     if (($yoastIndex['success'] && $yoastIndex['res']) && ($posts['success'] && $posts['res'])) {
        //         header("Location: /learn-ai/{$path}/{$slug}/");
        //         exit;
        //     }
        // }


        //throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound($slug);
        //die;
    //}

}
