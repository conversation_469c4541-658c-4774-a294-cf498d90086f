<?php

// do not show pagination if there is only one page
if (isset($total_pages) && $total_pages <= 1) {
    return;
}

?>


            <nav class="ct-pagination" data-pagination="load_more">
			    <div class="ct-load-more-helper">
                    <button class="ct-button ct-load-more">Load More</button>
                    <div id="cms-loading-spinner" class="cms-loading-spinner" style="display: none;"></div>
                    <div class="ct-last-page-text">No more posts to load</div>
                </div>
		    </nav>


<?php if (isset($total_pages) && $total_pages > 1): ?>
    <?php
        $currentPage = $current_page ?? 1;
        $baseUrl = rtrim(current_url(), '/');
        $baseUrl = preg_replace('#/page/\d+$#', '', $baseUrl);
        $baseUrl = str_ireplace('index.php/', '', $baseUrl);
        $queryParams = $_GET;

        function buildPageUrl($baseUrl, $page, $queryParams) {
            $queryString = http_build_query($queryParams);
            return esc($baseUrl . '/page/' . $page . (!empty($queryString) ? '?' . $queryString : ''));
        }

        $prevUrl = $currentPage > 1 ? buildPageUrl($baseUrl, $currentPage - 1, $queryParams) : null;
        $nextUrl = $currentPage < $total_pages ? buildPageUrl($baseUrl, $currentPage + 1, $queryParams) : null;
    ?>

    <!-- SEO rel prev/next -->
    <?php if ($prevUrl): ?>
        <link rel="prev" href="<?= $prevUrl ?>" />
    <?php endif; ?>
    <?php if ($nextUrl): ?>
        <link rel="next" href="<?= $nextUrl ?>" />
    <?php endif; ?>

    <!-- Semantic pagination -->
    <nav class="seo-pagination-links" aria-label="Page navigation" style="display: none;">
        <ul>
            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <?php if ($i == $currentPage) continue; ?>
                <li>
                    <a href="<?= buildPageUrl($baseUrl, $i, $queryParams) ?>">Page <?= $i ?></a>
                </li>
            <?php endfor; ?>
        </ul>
    </nav>
<?php endif; ?>





<style nonce="<?= $nonce ?>">
.cms-loading-spinner {
    width: 30px;
    height: 30px;
    border: 4px solid #ddd;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin: 1em auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.seo-pagination-links {
    position: absolute;
    left: -9999px;
    top: -9999px;
}
</style>
<script nonce="<?= $nonce ?>">
$(document).ready(function () {
    let currentPage = <?= $current_page ?? 1 ?>;
    let maxPage = <?= $total_pages ?? 1 ?>;

    const $loadMoreBtn = $('.ct-load-more');
    const $spinner = $('#cms-loading-spinner');
    const $noMoreText = $('.ct-last-page-text');

    $spinner.hide();
    $noMoreText.hide();

    function getNextPageUrl(pageNum) {
        const location = window.location;
        let path = location.pathname.replace(/\/$/, '');

        // Replace or append /page/{number}
        if (path.match(/\/page\/\d+$/)) {
            path = path.replace(/\/page\/\d+$/, '/page/' + pageNum);
        } else {
            path += '/page/' + pageNum;
        }

        // Get current query string
        let query = location.search;

        // Convert to URLSearchParams for manipulation
        const params = new URLSearchParams(query);

        // Always add or update ajax=1
        params.set('ajax', '1');

        // Return full URL
        return path + '?' + params.toString();
    }

    $loadMoreBtn.on('click', function(e) {
        e.preventDefault();

        currentPage++;
        $loadMoreBtn.hide();
        $spinner.show();

        const nextPageUrl = getNextPageUrl(currentPage);

        $.get(nextPageUrl, function(response) {
            $('#content-collection').append(response);
            $spinner.hide();

            if (currentPage >= maxPage) {
                $loadMoreBtn.hide();
                $noMoreText.show();
            } else {
                $loadMoreBtn.show();
            }
        }).fail(function() {
            $spinner.hide();
            $loadMoreBtn.show();
            alert('Failed to load more posts.');
        });
    });
});
</script>