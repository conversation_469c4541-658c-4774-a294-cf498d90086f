import React, { useRef, useState } from "react";
import { motion } from "framer-motion";
import axios from "axios";
import PasswordToggler from "../login/passwordtoggler";
import { SetCookie, GetCookie } from "../core/utils/cookies";
import { ValidatePassword } from "../core/utils/validation";
import toastr from "toastr";
import { useTranslation } from "react-i18next";
import "./popup.css";

const RegForm = ({
  handleRedirect,
  setWillRedirect,
  emailError,
  passwordError,
  setEmailError,
  setPasswordError,
  members,
  reg_google,
  reg_apple,
  setEmail,
  setPassword,
  setEmailOptIn,
  email,
  password,
  emailOptIn,
  showPW,
  setShowPW,
  pwInputRef,
  spanErrorRefs,
  smooth_login,
  prefix,
}) => {
  const { t } = useTranslation();
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [termsError, setTermsError] = useState("");
  const kt8typtb = GetCookie("kt8typtb") || "";

  const handleChange = (event, inputType) => {
    const el = event.target;
    const value = el.value;
    const isFocus = event.type === "focus";
    const isInput = event.type === "change" || event.type === "keyup";

    if (!!value || isFocus || isInput) {
      el.classList.add("autofilled");
    } else {
      el.classList.remove("autofilled");
    }

    if (smooth_login !== "on" || (smooth_login === "on" && isInput)) {
      switch (inputType) {
        case "email":
          setEmailError("");
          setEmail(value);
          break;
        case "password":
          setPasswordError("");
          setPassword(value);
          break;
        case "emailOptIn":
          setEmailOptIn(el.checked);
          break;
        default:
          if (event.keyCode === 13) {
            registerUser();
          }
          break;
      }
    }
  };

  const honeypotRef = useRef(null);

  function registerUser() {
    if (honeypotRef.current?.value) {
      SetCookie("ishoneypot", "yes", { path: "/" });
      SetCookie("ishoneypot", "yes", { domain: ".ai-pro.org", path: "/" });
    }

    setWillRedirect(false);
    var isEmailPassed = validateEmail();
    var isPasswordPassed = validatePassword();
    var isTermsPassed = validateTerms();
    if (!isEmailPassed || !isPasswordPassed || !isTermsPassed) return;

    document.querySelector(".loader-container").classList.add("active");
    axios
      .post(
        `${process.env.REACT_APP_API_URL}/t/register`,
        {
          email,
          password,
          pass_con: password,
          emailOptIn,
        },
        { headers: { "content-type": "application/x-www-form-urlencoded" } }
      )
      .then(function (res) {
        let output = res.data;

        if (output.success) {
          try {
            if (GetCookie("emailopt") === "on") {
              // Mixpanel tracking
              window.mixpanel.track("register", {
                emailOptIn,
              });
            }
            // Quora tracking
            window.qp("track", "CompleteRegistration");
            // TikTok pixel tracking code
            window.ttq.identify({ email: `${email}` });
            window.ttq.track("Registration Page", {
              contents: [
                {
                  content_id: `${output.data.login_token}`,
                  content_name: `${email}`,
                },
              ],
              description: "Registration (Arcana)",
            });
          } catch (error) {
            console.error("An error occurred:", error.message);
          }
          toastr.success("Success");
          SetCookie("access", output.data.login_token);
          const flow = GetCookie("flow");
          const chatpdf = GetCookie("chatpdf");
          if (flow === "chatpdf") {
            if (chatpdf === "01") {
              handleRedirect("/pay");
            } else {
              handleRedirect("https://" + prefix + "chatpdf.ai-pro.org");
            }
          } else {
            if (members === "on") {
              handleRedirect("/my-account");
            } else {
              const pricing = GetCookie("pricing");
              const iSplanEnt = GetCookie("iSplanEnt");

              if (
                process.env.REACT_APP_ENTERPRISE_ID === pricing ||
                iSplanEnt === "1"
              ) {
                handleRedirect("/pay/mcWiDilmgQ");
              } else {
                handleRedirect("/pay");
              }
            }
          }

          return;
        }
        document.querySelector(".loader-container").classList.remove("active");
        if (output.data) {
          if (output.data.msg.includes("already exists")) {
            setEmailError("Email address has already been taken");
            window.location.href = "/start-splash-redirect";
          } else {
            setEmailError(output.data.msg);
            // clear email
            setEmail("");
            const emailInput = document.querySelector(".email-input");
            if (emailInput) {
              emailInput.value = "";
            }

            // terms-checkbox
            setTermsAccepted(false);
            const termsCheckbox = document.querySelector("#terms-checkbox");
            if (termsCheckbox) {
              termsCheckbox.checked = false;
            }
          }
        }
      })
      .catch(function (error) {
        if (error.response && error.response.status === 429) {
          document
            .querySelector(".loader-container")
            .classList.remove("active");
          toastr.error("Sorry, too many requests. Please try again in a bit!");
        }
      });
  }
  const validateEmail = () => {
    let isPassed = false;
    if (!email) {
      setEmailError(t("arcana.register.form.emailReqText"));
    } else if (!/^[a-zA-Z0-9_.\-@]+$/.test(email)) {
      setEmailError(t("arcana.register.form.emailAlphaNumericText"));
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError(t("arcana.register.form.emailInvalidText"));
    } else {
      setEmailError("");
      isPassed = true;
    }
    return isPassed;
  };

  const isRegistered = (email) => {
    axios
      .post(
        `${process.env.REACT_APP_API_URL}/t/is-registered`,
        {
          email,
        },
        { headers: { "content-type": "application/x-www-form-urlencoded" } }
      )
      .then(function (res) {
        let output = res.data;
        if (output.success) {
          setEmailError("Email address has already been taken");
          window.location.href = "/start-splash-redirect";
        }
      });
  };

  const validatePassword = () => {
    let msg = ValidatePassword(password);
    if (msg) {
      setPasswordError(msg);
      return false;
    }
    return true;
  };
  const validateTerms = () => {
    if (!termsAccepted) {
      setTermsError(t("arcana.register.form.termsRequiredText"));
      return false;
    }
    setTermsError("");
    return true;
  };

  const displayRequired = (text) => {
    const locales = GetCookie("locales") ?? "en";
    const value = `${text} *`;

    if (kt8typtb === "arcana_wp") {
      return value;
    }

    switch (locales) {
      case "de":
      case "es":
      case "fr":
      case "pl":
      case "pt":
      case "tr":
        return `*${text}`;
      default:
        return value;
    }
  };

  const getEmailPlaceholder = () => {
    let text = t("arcana.register.form.emailText");
    const locales = GetCookie("locales") ?? "en";

    if (kt8typtb === "arcana_wp") {
      switch (locales) {
        case "de":
          text = "E-Mail-Adresse";
          break;
        case "it":
          text = "Email";
          break;
        default:
          return displayRequired(text);
      }
    }

    return displayRequired(text);
  };

  const getPasswordPlaceholder = () => {
    const text = t("arcana.register.form.passwordText");

    return displayRequired(text);
  };

  return (
    <>
      {smooth_login === "on" ? (
        <>
          <div className="bg-white w-full relative mb-3 sm:text-sm">
            <input
              type="text"
              ref={honeypotRef}
              name="user_phone"
              className="hidden absolute left-[-9999px]"
              autocomplete="off"
            />
            <input
              className="peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]"
              placeholder={`${t("arcana.register.form.emailText")} *`}
              type="email"
              name="email"
              onBlur={(e) => handleChange(e, "email")}
              onChange={(e) => handleChange(e, "email")}
              onFocus={(e) => handleChange(e, "email")}
              onKeyUp={handleChange}
            />
            <label
              className="transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]"
              for="email"
            >
              {t("arcana.register.form.emailText")}
            </label>
          </div>
          <span
            className="block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden"
            ref={(el) => (spanErrorRefs.current[0] = el)}
          >
            {emailError}
          </span>
          <div className="bg-white w-full relative mb-3 sm:text-sm">
            <input
              className="peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 pl-6 pr-16 placeholder:text-transparent placeholder:[user-select:_none]"
              placeholder={`${t("arcana.register.form.passwordText")} *`}
              type="password"
              name="password"
              onBlur={(e) => handleChange(e, "password")}
              onChange={(e) => handleChange(e, "password")}
              onFocus={(e) => handleChange(e, "password")}
              onKeyUp={handleChange}
              ref={pwInputRef}
            />
            <label
              className="transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]"
              for="password"
            >
              {t("arcana.register.form.passwordText")}
            </label>
            <PasswordToggler
              className="right-6 top-1/2 -translate-y-1/2"
              pwInputRef={pwInputRef}
              setShowPW={setShowPW}
              showPW={showPW}
            />
          </div>
          <span
            className="block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden"
            ref={(el) => (spanErrorRefs.current[1] = el)}
          >
            {passwordError}
          </span>
        </>
      ) : (
        <>
          <label className="relative block w-full">
            <input
              type="text"
              ref={honeypotRef}
              name="user_phone"
              className="hidden absolute left-[-9999px]"
              autocomplete="off"
            />
            <input
              className={`email-input placeholder:italic placeholder:text-[#597291] block bg-white w-full border ${
                emailError ? "border-red-500" : "border-slate-300"
              } rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none hover:border-[#31aae1] focus:border-[#31aae1] hover:shadow-md focus:shadow-md sm:text-sm`}
              placeholder={getEmailPlaceholder()}
              aria-label="Email Address"
              type="email"
              name="email"
              onBlur={(event) => {
                validateEmail(event.target.value);
                isRegistered(event.target.value);
              }}
              onChange={(e) => handleChange(e, "email")}
              onKeyUp={(event) => {
                setEmailError("");
                setEmail(event.target.value);
                if (event.keyCode === 13) registerUser();
              }}
            />
            {emailError && <div class="input-error-message">{emailError}</div>}
          </label>
          <label className="block w-full">
            <div className="relative block w-full">
              <input
                className={`placeholder:italic placeholder:text-[#597291] block bg-white w-full border ${
                  passwordError ? "border-red-500" : "border-slate-300"
                } rounded-md mb-3 py-2 pl-3 shadow-sm focus:outline-none hover:border-[#31aae1] focus:border-[#31aae1] hover:shadow-md focus:shadow-md sm:text-sm pr-10`}
                placeholder={getPasswordPlaceholder()}
                aria-label="Password"
                type="password"
                name="password"
                onBlur={(event) => {
                  validatePassword();
                  setPassword(event.target.value);
                }}
                onChange={(e) => handleChange(e, "password")}
                onKeyUp={(event) => {
                  setPasswordError("");
                  setPassword(event.target.value);
                  if (event.keyCode === 13) registerUser();
                }}
                ref={pwInputRef}
              />
              {passwordError && (
                <div class="input-error-message">{passwordError}</div>
              )}
              <PasswordToggler
                className="right-3 top-3"
                pwInputRef={pwInputRef}
                setShowPW={setShowPW}
                showPW={showPW}
              />
            </div>
          </label>
        </>
      )}
      {GetCookie("emailopt") === "on" ? (
        <div class="flex gap-2 items-center">
          <input
            id="checked-checkbox"
            type="checkbox"
            name="emailOptIn"
            checked={emailOptIn}
            onChange={(e) => handleChange(e, "emailOptIn")}
            className="w-4 h-4 text-gray-600 bg-gray-100 border-gray-300 rounded dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label
            for="checked-checkbox"
            class="text-xs text-[#2872FA] text-left"
          >
            {t("arcana.register.form.emailOptText")}
          </label>
        </div>
      ) : (
        ""
      )}

      <div className="flex flex-col gap-2 mb-2 pl-2 relative">
        <div className="flex gap-2 items-center">
          <input
            id="terms-checkbox"
            type="checkbox"
            checked={termsAccepted}
            onChange={(e) => {
              setTermsAccepted(e.target.checked);
              if (e.target.checked) setTermsError("");
            }}
            className="w-4 h-4 text-gray-600 bg-gray-100 border-gray-300 rounded dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600 !accent-[#2872FA]"
          />
          {termsError && <div class="input-error-message">{termsError}</div>}
          <label
            htmlFor="terms-checkbox"
            className={`text-xs text-gray-600 text-left ${
              kt8typtb === "arcana_wp" ? "m-1" : ""
            }`}
          >
            {t("arcana.register.form.termsText")}{" "}
            <a
              href="/member-tos-page"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#2872FA] hover:text-blue-800"
            >
              {t("arcana.register.form.termsText2")}
            </a>{" "}
            {t("arcana.register.form.termsText3")}{" "}
            <a
              href="/privacy-policy"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#2872FA] hover:text-blue-800"
            >
              {t("arcana.register.form.termsText4")}
            </a>
          </label>
        </div>
      </div>

      <motion.button
        onClick={registerUser}
        className={`mb-1 text-white text-[16px] font-normal py-3 px-6 my-3 rounded-lg w-full hover:bg-[#3D80FA] register-btn ${
          reg_google === "02" || reg_apple === "02"
            ? "bg-black mb-[25px]"
            : reg_google === "on" || reg_apple === "on"
            ? "bg-[#2872FA] mb-[5px]"
            : "bg-[#2872FA]"
        }`}
        aria-label="register"
      >
        {t("arcana.register.form.continueText")}
      </motion.button>

      {reg_google === "02" || reg_apple === "02" ? (
        <span class="flex items-center mb-[15px]">
          <div class="bg-gray-300 h-px flex-1 mr-3"></div>
          <span class="font-bold text-gray-600/75">
            {t("arcana.register.form.orText")}
          </span>
          <div class="bg-gray-300 h-px flex-1 ml-3"></div>
        </span>
      ) : (
        <p className="text-center text-sm text-[#3A4F66] pt-4">
          {t("arcana.register.form.haveAccountText")}{" "}
          <a
            href={`https://${prefix}start.ai-pro.org/login`}
            className="text-[#2872FA] hover:text-blue-800 font-medium"
          >
            {t("arcana.register.form.loginLinkText")}
          </a>
        </p>
      )}
    </>
  );
};

export default RegForm;
