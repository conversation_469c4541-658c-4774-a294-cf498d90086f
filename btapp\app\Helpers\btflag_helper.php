<?php

/**
 * BTFlag
 *
 * @todo
 * - should have --> $option = []
 */

//------------------------------------------------------------------------------------------------

//------------------------------------------------------------------------------------------------

function btflag($flag, $default = null)
{
    $flag_is = btflag_protected($flag, $default);
    if ($flag_is) {
        btflag_set($flag, $flag_is);
        return $flag_is;
    }
    $flag_is = $default;

    if (isset($_GET[$flag])) {
        $flag_is = !empty($_GET[$flag]) ? $_GET[$flag] : $default;
        btflag_set($flag, $flag_is);
        return $flag_is;
    }

    return isset($_COOKIE[$flag]) ? $_COOKIE[$flag] : $flag_is;
}
function btflag_xlate($flag, $default = null)
{
    $flag_is = btFlag($flag, $default);

    $protected_flag = btflag_protected_flags();

    return $protected_flag[$flag]['xlate'][$flag_is];
}
function btflag_protected_flags()
{
    return[]; //TODO: not properly working for translate function (btflag_xlate)
    return [
        'mode' => [
            'default' => 'live',
            'xlate' => ['' => 'live', 'live' => 'live', 'test' => 'test'],
            'path' => '/',
            'domain' => '',
        ],
    ];
}
function btflag_protected($flag, $default = null)
{
    $protected_flag = btflag_protected_flags();

    if (!array_key_exists($flag, $protected_flag)) {
        return false;
    }

    if ($default == null) {
        $default = $protected_flag[$flag]['default'];
    }

    $flag_is = $default;
    $flag_hashed = btflag_hash($flag);

    if (isset($_GET[$flag])) {
        $flag_value = $_GET[$flag];

        if ($flag_value == $default) {
            btflag_remove($flag_hashed, 1);
            return $default;
        }

        $flag_is = getenv($flag_value);
        if ($flag_is == "") {
            btflag_remove($flag_hashed, 1);
            return $default;
        }

        btflag_set($flag_hashed, 1);
        return $flag_is;
    }

    if (!isset($_COOKIE[$flag_hashed])) {
        btflag_remove($flag, 1);
        btflag_remove($flag_hashed, 1);
        return '';
    }

    return !empty($_COOKIE[$flag]) ? $_COOKIE[$flag] : $default;
}
/**
 *  Limited for COOKIE only
 */
function btflag_set($flag, $value, $option = [])
{
    $expiryInDays = 30;
    $path = '/';
    $secure = false;
    $httponly = false;
    $domain = null;  // default: don't set domain
    $samesite = 'Lax';

    $current_url = current_url();
    $mainDomain = 'ai-pro.org';

    if(isset($option['domain']) && $option['domain']) {
        $domain = $option['domain'];
    } else if (strpos($current_url, $mainDomain) !== false) {
        $parsed_url = parse_url($current_url);
        $host = $parsed_url['host'] ?? '';

        if ($host === $mainDomain) {
            $domain = '.' . $mainDomain;
        } else {
            $domain = null;
        }

        $secure = true;
        $httponly = true;
    }

    if (!empty($option)) {
        $expiryInDays = $option['expires'] ?? $expiryInDays;
        $path = $option['path'] ?? $path;
        if (array_key_exists('domain', $option)) {
            $domain = $option['domain'];
        }
    }

    $expires = time() + (86400 * floatval($expiryInDays));

    $cookieOptions = [
        'expires' => $expires,
        'path' => $path,
        'secure' => $secure,
        'httponly' => $httponly,
        'samesite' => $samesite
    ];

    if (!is_null($domain)) {
        $cookieOptions['domain'] = $domain;
    }

    setcookie($flag, $value, $cookieOptions);
}

function btflag_remove($flag, $value = null, $option = [])
{
    $path = isset($option['path']) ? $option['path'] : '/';
    $domain = isset($option['domain']) ? $option['domain'] : '';

    setcookie($flag, $value, time() - 3600, '', $domain);
    return setcookie($flag, $value, time() - 3600, $path, $domain);
}
function btflag_hash($flag)
{
    $hashing = '5_UGy_';
    $prefix = '__';

    return $prefix . hash('md5', $hashing . $flag, false);
}
function btflag_remove_protected_flags()
{
    $protected_flag = btflag_protected_flags();

    foreach ($protected_flag as $name => $value) {
        btflag_remove($name, '');
        btflag_remove($name, '', $value['path'], $value['domain']);
    }

    $flag_hashed = btflag_hash('mode');
    $other_flag = [
        $flag_hashed,
    ];

    foreach ($other_flag as $key => $name) {
        btflag_remove($name, '');
    }
}
function btflag_appender($_escape = true)
{
    $request_data = $_GET;

    if ($_escape) {
        $and = "&amp;";
    } else {
        $and = "&";
    }

    // $ci = &get_instance();
    // $flags = $ci->input->get();
    $link = "";

    foreach ($request_data as $key => $value) {
        if ($key == 'clixid') {
            continue;
        }

        $link .= $and . $key . "=" . $value;
    }

    return $link;
}
//TODO: janis - newly added
if (!function_exists('btflag_get')) {
    function btflag_get($flag, $value = null)
    {
        return (isset($_GET[$flag])) ? $_GET[$flag] : $value;
    }
}
//TODO: janis - newly added
if (!function_exists('btflag_cookie')) {
    function btflag_cookie($flag, $value = null)
    {
        return (isset($_COOKIE[$flag])) ? $_COOKIE[$flag] : $value;
    }
}
if (!function_exists('btflag_init')) {
    function btflag_init()
    {
        $protected_flag = btflag_protected_flags();

        foreach ($protected_flag as $key => $value) {
            $flag_value = (isset($_COOKIE[$key])) ? $_COOKIE[$key] : $value['default'];
            $flag_value = (isset($_GET[$key])) ? $_GET[$key] : $flag_value;
            btflag_set($key, $flag_value);
        }
    }
}
if (!function_exists('btflag_default')) {
    function btflag_default($flag)
    {
        $protected_flag = btflag_protected_flags();
        $value = isset($protected_flag[$flag]) && isset($protected_flag[$flag]['default']) ? $protected_flag[$flag]['default'] : '';
        return $value;
    }
}
