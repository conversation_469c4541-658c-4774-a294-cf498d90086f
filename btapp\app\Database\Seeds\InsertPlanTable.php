<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class InsertPlanTable extends PlanSeeder
{
    // to run seed -> php spark db:seed InsertPlanTable
    protected $table = "plan";

    public function run()
    {
        $team_max_pricing_description = $this->getMaxPricingDescription("60");
        $office_max_pricing_description = $this->getMaxPricingDescription("100");
        $enterprise_max_pricing_description = $this->getMaxPricingDescription("120");

        $trial_pricing_description = "<div>{trialDays}-Day Trial, {trialPricePer}</div>";

        $date_now = date('Y-m-d H:i:s', time());
        $data = [
            [
                'plan_id'           => '1',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '01',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '2',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539982',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '7-Day Trial',
                'ppg'               => '02',
                'fs_plan_id'        => 'trial-195-7',
                'recurly_plan_id'   => 'trial-195-7',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52622","live":"833106"}',
                'trial_days'        => 7,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => '1.95',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$1.95 for 7 days, then $10/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $10 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '3',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539983',
                'plan_name'         => 'ANNUAL',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'ANNUAL',
                'ppg'               => '02',
                'fs_plan_id'        => 'annual-47',
                'recurly_plan_id'   => 'annual-47',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52614","live":"833097"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '47',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$47/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '4',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539984',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '03',
                'fs_plan_id'        => 'trial-285-14',
                'recurly_plan_id'   => 'trial-285-14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52620","live":"833103"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$2.85 for 14 days, then $23.85/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "de" => [
                        "label" => "14-tägige Testversion",
                        "price_text" => "$2.85",
                        "description" => 'Zugang zu fortgeschrittenen Modellen:<div class="indent">GPT-3.5 TURBO</div><div class="indent">GPT-4o mini</div><div class="indent">GPT-4o</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br><div class="hover-target">Voller Kontextspeicher<span class="description-tooltip">Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.</span></div><div class="hover-target">Vollständiges Dialoglimit<span class="description-tooltip">Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 500.000 Tokens.</span></div><div>Unbegrenzte Chatverlauf-Speicherung</div><div>Vorlagen für Befehle</div><div>Bild-Datei-Upload</div>Erweiterte Werkzeuge:<div class="hover-target">Vollzugriff<span class="description-tooltip">Zugriff auf über ein Dutzend spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KI-unterstützte Suchmaschine usw.</span></div><div>14 Tage Testversion, danach nur 23,85 $ pro Monat.</div>',
                        "button_label" => "Fortfahren"
                    ],
                    "es" => [
                        "label" => "14 Días de Prueba",
                        "price_text" => "$2.85",
                        'description' => 'Acceso a modelos avanzados: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: 50k tokens/mes <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span></div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NUEVO</span></div> <div class="hover-target indent">Claude 3.7: 50K tokens/mes <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span></div> <div class="indent">DALL-E 3: 50 imágenes/mes</div> <div class="indent">Flux: 30 imágenes/mes</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Memoria de contexto completo <span class="description-tooltip">Esto es lo que el modelo de IA recordará dentro de una conversación.</span></div> <div class="hover-target">Límite completo de diálogo <span class="description-tooltip">Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1,000,000 tokens.</span></div> <div class="hover-target">Acceso completo a herramientas avanzadas. <span class="description-tooltip">Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.</span></div> <div>Prueba de 14 días, luego solo $23.85 por mes.</div>',
                        "button_label" => "Continuar"
                    ],
                    "fr" => [
                        "label" => "Essai de 14 jours",
                        "price_text" => "$2.85",
                        "description" => 'Accès aux Modèles Avancés:<div class="indent">GPT-3.5 TURBO</div><div class="indent">GPT-4o mini</div><div class="indent">GPT-4o</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br><div class="hover-target">Mémoire de Contexte Complète<span class="description-tooltip">Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.</span></div><div class="hover-target">Limite Dialogue Complète<span class="description-tooltip">Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 500 000 jetons.</span></div><div>Sauvegardes illimitées de l\'historique de chat</div><div>Modèles de Commandes</div><div>Téléchargement de fichiers image</div>Outils Avancés:<div class="hover-target">Accès Complet<span class="description-tooltip">Accès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc.</span></div><div>Essai de 14 jours, puis seulement 23,85 $ par mois.</div>',
                        "button_label" => "Continuer"
                    ],
                    "it" => [
                        "label" => "Prova di 14 giorni",
                        "price_text" => "€2.85",
                        "description" => '        \r\nAccesso ai modelli avanzati:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div> \r\n<br>\r\n\r\n<div class="hover-target">\r\n    Memorizzazione del contesto completa\r\n    <span class="description-tooltip">\r\n        Indica quanto il modello di intelligenza artificiale sarà in grado di ricordare all\'interno di una singola conversazione.\r\n    </span>\r\n</div>\r\n\r\n<div class="hover-target">\r\n    Limite totale di dialogo\r\n    <span class="description-tooltip">\r\n        Questa è la lunghezza massima totale che il modello di intelligenza artificiale può produrre al mese, ossia circa 500.000 tokens.\r\n    </span>\r\n</div>\r\n\r\n<div>Salvataggi illimitati della cronologia della chat</div>\r\n<div>Modelli di prompt</div>\r\n<div>Caricamento di file immagine</div>\r\nStrumenti avanzati: \r\n<div class="hover-target">\r\n    Accesso completo\r\n    <span class="description-tooltip">\r\n        Accesso a oltre una dozzina di strumenti di AI specializzati, tra cui un generatore di testo in immagini, un analizzatore di documenti, un motore di ricerca potenziato dall\'intelligenza artificiale, ecc.\r\n    </span>\r\n</div>\r\n<div>Prova di 14 giorni, poi solo €23.85 al mese.</div>\r\n<br>',
                        "button_label" => "Continua"
                    ],
                    'tr' => [
                        'label' => '14 Günlük Deneme',
                        'price_text' => '$2.85',
                        'description' => 'Gelişmiş Modeller\'e Erişim: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: Aylık 50K Token <span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div> <div class="indent newBadge">Grok AI✨ <span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div> <div class="hover-target indent">Claude 3.7: Aylık 50K Token <span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div> <div class="indent">DALL-E 3: Aylık 50 Görsel</div> <div class="indent">Flux: Aylık 30 Görsel</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Tam Bağlam Hafızası <span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div> <div class="hover-target">Tam Diyalog Sınırı <span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div> <div class="hover-target">Gelişmiş Araçlara Tam Erişim <span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div> <div>14 Gün Deneme, ardından sadece ayda 23,85&nbsp;$</div>',
                        'button_label' => 'Devam ediniz'
                    ],
                    'pt' => [
                        'label' => 'Teste de 14 Dias',
                        'price_text' => '$2.85',
                        'description' => 'Acesso aos Modelos Avançados:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMemória de Contexto Completa\r\n<span class="description-tooltip">\r\nEsta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite Total de Diálogo\r\n<span class="description-tooltip">\r\nEste é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.\r\n</span>\r\n</div>\r\n<div>Salvamento Ilimitado do Histórico de Conversas</div>\r\n<div>Modelos de Comando</div>\r\n<div>Upload de Arquivo de Imagem</div>\r\nFerramentas Avançadas:\r\n<div class="hover-target">\r\nAcesso Completo\r\n<span class="description-tooltip">\r\nAcesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.\r\n</span>\r\n</div>\r\n<div>Teste de 14 Dias, depois apenas $23,85 por mês.</div>',
                        'button_label' => 'Continuar'
                    ],
                    'pl' => $this->getPolishLocale('14-dniowy okres próbny', 'Pro', '$2.85', [
                        'trial_days' => 14,
                        'price_text' => '$23,85',
                    ], 4),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '5',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539985',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'Basic',
                'ppg'               => '04',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833098"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '6',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539986',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '05',
                'fs_plan_id'        => 'pro-eur-2385-6',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53677","live":"837580"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€2.85 for 14 days, then only €23.85/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged €23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "de" => [
                        "label" => "14-tägige Testversion",
                        "price_text" => "€2.85",
                        "description" => 'Zugang zu fortgeschrittenen Modellen: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: 50k Token/Monat <span class="description-tooltip">Ungefähr 2.000 Sätze.</span></div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NEU</span></div> <div class="hover-target indent">Claude 3.7: 50K Token/Monat <span class="description-tooltip">Ungefähr 2.000 Sätze.</span></div> <div class="indent">DALL-E 3: 50 Bilder/Monat</div> <div class="indent">Flux: 30 Bilder/Monat</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Voller Kontextspeicher <span class="description-tooltip">Das ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.</span></div> <div class="hover-target">Vollständiges Dialoglimit <span class="description-tooltip">Dies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 1.000.000 Tokens.</span></div> <div class="hover-target">Vollständiger Zugang zu fortgeschrittenen Tools <span class="description-tooltip">Zugriff auf über ein Dutzend spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KI-unterstützte Suchmaschine usw.</span></div> <div>14 Tage Testversion, danach nur 23,85 € pro Monat.</div>',
                        "button_label" => "Fortfahren"
                    ],
                    "es" => [
                        "label" => "14 Días de Prueba",
                        "price_text" => "€2.85",
                        "description" => 'Acceso a modelos avanzados: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: 50k tokens/mes <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span></div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NUEVO</span></div> <div class="hover-target indent">Claude 3.7: 50K tokens/mes <span class="description-tooltip">Aproximadamente 2,000 oraciones.</span></div> <div class="indent">DALL-E 3: 50 imágenes/mes</div> <div class="indent">Flux: 30 imágenes/mes</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Memoria de contexto completo <span class="description-tooltip">Esto es lo que el modelo de IA recordará dentro de una conversación.</span></div> <div class="hover-target">Límite completo de diálogo <span class="description-tooltip">Esta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 1.000.000 tokens.</span></div> <div class="hover-target">Acceso completo a herramientas avanzadas <span class="description-tooltip">Acceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.</span></div> <div>Prueba de 14 días, luego solo €23.85 por mes.</div>',
                        "button_label" => "Continuar"
                    ],
                    "fr" => [
                        "label" => "Essai de 14 jours",
                        "price_text" => "€2.85",
                        "description" => 'Accès aux Modèles Avancés: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: 50k jetons par mois <span class="description-tooltip">Environ 2 000 phrases.</span></div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">NOUVEAU</span></div> <div class="hover-target indent">Claude 3.7 : 50k jetons par mois <span class="description-tooltip">Environ 2 000 phrases.</span></div> <div class="indent">DALL-E 3 : 50 images par mois</div> <div class="indent">Flux : 30 images par mois</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Mémoire de Contexte Complète <span class="description-tooltip">Voici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.</span></div> <div class="hover-target">Limite Dialogue Complète <span class="description-tooltip">Ceci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 1 000 000 jetons.</span></div> <div class="hover-target">Accès complet aux outils avancés <span class="description-tooltip">Accès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc.</span></div> <div>Essai de 14 jours, puis seulement 23,85&nbsp;€ par mois.</div>',
                        "button_label" => "Continuer"
                    ],
                    "it" => [
                        "label" => "Prova di 14 giorni",
                        "price_text" => "€2.85",
                        "description" => 'Accesso ai modelli avanzati: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="hover-target indent">DeepSeek R1: 50k Token al mese <span class="description-tooltip">Circa 2.000 frasi.</span></div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NUOVO</span></div> <div class="hover-target indent">Claude 3.7: 50K Token al mese <span class="description-tooltip">Circa 2.000 frasi.</span></div> <div class="indent">DALL-E 3: 50 immagini al mese</div> <div class="indent">Flux: 30 immagini al mese</div> <div class="indent">LLaMA3-70B</div> <div class="indent">Gemini Pro</div> <br> <div class="hover-target">Memorizzazione del contesto completa <span class="description-tooltip">Indica quanto il modello di intelligenza artificiale sarà in grado di ricordare all\'interno di una singola conversazione.</span></div> <div class="hover-target">Limite totale di dialogo <span class="description-tooltip">Questa è la lunghezza massima totale che il modello di intelligenza artificiale può produrre al mese, ossia circa 1.000.000 token.</span></div> <div class="hover-target">Accesso completo agli strumenti avanzati <span class="description-tooltip">Accesso a oltre una dozzina di strumenti di AI specializzati, tra cui un generatore di testo in immagini, un analizzatore di documenti, un motore di ricerca potenziato dall\'intelligenza artificiale, ecc.</span></div> <div>Prova di 14 giorni, poi solo € 23,85 al mese.</div> <br>',
                        "button_label" => "Continua"
                    ],
                    'pt' => [
                        'label' => 'Teste de 14 Dias',
                        'price_text' => '€2.85',
                        'description' => 'Acesso aos Modelos Avançados:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMemória de Contexto Completa\r\n<span class="description-tooltip">\r\nEsta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite Total de Diálogo\r\n<span class="description-tooltip">\r\nEste é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.\r\n</span>\r\n</div>\r\n<div>Salvamento Ilimitado do Histórico de Conversas</div>\r\n<div>Modelos de Comando</div>\r\n<div>Upload de Arquivo de Imagem</div>\r\nFerramentas Avançadas:\r\n<div class="hover-target">\r\nAcesso Completo\r\n<span class="description-tooltip">\r\nAcesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.\r\n</span>\r\n</div>\r\n<div>Teste de 14 Dias, depois apenas €23,85 por mês.</div>',
                        'button_label' => 'Continuar'
                    ],
                    'tr' => [
                        'label' => '14 Günlük Deneme',
                        'price_text' => '€2.85',
                        'description' => 'Gelişmiş Modeller\'e Erişim:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nTam Bağlam Hafızası\r\n<span class="description-tooltip">\r\nBu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nTam Diyalog Sınırı\r\n<span class="description-tooltip">\r\nBu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500,000 token.\r\n</span>\r\n</div>\r\n<div>Sınırsız Sohbet Geçmişi Kaydetme</div>\r\n<div>Komut Şablonları</div>\r\n<div>Görüntü Dosyası Yükleme</div>\r\nGelişmiş Araçlar:\r\n<div class="hover-target">\r\nTam Erişim\r\n<span class="description-tooltip">\r\nMetinden görüntü oluşturma, belge analisti, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.\r\n</span>\r\n</div>\r\n<div>14 Gün Ücretsiz Deneme, ardından sadece ayda 23,85 &#8364;</div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '7',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539987',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '06',
                'fs_plan_id'        => 'pro-gbp-2385-7',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53678","live":"837581"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£2.85 for 14days, then £23.85/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged £23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '8',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539988',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '07',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => '10',
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '10ر.س for 14 days, then 90ر.س/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 90ر.س after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '9',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539989',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '3-Day Trial',
                'ppg'               => '08',
                'fs_plan_id'        => 'pro-gbp-2385-9',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53679","live":"837582"}',
                'trial_days'        => 3,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£2.85 for 3 days, then £23.85/Month',
                'display_txt2'      =>  $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged £23.85 after 3 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '10',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539990',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '09',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20-ppg09',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '11',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539991',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '10',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10-ppg10',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833098"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '12',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539992',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '10',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20-ppg10',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '13',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539993',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '7-Day Trial',
                'ppg'               => '11',
                'fs_plan_id'        => 'basic-brl-49-13',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53680","live":"837583"}',
                'trial_days'        => 7,
                'payment_interval'  => 'Monthly',
                'price'             => '49',
                'trial_price'       => '5.77',
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$5.77 for 7 days, then R$49/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged R$49.00 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "pt" => [
                        "label" => "7 dias de Teste",
                        "price_text" => "R$5.77",
                        "description" => 'Acesso aos Modelos Básicos: <div class="indent">GPT-4o</div> <div class="indent">DeepSeek V3</div> <div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff;padding: 2px 10px;border-radius: 10px;color: #2872fa !important;font-weight: bold;font-size: 12px;">NOVO</span></div> <div class="hover-target indent">Claude 3.5: 25K Tokens/Mês <span class="description-tooltip">Aproximadamente 1.000 frases.</span></div> <div class="indent">DALL-E 3: 20 Imagens/Mês</div> <div class="indent">Flux: 15 Imagens/Mês</div> <div class="indent">LLaMA3-8B</div> <div class="indent">Gemma-7B</div> <br> <div class="hover-target">Memória de Contexto Padrão <span class="description-tooltip">Esta é a quantidade de informação de que o modelo de IA se recordará numa única conversa.</span></div> <div class="hover-target">Limite Padrão de Diálogo <span class="description-tooltip">Este é o total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.</span></div> <br> <div>Teste Grátis de 7 Dias, depois apenas R$49 por mês.</div> <br><br>',
                        "button_label" => "Continuar"
                    ]
                    ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '14',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539994',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '12',
                'fs_plan_id'        => 'trial-285-14-10',
                'recurly_plan_id'   => 'trial-285-14-10',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52621","live":"833104"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$2.85 for 14 days, then $10/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $10.00 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '15',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '14',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10-ppg14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833101"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "es" => [
                        "label" => "BASIC",
                        "price_text" => "$10/Month",
                        "description" => 'Acceso a modelos básicos:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nMemoria de Contexto Estándar\r\n<span class="description-tooltip">\r\nEsto es lo que el modelo de IA recordará dentro de una conversación.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLímite estándar de diálogo\r\n<span class="description-tooltip">\r\nEsta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 250,000 tokens.\r\n</span>\r\n</div>\r\n<div>Guardado ilimitado del historial de chat</div>\r\n<div>Subida de archivos de imagen</div>\r\nHerramientas avanzadas:\r\n<div>Solo generador de texto a imagen</div>',
                        "button_label" => "Continuar"
                    ],
                    'tr' => [
                        'label' => 'BASIC',
                        'price_text' => '$10/Month',
                        'description' => 'Temel Modellere Erişim:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nStandart Bağlam Hafızası\r\n<span class="description-tooltip">\r\nAI modelinin tek bir sohbet süresince ne kadar bilgiyi hatırlayacağını ifade eder. \r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nStandart Diyalog Sınırı\r\n<span class="description-tooltip">\r\nYapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 250.000 simge kullanır. \r\n</span>\r\n</div>\r\n<div>Sınırsız Sohbet Geçmişi Kaydetme</div>\r\n<div>Görüntü Dosyası Yükleme</div>\r\nGelişmiş Araçlar:\r\n<div>Sadece Metinden Görüntü Oluşturma</div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '16',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539996',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '14',
                'fs_plan_id'        => 'pro-23851',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "es" => [
                        "label" => "PRO",
                        "price_text" => "$23.85/Month",
                        "description" => 'Acceso a modelos avanzados:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMemoria de contexto completo\r\n<span class="description-tooltip">\r\nEsto es lo que el modelo de IA recordará dentro de una conversación.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLímite completo de diálogo\r\n<span class="description-tooltip">\r\nEsta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 500,000 tokens.\r\n</span>\r\n</div>\r\n<div>Guardado ilimitado del historial de chat</div>\r\n<div>Plantillas de prompts</div>\r\n<div>Subida de archivos de imagen</div>\r\nHerramientas avanzadas:\r\n<div class="hover-target">\r\nAcceso completo\r\n<span class="description-tooltip">\r\nAcceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.\r\n</span>\r\n</div>',
                        "button_label" => "Continuar"
                    ],
                    'tr' => [
                        'label' => 'PRO',
                        'price_text' => '$23.85/Month',
                        'description' => 'Gelişmiş Modellere Erişim:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nTam Bağlam Hafızası\r\n<span class="description-tooltip">\r\n AI modelinin tek bir sohbet süresince ne kadar bilgiyi hatırlayacağını ifade eder.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nTam Diyalog Sınırı\r\n<span class="description-tooltip">\r\nYapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500.000 token kullanır.\r\n</span>\r\n</div>\r\n<div>Sınırsız Sohbet Geçmişi Kaydetme</div>\r\n<div>Komut Şablonları</div>\r\n<div>Görüntü Dosyası Yükleme</div>\r\nGelişmiş Araçlar:\r\n<div class="hover-target">\r\nTam Erişim\r\n<span class="description-tooltip">\r\nMetinden görüntü oluşturma, belge analisti, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.\r\n</span>\r\n</div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '17',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539997',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '15',
                'fs_plan_id'        => 'pro-brl-110-17',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53687","live":"837603"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '110',
                'trial_price'       => '13.49',
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$13.49 for 14 days, then R$110/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged R$110.00 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '18',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539998',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '16',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => '10',
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '10AED for 14 days, then 90AED/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 90AED after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '19',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '17',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => 'basic-annual-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54608","live":"839874"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '20',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540000',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '17',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '23',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a140000',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '18',
                'fs_plan_id'        => 'basic-5',
                'recurly_plan_id'   => 'basic-5',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"61168","live":"843921"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$5/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '24',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-01822a540001',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '19',
                'fs_plan_id'        => 'basic-eur-5-24',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"61169","live":"843922"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€5/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '25',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-01822a540002',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '20',
                'fs_plan_id'        => 'basic-gbp-5-25',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"61171","live":"843924"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£5/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '26',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-01822a540003',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '21',
                'fs_plan_id'        => 'basic-brl-25-26',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"61170","live":"843923"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '25',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$25/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '27',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-01822a540004',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '22',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '5ر.س./Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '28',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-01822a540005',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '23',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '5AED/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '29',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540001',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '24',
                'fs_plan_id'        => 'basic-gbp-10-29',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60720","live":"842398"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '30',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540002',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '24',
                'fs_plan_id'        => 'pro-gbp-2385-30',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60723","live":"842400"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '31',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540003',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '25',
                'fs_plan_id'        => 'basic-gbp-97-31',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60722","live":"842399"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '32',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540004',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '25',
                'fs_plan_id'        => 'pro-gbp-197-32',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60724","live":"842401"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '33',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540005',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '26',
                'fs_plan_id'        => 'basic-eur-10-33',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60725","live":"842402"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "de" => [
                        "label" => "BASIC",
                        "price_text" => "€10/Monat",
                        "description" => 'Zugang zu Basis-Modellen:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nStandardkontextgedächtnis\r\n<span class="description-tooltip">\r\nDas ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nStandard-Dialoglimit\r\n<span class="description-tooltip">\r\nDies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 250.000 Tokens.\r\n</span>\r\n</div>\r\n<div>Unbegrenzte Chatverlauf-Speicherung</div>\r\n<div>Bild-Datei-Upload</div>\r\nErweiterte Werkzeuge:\r\n<div>Nur Text-zu-Bild-Generator</div>\r\n',
                        "button_label" => "Fortfahren"
                    ],
                    "es" => [
                        "label" => "BASIC",
                        "price_text" => "€10/Mes",
                        "description" => 'Acceso a modelos básicos:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nMemoria de Contexto Estándar\r\n<span class="description-tooltip">\r\nEsto es lo que el modelo de IA recordará dentro de una conversación.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLímite estándar de diálogo\r\n<span class="description-tooltip">\r\nEsta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 250,000 tokens.\r\n</span>\r\n</div>\r\n<div>Guardado ilimitado del historial de chat</div>\r\n<div>Subida de archivos de imagen</div>\r\nHerramientas avanzadas:\r\n<div>Solo generador de texto a imagen</div>\r\n',
                        "button_label" => "Continuar"
                    ],
                    "fr" => [
                        "label" => "BASIC",
                        "price_text" => "€10/Mois",
                        "description" => 'Accès aux Modèles de Base:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nMémoire de Contexte Standard\r\n<span class="description-tooltip">\r\nVoici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite de Dialogue Standard\r\n<span class="description-tooltip">\r\nCeci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 250 000 jetons.\r\n</span>\r\n</div>\r\n<div>Sauvegardes illimitées de l\'historique de chat</div>\r\n<div>Téléchargement de fichier image</div>\r\nOutils Avancés:\r\n<div>Générateur de texte en image uniquement</div>\r\n',
                        "button_label" => "Continuer"
                    ],
                    'pt' => [
                        'label' => 'BASIC',
                        'price_text' => '€10/Mês',
                        'description' => 'Acesso aos Modelos Básicos:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-8B</div>\r\n<div class="indent">Gemma-7B</div>\r\n<br>\r\n<div class="hover-target">\r\nMemória de Contexto Padrão\r\n<span class="description-tooltip">\r\nEsta é a quantidade de informação de que o modelo de IA se recordará numa única conversa.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite Padrão de Diálogo\r\n<span class="description-tooltip">\r\nEste é o total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 250.000 tokens.\r\n</span>\r\n</div>\r\n<div>Arquivo Ilimitado do Histórico de Conversas</div>\r\n<div>Upload de Arquivo de Imagem</div>\r\nFerramentas Avançadas:\r\n<div>Gerador de Texto para Imagem</div>',
                        'button_label' => 'Continuar'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '34',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540006',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '26',
                'fs_plan_id'        => 'pro-eur-2385-34',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60727","live":"842404"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "de" => [
                        "label" => "PRO",
                        "price_text" => "€23.85/Monat",
                        "description" => 'Zugang zu fortgeschrittenen Modellen:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nVoller Kontextspeicher\r\n<span class="description-tooltip">\r\nDas ist, wie viel das KI-Modell innerhalb eines Gesprächs erinnern kann.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nVollständiges Dialoglimit\r\n<span class="description-tooltip">\r\nDies ist die maximale Gesamtlänge, die das KI-Modell für einen monatlichen Zeitraum ausgeben kann. Ungefähr 500.000 Tokens.\r\n</span>\r\n</div>\r\n<div>Unbegrenzte Chatverlauf-Speicherung</div>\r\n<div>Vorlagen für Befehle</div>\r\n<div>Bild-Datei-Upload</div>\r\nErweiterte Werkzeuge:\r\n<div class="hover-target">\r\nVollzugriff\r\n<span class="description-tooltip">\r\nZugriff auf eine Vielzahl spezialisierte KI-Tools, darunter einen Text-zu-Bild-Generator, einen Dokumentenanalysator, eine KI-unterstützte Suchmaschine usw.\r\n</span>\r\n</div>',
                        "button_label" => "Fortfahren"
                    ],
                    "es" => [
                        "label" => "PRO",
                        "price_text" => "€23.85/Mes",
                        "description" => 'Acceso a modelos avanzados:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMemoria de contexto completo\r\n<span class="description-tooltip">\r\nEsto es lo que el modelo de IA recordará dentro de una conversación.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLímite completo de diálogo\r\n<span class="description-tooltip">\r\nEsta es la longitud máxima total que el modelo de IA puede generar en un periodo mensual. Aproximadamente 500,000 tokens.\r\n</span>\r\n</div>\r\n<div>Guardado ilimitado del historial de chat</div>\r\n<div>Plantillas de prompts</div>\r\n<div>Subida de archivos de imagen</div>\r\nHerramientas avanzadas:\r\n<div class="hover-target">\r\nAcceso completo\r\n<span class="description-tooltip">\r\nAcceso a más de una docena de herramientas de IA especializadas, incluyendo un generador de texto a imagen, un analizador de documentos, un motor de búsqueda mejorado por IA, etc.\r\n</span>\r\n</div>\r\n\r\n',
                        "button_label" => "Continuar"
                    ],
                    "fr" => [
                        "label" => "PRO",
                        "price_text" => "€23.85/Mois",
                        "description" => 'Accès aux Modèles Avancés:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMémoire de Contexte Complète\r\n<span class="description-tooltip">\r\nVoici la quantité d\'informations que le modèle IA se souviendra au cours d\'une seule conversation.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite Complète de Dialogue\r\n<span class="description-tooltip">\r\nCeci est la longueur maximale totale que le modèle IA peut produire sur une période mensuelle, soit environ 500 000 jetons.\r\n</span>\r\n</div>\r\n<div>Sauvegardes illimitées de l\'historique de chat</div>\r\n<div>Modèles de Commandes</div>\r\n<div>Téléchargement de fichiers image</div>\r\nOutils Avancés:\r\n<div class="hover-target">\r\nAccès Complet\r\n<span class="description-tooltip">\r\nAccès à plus d\'une douzaine d\'outils IA spécialisés, y compris un générateur de texte en image, un analyseur de documents, un moteur de recherche amélioré par IA, etc.\r\n</span>\r\n</div>\r\n',
                        "button_label" => "Continuer"
                    ],
                    'pt' => [
                        'label' => 'PRO',
                        'price_text' => '€23.85/Mês',
                        'description' => 'Acesso aos Modelos Avançados:\r\n<div class="indent">GPT-3.5 TURBO</div>\r\n<div class="indent">GPT-4o mini</div>\r\n<div class="indent">GPT-4o</div>\r\n<div class="indent">LLaMA3-70B</div>\r\n<div class="indent">Gemini Pro</div>\r\n<br>\r\n<div class="hover-target">\r\nMemória de Contexto Completa\r\n<span class="description-tooltip">\r\nEsta é a quantidade de informação que o modelo de IA lembrará dentro de uma única conversa.\r\n</span>\r\n</div>\r\n<div class="hover-target">\r\nLimite Total de Diálogo\r\n<span class="description-tooltip">\r\nEste é o comprimento total máximo que o modelo de IA pode gerar durante um período mensal. Aproximadamente 500.000 tokens.\r\n</span>\r\n</div>\r\n<div>Salvamento Ilimitado do Histórico de Conversas</div>\r\n<div>Modelos de Comando</div>\r\n<div>Upload de Arquivo de Imagem</div>\r\nFerramentas Avançadas:\r\n<div class="hover-target">\r\nAcesso Completo\r\n<span class="description-tooltip">\r\nAcesso a mais de uma dúzia de ferramentas de IA especializadas, incluindo um gerador de texto para imagem, um analisador de documentos, um mecanismo de busca aprimorado por IA, entre outros.\r\n</span>\r\n</div>',
                        'button_label' => 'Continuar'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '35',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540007',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '27',
                'fs_plan_id'        => 'basic-eur-97-35',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60726","live":"842403"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '36',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540008',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '27',
                'fs_plan_id'        => 'pro-eur-197-36',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60728","live":"842405"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '37',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540009',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '28',
                'fs_plan_id'        => 'basic-brl-49-37',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60729","live":"842406"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '49',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$49/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '38',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540010',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '28',
                'fs_plan_id'        => 'pro-brl-114-38',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60731","live":"842408"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '114',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$114/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '39',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540011',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '29',
                'fs_plan_id'        => 'basic-brl-465-39',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60730","live":"842407"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '465',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$465/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '40',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540012',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '29',
                'fs_plan_id'        => 'pro-brl-945-40',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60732","live":"842409"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '945',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$945/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '41',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540013',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '30',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '90ر.س./Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '42',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540014',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '30',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '98',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '98ر.س./Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '43',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540015',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '31',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '860',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '860ر.س./Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '44',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540016',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '31',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '940',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '940ر.س./Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '45',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540017',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '32',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '90AED/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '46',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540018',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '32',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '98',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '98AED/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '47',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540019',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '33',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '860',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '860AED/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '48',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540020',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '33',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '940',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '940AED/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '49',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540021',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '34',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '3',
                'payment_interval'  => 'Yearly',
                'price'             => '20',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '50',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540022',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '35',
                'fs_plan_id'        => 'trial-285-7-10',
                'recurly_plan_id'   => 'trial-285-7-10',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '7',
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $10 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '51',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540023',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '36',
                'fs_plan_id'        => 'basic-eur-10-51',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '7',
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => '2.85',
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€10/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged €10 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '52',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540024',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '37',
                'fs_plan_id'        => 'basic-gbp-10-52',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '7',
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => '2.85',
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£10/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged £10 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '54',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540025',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '38',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '7',
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => '2.85',
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '90ر.س/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 90ر.س after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '55',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540026',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '39',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '7',
                'payment_interval'  => 'Monthly',
                'price'             => '90',
                'trial_price'       => '2.85',
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '90AED/Month',
                'display_txt2'      => $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 90AED after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ], // PPG 40 is already used (combination of 14 and 17), use PPG 41 for next PPG
            [
                'plan_id'           => '56',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539997',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '41',
                'fs_plan_id'        => 'pro-23851',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '57',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-02822a539998',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '42',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '20ر.س./Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '58',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '43',
                'fs_plan_id'        => null,
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '20AED/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '59',
                'plan_pid'          => 'f5dea692-f94c-11ed-ba03-02822a549919',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '3-Day Trial',
                'ppg'               => '44',
                'fs_plan_id'        => 'pro-3days-trial-23-85',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => '3',
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$2.85 for 3 days, then $23.85/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $23.85 after 3 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '60',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822x139999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '45',
                'fs_plan_id'        => 'basic-15',
                'recurly_plan_id'   => 'basic-15',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"65985","live":"860982"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '15',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$15/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '61',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822x239998',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '45',
                'fs_plan_id'        => 'pro-27',
                'recurly_plan_id'   => 'pro-27',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"65984","live":"860981"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '27',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$27/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '62',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba03-02822a5399100',
                'plan_name'         => 'Enterprise',
                'plan_type'         => 'Enterprise',
                'details'           => null,
                'label'             => 'ENTERPRISE',
                'ppg'               => '46',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'enterprise-1920',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '1920',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '192',
                'max_members'       => '9',
                'sort_order'          => '3',
                'display_txt1'      => '$1,920/Year',
                'display_txt2'      => $this->enterprise_pricing_description,
                'display_txt3'      => 'Your subscription will renew yearly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '63',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '46',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => 'basic-annual-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54608","live":"839874"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '1',
                'display_txt1'      => '$97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '64',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540000',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '46',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '2',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '65',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba05-02822x138991',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '47',
                'fs_plan_id'        => 'basic-20',
                'recurly_plan_id'   => 'basic-20',
                'stripe_plan_id'    => '{"test":"prod_OCUWwiEMO9bZaP","live":"prod_OCUUZxq2eSLN5r"}',
                'paddle_plan_id'    => '{"test":"52616","live":"833099"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '66',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822y239998',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '47',
                'fs_plan_id'        => 'pro-37',
                'recurly_plan_id'   => 'pro-37',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"66329","live":"863733"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '37',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$37/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ], // PPG 48 is already used (combination of 14 and 17), use PPG 49 for next PPG
            [
                'plan_id'           => '67',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y240000',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '49',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '68',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba05-12822x138991',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '50',
                'fs_plan_id'        => 'basic-20',
                'recurly_plan_id'   => 'basic-20',
                'stripe_plan_id'    => '{"test":"prod_OCUWwiEMO9bZaP","live":"prod_OCUUZxq2eSLN5r"}',
                'paddle_plan_id'    => '{"test":"52616","live":"833099"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '69',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-22822y239998',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '50',
                'fs_plan_id'        => 'pro-40',
                'recurly_plan_id'   => 'pro-40',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67174","live":"869357"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '40',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$40/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '70',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a539996',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '51',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '71',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a540000',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '51',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            [
                'plan_id'           => '72',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '52',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '73',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '52',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '74',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '52',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '75',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '52',
                'fs_plan_id'        => 'pro-max-932',
                'recurly_plan_id'   => 'pro-max-932',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67559","live":"872157"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '76',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n5',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '53',
                'fs_plan_id'        => 'promax-gbp-97-76',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67648","live":"872399"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '77',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n6',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '53',
                'fs_plan_id'        => 'promax-gbp-932-77',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67649","live":"872400"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '78',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n7',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '54',
                'fs_plan_id'        => 'promax-eur-97-78',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67650","live":"872401"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '79',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n8',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '54',
                'fs_plan_id'        => 'promax-eur-932-79',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67651","live":"872402"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '80',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-22822y2400n8',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '55',
                'fs_plan_id'        => 'promax-brl-482-80',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67652","live":"872403"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '482',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$482/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '81',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n9',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '55',
                'fs_plan_id'        => 'promax-brl-4616-81',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67653","live":"872404"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '4616',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$4,616/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '82',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400o0',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '56',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '364',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '364ر.س./Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '83',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400o1',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '56',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '3495',
                'trial_price'       => null,
                'currency'          => 'SAR',
                'currency_symbol'   => 'ر.س.',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3,495ر.س./Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '84',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400o2',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '57',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '357',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '357AED/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '85',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400o3',
                'plan_name'         => 'PRO MAX Annual',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '57',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '3424',
                'trial_price'       => null,
                'currency'          => 'AED',
                'currency_symbol'   => 'AED',
                'country'           => 'AE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3,424AED/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '86',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400o4',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '58',
                'fs_plan_id'        => 'pro-23-97-month',
                'recurly_plan_id'   => 'pro-23-97-month',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67857","live":"873636"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.97/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            //NEW PPG Switzerland
            [
                'plan_id'           => '87',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539901',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '60',
                'fs_plan_id'        => 'pro-chf-20-87',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68350","live":"877188"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => '2.85',
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '2.85CHF for 14 days, then 20CHF/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 20CHF after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '88',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539903',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '61',
                'fs_plan_id'        => 'basic-chf-10-88',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68351","live":"877189"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '10CHF/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '89',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539902',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '61',
                'fs_plan_id'        => 'pro-chf-20-89',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68352","live":"877190"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '20CHF/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            //NEW PPG Sweden
            [
                'plan_id'           => '90',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539904',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '62',
                'fs_plan_id'        => 'pro-sek-200-90',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68353","live":"877191"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '200',
                'trial_price'       => '29',
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '29SEK for 14 days, then 200SEK/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 200SEK after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '91',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539906',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '63',
                'fs_plan_id'        => 'basic-sek-100-91',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68354","live":"877192"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '100',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '100SEK/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '92',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539905',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '63',
                'fs_plan_id'        => 'pro-sek-200-92',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68355","live":"877193"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '200',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '200SEK/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            //Upgrade Downgrade for CHF
            [
                'plan_id'           => '93',
                'plan_pid'          => 'd4dea691-f94c-11ed-ba14-12833a539910',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '68',
                'fs_plan_id'        => 'basic-chf-10-93',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68359","live":"877202"}',
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '10CHF/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '94',
                'plan_pid'          => 'd4dea691-f94c-11ed-ba14-12833a539911',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '69',
                'fs_plan_id'        => 'basic-chf-97-94',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68360","live":"877203"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '97CHF/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '95',
                'plan_pid'          => 'd4dea691-f94c-11ed-ba14-12833a539909',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '67',
                'fs_plan_id'        => 'pro-chf-197-95',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68358","live":"877201"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '197CHF/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '96',
                'plan_pid'          => 'd4dea691-f94c-11ed-ba14-12833a539907',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '64',
                'fs_plan_id'        => 'promax-chf-97-96',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68356","live":"877195"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '97CHF/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '97',
                'plan_pid'          => 'd4dea691-f94c-11ed-ba14-12833a539908',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '65',
                'fs_plan_id'        => 'promax-chf-932-97',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68357","live":"877196"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '932CHF/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            //Upgrade Downgrade for SEK
            [
                'plan_id'           => '98',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539910',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '68',
                'fs_plan_id'        => 'basic-sek-100-98',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68364","live":"877207"}',
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '100',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '100SEK/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '99',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539911',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '69',
                'fs_plan_id'        => 'basic-sek-970-99',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68365","live":"877208"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '970',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '970SEK/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '100',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539909',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '67',
                'fs_plan_id'        => 'pro-sek-1970-100',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68363","live":"877206"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '1970',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '1970SEK/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '101',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539908',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '65',
                'fs_plan_id'        => 'promax-sek-9320-101',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68362","live":"877205"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '9320',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '9320SEK/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '102',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539908',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '66',
                'fs_plan_id'        => 'promax-sek-970-102',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"68361","live":"877204"}',
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '970',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '970SEK/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '103',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539901',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '70',
                'fs_plan_id'        => 'pro-max-197',
                'recurly_plan_id'   => 'pro-max-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69137","live":"882351"}',
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '104',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539903',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '70',
                'fs_plan_id'        => 'pro-max-1891',
                'recurly_plan_id'   => 'pro-max-1891',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69138","live":"882363"}',
                'trial_days'        => null,
                'payment_interval'  => 'yearly',
                'price'             => '1891',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$1,891/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '105',
                'plan_pid'          => 'f5deb691-f94c-11ed-ba03-02922a5389101',
                'plan_name'         => 'Enterprise',
                'plan_type'         => 'Enterprise',
                'details'           => null,
                'label'             => 'ENTERPRISE',
                'ppg'               => '71',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'ent-monthly-160',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '160',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '16',
                'max_members'       => '9',
                'sort_order'          => '3',
                'display_txt1'      => '$160/Month',
                'display_txt2'      => $this->enterprise_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            //NEW PPGs FOR POLAND
            [
                'plan_id'           => '106',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531806',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '72',
                'fs_plan_id'        => 'pro-pln-95-106',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69900","live":"887620"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '95',
                'trial_price'       => '11.35',
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '11.35zł for 14 days, then 95zł/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 95zł after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('14-dniowy okres próbny', 'Pro', '11.35zł', [
                        'trial_days' => 14,
                        'price_text' => '95zł',
                    ], 106),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '107',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531807',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '73',
                'fs_plan_id'        => 'basic-pln-40-107',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69901","live":"887622"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '40',
                'trial_price'       => null,
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '40zł/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '108',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531808',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '73',
                'fs_plan_id'        => 'pro-pln-95-108',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69902","live":"887623"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '95',
                'trial_price'       => null,
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '95zł/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '109',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531809',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '74',
                'fs_plan_id'        => 'pro-pln-80-109',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69903","live":"887624"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '80',
                'trial_price'       => null,
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '80zł/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '110',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531810',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '74',
                'fs_plan_id'        => 'promax-pln-385-110',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69904","live":"887625"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '385',
                'trial_price'       => null,
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '385zł/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '111',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531836',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '90',
                'fs_plan_id'        => 'basic-pln-20-111',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70000","live":"888173"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'PLN',
                'currency_symbol'   => 'zł',
                'country'           => 'PL',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '20zł/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            //NEW PPGs FOR ROMANIA
            [
                'plan_id'           => '112',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531811',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '75',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '109',
                'trial_price'       => '13.15',
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '13.15LEI for 14 days, then 109LEI/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 109LEI after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '113',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531812',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '76',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '46',
                'trial_price'       => null,
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '46LEI/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '114',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531813',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '76',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '109',
                'trial_price'       => null,
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '109LEI/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '115',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531814',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '77',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '92',
                'trial_price'       => null,
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '92LEI/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '116',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531815',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '77',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '449',
                'trial_price'       => null,
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '449LEI/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '117',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531837',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '91',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23',
                'trial_price'       => null,
                'currency'          => 'RON',
                'currency_symbol'   => 'LEI',
                'country'           => 'RO',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '23LEI/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            //NEW PPGs FOR CZECH REPUBLIC
            [
                'plan_id'           => '118',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531816',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '78',
                'fs_plan_id'        => 'pro-czk-549-118',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69905","live":"887632"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '549',
                'trial_price'       => '65.45',
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč65.45 for 14 days, then Kč549/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged Kč549 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '119',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531817',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '79',
                'fs_plan_id'        => 'basic-czk-230-119',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69906","live":"887659"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '230',
                'trial_price'       => null,
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč230/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '120',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531818',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '79',
                'fs_plan_id'        => 'pro-czk-549-120',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69907","live":"887668"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '549',
                'trial_price'       => null,
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč549/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '121',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531819',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '80',
                'fs_plan_id'        => 'pro-czk-460-121',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69908","live":"887669"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '460',
                'trial_price'       => null,
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč460/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '122',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531820',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '80',
                'fs_plan_id'        => 'promax-czk-2225-122',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69909","live":"887670"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '2225',
                'trial_price'       => null,
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč2,225/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '123',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531838',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '92',
                'fs_plan_id'        => 'basic-czk-115-123',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70001","live":"888174"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '115',
                'trial_price'       => null,
                'currency'          => 'CZK',
                'currency_symbol'   => 'Kč',
                'country'           => 'CZ',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'Kč115/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            //NEW PPGs FOR HUNGARY
            [
                'plan_id'           => '124',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531821',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '81',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69910","live":"887671"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '8590',
                'trial_price'       => '1025',
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '1,025Ft for 14 days, then 8,590Ft/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 8590Ft after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '125',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531822',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '82',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69911","live":"887672"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '3600',
                'trial_price'       => null,
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3,600Ft/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '126',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531823',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '82',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69912","live":"887673"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '8590',
                'trial_price'       => null,
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '8,590Ft/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '127',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531824',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '83',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69913","live":"887674"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '7200',
                'trial_price'       => null,
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '7,200Ft/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '128',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531825',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '83',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69914","live":"887676"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '34990',
                'trial_price'       => null,
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '34,990Ft/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            [
                'plan_id'           => '129',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531839',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '93',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70002","live":"888175"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '1800',
                'trial_price'       => null,
                'currency'          => 'HUF',
                'currency_symbol'   => 'Ft',
                'country'           => 'HU',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '1,800Ft/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            //NEW PPGs FOR DENMARK
            [
                'plan_id'           => '130',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531826',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '84',
                'fs_plan_id'        => 'pro-dkk-165-130',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69915","live":"887678"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '165',
                'trial_price'       => '19.75',
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '19.75kr. for 14 days, then 165kr./Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 165kr. after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '131',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531827',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '85',
                'fs_plan_id'        => 'basic-dkk-70-131',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69916","live":"887679"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '70',
                'trial_price'       => null,
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '70kr./Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '132',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531828',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '85',
                'fs_plan_id'        => 'pro-dkk-165-132',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69917","live":"887680"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '165',
                'trial_price'       => null,
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '165kr./Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '133',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531829',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '86',
                'fs_plan_id'        => 'pro-dkk-140-133',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69918","live":"887681"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '140',
                'trial_price'       => null,
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '140kr./Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '134',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531830',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '86',
                'fs_plan_id'        => 'promax-dkk-675-134',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"69919","live":"887682"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '675',
                'trial_price'       => null,
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '675kr./Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '135',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531840',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '94',
                'fs_plan_id'        => 'basic-dkk-35-135',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70003","live":"888176"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '35',
                'trial_price'       => null,
                'currency'          => 'DKK',
                'currency_symbol'   => 'kr.',
                'country'           => 'DK',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '35kr./Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            //NEW PPGs FOR BULGARIA
            [
                'plan_id'           => '136',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531831',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '87',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '45',
                'trial_price'       => '5.15',
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '5.15лв for 14 days, then 45лв/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 45лв after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '137',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531832',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '88',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '18',
                'trial_price'       => null,
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '18лв/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '138',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531833',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '88',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '45',
                'trial_price'       => null,
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '45лв/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '139',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531834',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '89',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '36',
                'trial_price'       => null,
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '36лв/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '140',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-02822a531835',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '89',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '175',
                'trial_price'       => null,
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '175лв/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '141',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a531841',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '95',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '9',
                'trial_price'       => null,
                'currency'          => 'BGN',
                'currency_symbol'   => 'лв',
                'country'           => 'BG',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '9лв/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '142',
                'plan_pid'          => 'f4dea791-f94c-11ed-ee04-02822a531142',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'PRO',
                'details'           => null,
                'label'             => '3-Day Trial',
                'ppg'               => '96',
                'fs_plan_id'        => 'pro-3days-free-trial-23-97',
                'recurly_plan_id'   => 'pro-3days-free-trial-23-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70389","live":"890810"}',
                'trial_days'        => '3',
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '0',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$0 FOR 3 DAYS, THEN $23.85/MONTH',
                'display_txt2'      => '<span style="text-align: center; font-size: 13px; font-style: italic;"><span style="font-weight:bold;">FREE 3-day trial</span> access to Pro.<br>$23.85/month after trial ends.<br>Cancel anytime.</span><br><br>' . $this->pro_pricing_description,
                'display_txt3'      => 'You will have free trial access to our Pro plan for 3 days. Cancel your trial at any time at no charge or continue your subscription after the trial ends for $23.85/month.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '143',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '97',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10-ppg14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833101"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('BASIC', 'Basic', '$10'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '144',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '97',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('PRO', 'Pro', '$20'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '145',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '97',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('PRO MAX', 'ProMax', '$97'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '146',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '97',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => 'basic-annual-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54608","live":"839874"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('BASIC ANNUAL', 'Basic', '$97'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '147',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '97',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('PRO ANNUAL', 'Pro', '$197'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '148',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '97',
                'fs_plan_id'        => 'pro-max-932',
                'recurly_plan_id'   => 'pro-max-932',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67559","live":"872157"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'pl' => $this->getPolishLocale('PRO MAX ANNUAL', 'ProMax', '$932'),
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '149',
                'plan_pid'          => 'sedea691-f94c-11ed-ba14-12833a539912',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '98',
                'fs_plan_id'        => 'basic-sek-50-149',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70464","live":"891750"}',
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '50',
                'trial_price'       => null,
                'currency'          => 'SEK',
                'currency_symbol'   => 'SEK',
                'country'           => 'SE',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '50SEK/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '150',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12833a539904',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '99',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => '',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"70465","live":"891751"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '5',
                'trial_price'       => null,
                'currency'          => 'CHF',
                'currency_symbol'   => 'CHF',
                'country'           => 'CH',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '5CHF/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            [
                'plan_id'           => '151',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539992',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '108',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '{"dl_test":"price_1PnaXkLtUaKDxQEZXwLaF71u","dl_live":"price_1PnacILtUaKDxQEZXGeCVTwi"}',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'        => '',
                'display_txt1'      => '<div class="price_text"><span class="price_bold">$23.85</span><span class="price_small">/Month</span></div><div class="price_sub_text">or $197 annual <span class="price_bubble">30% off</span></div>',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => '<div style="font-size:14px; font-weight:bold; color: rgb(75, 85, 99)"><span style="font-size:36px;">$23.85</span>/Month</div><div style="font-size:14px; font-weight:bold; color: rgb(75, 85, 99)">or $197 annual <span style="background-color:#1d4ed8; border-radius: 1.5rem;; color:#fff; border-radius: 1.5rem; padding: 2px 6px 2px 6px;">30% off</span></div>',
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => '{"upgrade_id":"74"}'
            ],
            [
                'plan_id'           => '152',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y240001',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '108',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"dl_test":"price_1Pnab1LtUaKDxQEZFffYw1SC","dl_live":"price_1PnacMLtUaKDxQEZV9HzIPv0"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '<div class="price_text"><span class="price_bold">$97</span><span class="price_small">/Month</span></div><div class="price_sub_text">or $932 annual <span class="price_bubble">20% off</span></div>',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => '<div style="font-size:14px; font-weight:bold; color: rgb(75, 85, 99)"><span style="font-size:36px;">$97</span>/Month</div><div style="font-size:14px; font-weight:bold; color: rgb(75, 85, 99)">or $932 annual <span style="background-color:#1d4ed8; border-radius: 1.5rem;; color:#fff; border-radius: 1.5rem; padding: 2px 6px 2px 6px;">20% off</span></div>',
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => '{"upgrade_id":"75"}'
            ],
            [
                'plan_id'           => '153',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y240000',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '117',
                'fs_plan_id'        => 'pro-max-97-3day-trial',
                'recurly_plan_id'   => 'pro-max-97-3day-trial',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => '3',
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => '0',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$0 FOR 3 DAYS, THEN $97/MONTH',
                'display_txt2'      => '<span style="text-align: center; font-size: 13px; font-style: italic;"><span style="font-weight:bold;">FREE 3-day trial</span> access to Pro Max.<br>$97/month after trial ends.<br>Cancel anytime.</span><br><br>' . $this->promax_pricing_description,
                'display_txt3'      => 'You will have free trial access to our Pro Max plan for 3 days. Cancel your trial at any time at no charge or continue your subscription after the trial ends for $97/month.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],

            [
                'plan_id'           => '154',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '101',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60720","live":"842398"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '155',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '101',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"72038","live":"897100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '156',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '101',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67648","live":"872399"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '157',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '101',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60722","live":"842399"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '158',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '101',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60724","live":"842401"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '159',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '101',
                'fs_plan_id'        => 'pro-max-932',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67649","live":"872400"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '160',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '109',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60725","live":"842402"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '161',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '109',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"72084","live":"897348"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '162',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '109',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67650","live":"872401"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '163',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '109',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60726","live":"842403"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '164',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '109',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60728","live":"842405"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '165',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '109',
                'fs_plan_id'        => 'pro-max-932',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67651","live":"872402"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '932',
                'trial_price'       => null,
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€932/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '166',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '110',
                'fs_plan_id'        => 'basic-brl-49-37',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60729","live":"842406"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '49',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$49/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '167',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '110',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"72085","live":"897350"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '98',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$98/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '168',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '110',
                'fs_plan_id'        => 'promax-brl-482-80',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67652","live":"872403"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '482',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$482/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '169',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '110',
                'fs_plan_id'        => 'basic-brl-465-39',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60730","live":"842407"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '465',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$465/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '170',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '110',
                'fs_plan_id'        => 'pro-brl-945-40',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"60732","live":"842409"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '945',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$945/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '171',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX Annual',
                'ppg'               => '110',
                'fs_plan_id'        => 'promax-brl-4616-81',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"67653","live":"872404"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '4616',
                'trial_price'       => null,
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$4,616/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ], 
            [
                'plan_id'           => '172',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539984',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '111',
                'fs_plan_id'        => 'trial-pro-try-97-899',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72389","live":"898578"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '899',
                'trial_price'       => '97',
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '97₺ for 14 days, then 899₺/Month',
                'display_txt2'      => $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged 899₺ after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => '14 Günlük Deneme',
                        'price_text' => '97₺',
                        'description' => 'Gelişmiş Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">DeepSeek V3</div><div class="hover-target indent">DeepSeek R1: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="hover-target indent">Claude 3.7: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent">DALL-E 3: Aylık 50 Görsel</div><div class="indent">Flux: Aylık 30 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br /><div class="hover-target">Tam Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Tam Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div><div>14 Gün Deneme, ardından sadece ayda 899₺</div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '173',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '112',
                'fs_plan_id'        => 'basic-try-340',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72390","live":"898579"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '340',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '340₺/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'BASIC',
                        'price_text' => '340₺/Month',
                        'description' => 'Temel Modeller\'e Erişim::<div class="indent">GPT-4o mini</div><div class="indent">GPT-4o</div><div class="indent">LLaMA3-8B</div><div class="indent">Gemma-7B</div><div class="hover-target indent">Claude 3.5: 25K Token/Ay<span class="description-tooltip">Yaklaşık 1.000 cümle</span></div><div class="indent">DALL-E 3: 20 Görsel/Ay</div><div class="indent newBadge">Flux: 15 Görsel/Ay✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><br /><div class="hover-target">Standart Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir</span></div><div class="hover-target">Standart Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500,000 token.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '174',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539996',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '112',
                'fs_plan_id'        => 'pro-try-899',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72391","live":"898580"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '899',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '899₺/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO',
                        'price_text' => '899₺/Month',
                        'description' => 'Gelişmiş Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">DeepSeek V3</div><div class="hover-target indent">DeepSeek R1: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="hover-target indent">Claude 3.7: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent">DALL-E 3: Aylık 50 Görsel</div><div class="indent">Flux: Aylık 30 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br /><div class="hover-target">Tam Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Tam Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '175',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '113',
                'fs_plan_id'        => 'pro-try-680',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72392","live":"898581"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '680',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '680₺/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO',
                        'price_text' => '680₺/Month',
                        'description' => 'Gelişmiş Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">DeepSeek V3</div><div class="hover-target indent">DeepSeek R1: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="hover-target indent">Claude 3.7: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent">DALL-E 3: Aylık 50 Görsel</div><div class="indent">Flux: Aylık 30 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br /><div class="hover-target">Tam Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Tam Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '176',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '113',
                'fs_plan_id'        => 'pro-max-try-3330',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72393","live":"898583"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '3330',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3330₺/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO MAX',
                        'price_text' => '3330₺/Month',
                        'description' => 'Premium Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">GPT-4</div><div class="indent">OpenAI o1</div><div class="indent newBadge">OpenAI o3 mini✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DeepSeek V3</div><div class="indent">DeepSeek R1</div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent newBadge">Claude 3.7✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DALL-E 3</div><div class="indent">Flux: Aylık 160 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><div class="indent">(10+ diğer LLM Modelleri)</div><br /><div class="hover-target">Gelişmiş Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Gelişmiş Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '177',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '114',
                'fs_plan_id'        => 'basic-try-340',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72390","live":"898579"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '340',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '340₺/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'BASIC',
                        'price_text' => '340₺/Month',
                        'description' => 'Temel Modeller\'e Erişim::<div class="indent">GPT-4o mini</div><div class="indent">GPT-4o</div><div class="indent">LLaMA3-8B</div><div class="indent">Gemma-7B</div><div class="hover-target indent">Claude 3.5: 25K Token/Ay<span class="description-tooltip">Yaklaşık 1.000 cümle</span></div><div class="indent">DALL-E 3: 20 Görsel/Ay</div><div class="indent newBadge">Flux: 15 Görsel/Ay✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><br /><div class="hover-target">Standart Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir</span></div><div class="hover-target">Standart Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500,000 token.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '178',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '114',
                'fs_plan_id'        => 'pro-try-680',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72392","live":"898581"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '680',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '680₺/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO',
                        'price_text' => '680₺/Month',
                        'description' => 'Gelişmiş Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">DeepSeek V3</div><div class="hover-target indent">DeepSeek R1: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="hover-target indent">Claude 3.7: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent">DALL-E 3: Aylık 50 Görsel</div><div class="indent">Flux: Aylık 30 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br /><div class="hover-target">Tam Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Tam Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '179',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '114',
                'fs_plan_id'        => 'pro-max-try-3330',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72393","live":"898583"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '3330',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3330₺/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO MAX',
                        'price_text' => '3330₺/Month',
                        'description' => 'Premium Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">GPT-4</div><div class="indent">OpenAI o1</div><div class="indent newBadge">OpenAI o3 mini✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DeepSeek V3</div><div class="indent">DeepSeek R1</div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent newBadge">Claude 3.7✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DALL-E 3</div><div class="indent">Flux: Aylık 160 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><div class="indent">(10+ diğer LLM Modelleri)</div><br /><div class="hover-target">Gelişmiş Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Gelişmiş Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '180',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '114',
                'fs_plan_id'        => 'basic-try-yearly-3330',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72394","live":"898584"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '3330',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '3330₺/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'BASIC',
                        'price_text' => '3330₺/Year',
                        'description' => 'Temel Modeller\'e Erişim::<div class="indent">GPT-4o mini</div><div class="indent">GPT-4o</div><div class="indent">LLaMA3-8B</div><div class="indent">Gemma-7B</div><div class="hover-target indent">Claude 3.5: 25K Token/Ay<span class="description-tooltip">Yaklaşık 1.000 cümle</span></div><div class="indent">DALL-E 3: 20 Görsel/Ay</div><div class="indent newBadge">Flux: 15 Görsel/Ay✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><br /><div class="hover-target">Standart Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir</span></div><div class="hover-target">Standart Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 500,000 token.</span></div><div>Sınırsız Sohbet Geçmişi Kaydetme</div><div>Görüntü Dosyası Yükleme</div><br />Gelişmiş Araçlar:<div>Sadece Metinden Görüntü Üretici</div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '181',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539981n3',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '114',
                'fs_plan_id'        => 'pro-try-yearly-6690',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72395","live":"898585"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '6690',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '6690₺/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO',
                        'price_text' => '6690₺/Year',
                        'description' => 'Gelişmiş Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">DeepSeek V3</div><div class="hover-target indent">DeepSeek R1: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="hover-target indent">Claude 3.7: Aylık 50K Token<span class="description-tooltip">Yaklaşık 2.000 cümle.</span></div><div class="indent">DALL-E 3: Aylık 50 Görsel</div><div class="indent">Flux: Aylık 30 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><br /><div class="hover-target">Tam Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Tam Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur. Yaklaşık 1,000,000 token.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '182',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n4',
                'plan_name'         => 'PRO MAX',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX ANNUAL',
                'ppg'               => '114',
                'fs_plan_id'        => 'pro-max-yearly-31960',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72396","live":"898586"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '31960',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '31960₺/Year',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => 'PRO MAX',
                        'price_text' => '31960₺/Year',
                        'description' => 'Premium Modeller\'e Erişim:<div class="indent">GPT-4o</div><div class="indent">GPT-4</div><div class="indent">OpenAI o1</div><div class="indent newBadge">OpenAI o3 mini✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DeepSeek V3</div><div class="indent">DeepSeek R1</div><div class="indent newBadge">Grok AI✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent newBadge">Claude 3.7✨<span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;">YENİ</span></div><div class="indent">DALL-E 3</div><div class="indent">Flux: Aylık 160 Görsel</div><div class="indent">LLaMA3-70B</div><div class="indent">Gemini Pro</div><div class="indent">(10+ diğer LLM Modelleri)</div><br /><div class="hover-target">Gelişmiş Bağlam Hafızası<span class="description-tooltip">Bu, AI modelinin bir sohbet içinde ne kadar hatırlayacağını gösterir.</span></div><div class="hover-target">Gelişmiş Diyalog Sınırı<span class="description-tooltip">Bu, yapay zeka modelinin bir aylık dönem içinde maksimum toplam çıktı uzunluğudur.</span></div><div class="hover-target">Gelişmiş Araçlara Tam Erişim<span class="description-tooltip">Metin\'den görüntüye dönüştürücü, belge analizörü, AI destekli arama motoru gibi bir düzineden fazla özel yapay zeka aracına erişim.</span></div>',
                        'button_label' => 'Devam ediniz'
                    ]
                ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '183',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a140000',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '115',
                'fs_plan_id'        => 'basic-try-170',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72397","live":"898587"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '170',
                'trial_price'       => null,
                'currency'          => 'TRY',
                'currency_symbol'   => '₺',
                'country'           => 'TR',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '170₺/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '184',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-xxxxxxx00001',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '118',
                'fs_plan_id'        => 'pro-23851',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.85/Month',
                'display_txt2'      => $this->pro_single_pricing_description,
                'display_txt3'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0
            ],
            [
                'plan_id'           => '185',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-xxxxxxx00002',
                'plan_name'         => 'Team Max',
                'plan_type'         => 'Enterprise',
                'details'           => null,
                'label'             => 'Team Max',
                'ppg'               => '118',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'ent-monthly-139',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '139.95',
                'trial_price'       => null,
                'currency'          => 'USD',
                'price_per_member'  => '46.65',
                'max_members'       => '2',
                'sort_order'          => '3',
                'display_txt1'      => '$139.95/Month',
                'display_txt2'      => $team_max_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0
            ],
            [
                'plan_id'           => '186',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-xxxxxxx00003',
                'plan_name'         => 'Office Max',
                'plan_type'         => 'Enterprise',
                'details'           => null,
                'label'             => 'Office Max',
                'ppg'               => '118',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'ent-monthly-249',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '249.95',
                'trial_price'       => null,
                'currency'          => 'USD',
                'price_per_member'  => '49.99',
                'max_members'       => '4',
                'sort_order'          => '3',
                'display_txt1'      => '$249.95/Month',
                'display_txt2'      => $office_max_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0
            ],
            [
                'plan_id'           => '187',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-xxxxxxx00004',
                'plan_name'         => 'Enterprise Max',
                'plan_type'         => 'Enterprise',
                'details'           => null,
                'label'             => 'Enterprise Max',
                'ppg'               => '118',
                'fs_plan_id'        => '',
                'recurly_plan_id'   => 'ent-monthly-499',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => null,
                'trial_days'        => null,
                'payment_interval'  => 'monthly',
                'price'             => '499.95',
                'trial_price'       => null,
                'currency'          => 'USD',
                'price_per_member'  => '49.99',
                'max_members'       => '9',
                'sort_order'          => '3',
                'display_txt1'      => '$499.95/Month',
                'display_txt2'      => $enterprise_max_pricing_description,
                'display_txt3'      => 'Your subscription will renew monthly until you cancel it.',
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0
            ],
            [
                'plan_id'           => '188',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '40',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10-ppg14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833101"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '189',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539996',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '40',
                'fs_plan_id'        => 'pro-23851',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '190',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '40',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => 'basic-annual-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54608","live":"839874"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '191',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540000',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '40',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '192',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539995',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC',
                'ppg'               => '48',
                'fs_plan_id'        => 'basic-10',
                'recurly_plan_id'   => 'basic-10-ppg14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52615","live":"833101"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '10',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$10/Month',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '193',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539996',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '48',
                'fs_plan_id'        => 'pro-23851',
                'recurly_plan_id'   => 'pro-23851',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52619","live":"833102"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$23.85/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '194',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539999',
                'plan_name'         => 'BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => 'BASIC ANNUAL',
                'ppg'               => '48',
                'fs_plan_id'        => 'basic-annual-97',
                'recurly_plan_id'   => 'basic-annual-97',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54608","live":"839874"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Year',
                'display_txt2'      => $this->basic_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '195',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a540000',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO ANNUAL',
                'ppg'               => '48',
                'fs_plan_id'        => 'pro-annual-197',
                'recurly_plan_id'   => 'pro-annual-197',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"54609","live":"839875"}',
                'trial_days'        => null,
                'payment_interval'  => 'Yearly',
                'price'             => '197',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$197/Year',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => 'Your subscription will renew annually until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '196',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '58',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '197',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '119',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->ppgWithAdvancePlanDescription('Pro'),
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '198',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'ADVANCED',
                'plan_type'         => 'Advanced',
                'details'           => null,
                'label'             => 'ADVANCED',
                'ppg'               => '119',
                'fs_plan_id'        => 'advanced-49',
                'recurly_plan_id'   => 'advanced-49',
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"72789","live":"900718"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '49',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$49/Month',
                'display_txt2'      => $this->ppgWithAdvancePlanDescription('Advanced'),
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '199',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '119',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => null,
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->ppgWithAdvancePlanDescription('ProMax'),
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '200',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539987',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '102',
                'fs_plan_id'        => 'pro-gbp-2385-7',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53678","live":"837581"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£2.85 for 14days, then £23.85/Month',
                'display_txt2'      => "Generate Images from Text <br>" . $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged £23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '201',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539993',
                'plan_name'         => 'TRIAL-BASIC',
                'plan_type'         => 'Basic',
                'details'           => null,
                'label'             => '7-Day Trial',
                'ppg'               => '103',
                'fs_plan_id'        => 'basic-brl-49-13',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53680","live":"837583"}',
                'trial_days'        => 7,
                'payment_interval'  => 'Monthly',
                'price'             => '49',
                'trial_price'       => '5.77',
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$5.77 for 7 days, then R$49/Month',
                'display_txt2'      => "Generate Images from Text <br>" . $this->basic_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged R$49.00 after 7 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    "pt" => [
                        "label" => "7 dias de Teste Gratuito",
                        "price_text" => "R$5.77",
                        "description" => 'Acesso GPT-3.5<br>
                            Acesso Limitado ao GPT-4 (NOVO)<br>
                            &nbsp;&nbsp;&nbsp;~1200 frases<br>
                            &nbsp;&nbsp;&nbsp;~25 imagens em alta resolução<br><br>
                            Envio de Arquivos de Imagem<br>
                            Janela de Contexto de 4.096<br>
                            Resposta de 250.000 Tokens<br>
                            Respostas Avançadas<br>
                            Salvamento de Chats Ilimitado<br>
                            Carregamento de Arquivos de Imagem<br>
                            Geração de Imagens a partir de Texto<br>
                            Teste de 7 Dias, depois apenas R$49 por mês.<br>',
                        "button_label" => "Continuar"
                    ]
                    ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '202',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539984',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '100',
                'fs_plan_id'        => 'trial-285-14',
                'recurly_plan_id'   => 'trial-285-14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52620","live":"833103"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$2.85 for 14 days, then $23.85/Month',
                'display_txt2'      => "Generate Images from Text <br>".$this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => json_encode([
                    'tr' => [
                        'label' => '14 Günlük Deneme',
                        'price_text' => '€2.85',
                        'description' => 'GPT-3.5 Erişimi <br>GPT-4o Erişimi <span class="sparkle"></span> <span class="new">NEW</span><br>GPT-4 Erişimi <br><br>8.192 context window <br>500.000 Token Kelime Yanıtı <br>Gelişmiş Yanıtlar <br>Sınırsız Sohbet Kaydı <br>Komut Şablonları <br>Metinden Görüntü Oluşturma <br>14 Günlük Deneme, ardından ayda sadece 23,85 $',
                        'button_label' => 'Devam Et'
                    ]
                    ]),
                'options'           => ''
            ],
            [
                'plan_id'           => '203',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539984',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '104',
                'fs_plan_id'        => 'trial-285-14',
                'recurly_plan_id'   => 'trial-285-14',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52620","live":"833103"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$2.85 for 14 days, then $23.85/Month',
                'display_txt2'      => "ChatPDF - Talk to your documents<br>".$this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged $23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '204',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539986',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '105',
                'fs_plan_id'        => 'pro-eur-2385-6',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53677","live":"837580"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€2.85 for 14 days, then only €23.85/Month',
                'display_txt2'      => "ChatPDF - Talk to your documents<br>" . $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged €23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '205',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539987',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '106',
                'fs_plan_id'        => 'pro-gbp-2385-7',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53678","live":"837581"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'GBP',
                'currency_symbol'   => '£',
                'country'           => 'GB',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '£2.85 for 14days, then £23.85/Month',
                'display_txt2'      => "ChatPDF - Talk to your documents<br>" .  $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged £23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '206',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539997',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '107',
                'fs_plan_id'        => 'pro-brl-110-17',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53687","live":"837603"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '110',
                'trial_price'       => '13.49',
                'currency'          => 'BRL',
                'currency_symbol'   => 'R$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => 'R$13.49 for 14 days, then R$110/Month',
                'display_txt2'      => "ChatPDF - Talk to your documents<br>" . $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged R$110.00 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '207',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-12822a5399n1',
                'plan_name'         => 'PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => 'PRO',
                'ppg'               => '59',
                'fs_plan_id'        => 'pro-20',
                'recurly_plan_id'   => 'pro-20',
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"52617","live":"833100"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '20',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$20/Month',
                'display_txt2'      => $this->pro_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '208',
                'plan_pid'          => 'f4dea691-f94c-11ee-ba05-12822y2400n2',
                'plan_name'         => 'PRO Max',
                'plan_type'         => 'ProMax',
                'details'           => null,
                'label'             => 'PRO MAX',
                'ppg'               => '59',
                'fs_plan_id'        => 'pro-max-97',
                'recurly_plan_id'   => 'pro-max-97',
                'stripe_plan_id'    => '{"test":"prod_OCUWcWHZP8odSH","live":"prod_OCUU8CMKNuLKkL"}',
                'paddle_plan_id'    => '{"test":"67619","live":"872266"}',
                'trial_days'        => null,
                'payment_interval'  => 'Monthly',
                'price'             => '97',
                'trial_price'       => null,
                'currency'          => 'USD',
                'currency_symbol'   => '$',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '$97/Month',
                'display_txt2'      => $this->promax_pricing_description,
                'display_txt3'      => null,
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
            [
                'plan_id'           => '209',
                'plan_pid'          => 'f4dea691-f94c-11ed-ba04-02822a539986',
                'plan_name'         => 'TRIAL-PRO',
                'plan_type'         => 'Pro',
                'details'           => null,
                'label'             => '14-Day Trial',
                'ppg'               => '116',
                'fs_plan_id'        => 'pro-eur-2385-6',
                'recurly_plan_id'   => null,
                'stripe_plan_id'    => '',
                'paddle_plan_id'    => '{"test":"53677","live":"837580"}',
                'trial_days'        => 14,
                'payment_interval'  => 'Monthly',
                'price'             => '23.85',
                'trial_price'       => '2.85',
                'currency'          => 'EUR',
                'currency_symbol'   => '€',
                'country'           => 'US',
                'price_per_member'  => '',
                'max_members'       => '',
                'sort_order'          => '',
                'display_txt1'      => '€2.85 for 14 days, then only €23.85/Month',
                'display_txt2'      => 'Generate Images from Text <br>'. $this->pro_pricing_description . $trial_pricing_description,
                'display_txt3'      => 'You will be charged €23.85 after 14 days of trial. Your subscription will renew monthly until you cancel it.',
                'display_txt4'      => null,
                'created_at'        => $date_now,
                'updated_at'        => $date_now,
                'deleted_at'        => 0,
                'locales'           => null,
                'options'           => ''
            ],
        ];

        $this->db->table($this->table)->where('plan_id >', $this->version)->delete();
        $insertData = [];
        foreach ($data as $key => $value) {
            if($value['plan_id'] > $this->version) {
                $insertData[$key] = [];
                foreach($this->planColumn as $colKey => $colValue) {
                    $insertData[$key][$colValue] = isset($data[$key][$colValue]) ? $data[$key][$colValue] : '';
                }
            }
        }
        if($insertData) $builder = $this->db->table($this->table)->insertBatch($insertData);


        /*
        *
         Update Plan Data 
        *
        */
        // Long description
        // Update plan description
        $basic_data = $this->getBasicData();
        $promax_data = $this->getProMaxData();
        //
        $pro_data = "<div>ChatGPT-powered tools</div>";
        $pro_data .= "<div>8,192 context window</div>";
        $pro_data .= "<div>500,000 Token Cap - comprehensive and exhaustive responses</div>";
        $pro_data .= "<div>Customizable Response Models - can provide scenario-based responses <small>(ex. I am a college professor. Write me a lecture about...)</small></div>";
        $pro_data .= "<div>Save Chat History - store up to hundreds of research results accessible any time</div>";
        $pro_data .= "<div>Choose Light or Dark Mode - for all screen types</div>";
        $pro_data .= "<div>Export Conversations - Image, Text, CSV or JSON files</div>";
        $pro_data .= "<div>AI Art Maker - generate art based on prompts</div>";
        $pro_data .= "<div>Live Support</div>";
        $pro_data .= "<div>GPT-4 model</div>";
        $pro_data .= "<div>Chatbot Themes - change your background color</div>";
        $pro_no_chatpdf_description = $pro_data;
        $pro_data .= "<div>ChatPDF - ask questions based on your PDF document</div>";
        $pro_data .= "<div>Interior AI - create new room designs easily</div>";
        $pro_no_chatpdf_description .= "<div>Interior AI - create new room designs easily</div>";
        $pro_data .= "<div>Stable Diffusion-powered tools to create realistic images</div>";
        $pro_no_chatpdf_description .= "<div>Stable Diffusion-powered tools to create realistic images</div>";
        $pro_data .= "<div>AI Art Prompt Gallery</div>";
        $pro_no_chatpdf_description .= "<div>AI Art Prompt Gallery</div>";
        //
        $data = $this->planType;
        foreach ($data as $key => $value) {
            $builder = $this->db->table($this->table);
            $update_data = [];
            if ($value == 'basic') {
                $update_data['plan_type'] = 'Basic';
                $update_data['plan_description'] = $basic_data;
                if ($key == 63) {
                    $update_data['plan_description'] = $this->basic_pricing_description;
                }
                $update_data['max_tokens'] = '500000';
                $update_data['stripe_plan_id'] = $this->stripe_basic_id;
            } else if ($value == 'enterprise') {
                $update_data['plan_type'] = 'Enterprise';
                $update_data['plan_description'] = $this->enterprise_pricing_description;
                $update_data['stripe_plan_id'] = $this->stripe_pro_id;

                $update_data['max_tokens'] = '740000';
            } else if ($value == 'promax') {
                if ($key!=152){
                    $update_data['stripe_plan_id'] = $this->stripe_promax_id;
                }                
                $update_data['plan_description'] = $promax_data;
                $update_data['max_tokens'] = '99999999';
            } else if ($value == 'promax annual') {
                $update_data['plan_description'] = $promax_data;
                $update_data['stripe_plan_id'] = $this->stripe_promax_annual_id;
                $update_data['max_tokens'] = '99999999';

            } else if ($value == 'advanced') {
                $update_data['max_tokens'] = '2500000';
                $update_data['stripe_plan_id'] = $this->stripe_advanced_id;
            } else if ($value == 'pro single') {
                $update_data['plan_description'] = $this->enterprise_pricing_description;
                $update_data['stripe_plan_id'] = $this->stripe_promax_id;
                $update_data['max_tokens'] = '500000';

                $update_data['sub_text'] = 'For single user only';
                $update_data['display_txt2_1'] = " 
                    <div class='flex'>" . $this->checkIcon . "
                        <p> 
                            For single user only
                        </p>
                    </div>
                    <div class='flex'>" . $this->checkIcon . "
                        <p>
                            Limited access to latest language models: GPT-4o mini, GPT-4o, LLaMA3-8B, Gemini Pro
                        </p>
                    </div>
                    <div class='flex'>" . $this->checkIcon . "
                        <p> 
                            Limited access to AI apps
                        </p>
                    </div>
            
                    ";

            } else if ($value == 'team max') {
                $update_data['plan_description'] = $this->enterprise_pricing_description;
                $update_data['stripe_plan_id'] = $this->stripe_promax_id;
                $update_data['max_tokens'] = '99999999';
                $users = "3";
                $update_data['sub_text']  = 'Up to 3 users';
                $team_max_pricing_description2 = $this->getMaxPricingDescription2($users, "60");
                $update_data['display_txt2_1'] = $team_max_pricing_description2;
            } else if ($value == 'office max') {
                $update_data['plan_description'] = $this->enterprise_pricing_description;
                $update_data['stripe_plan_id'] = $this->stripe_promax_id;
                $update_data['max_tokens'] = '99999999';
                $users = "5";
                $update_data['sub_text']  = 'Up to 5 users';
                $office_max_pricing_description2 = $this->getMaxPricingDescription2($users, "100");
                $update_data['display_txt2_1'] = $office_max_pricing_description2;
            } else if ($value == 'enterprise max') {
                $update_data['plan_description'] = $this->enterprise_pricing_description;
                $update_data['stripe_plan_id'] = $this->stripe_promax_id;
                $update_data['max_tokens'] = '99999999';
                $users = "10";
                $update_data['sub_text']  = 'Up to 10 users';
                $enterprise_max_pricing_description2 = $this->getMaxPricingDescription2($users, "120");
                $update_data['display_txt2_1'] = $enterprise_max_pricing_description2;
            } else {

                $update_data['plan_description'] = $pro_data;

                $update_data['max_tokens'] = '1000000';
                if ($key == 64) {
                    $update_data['plan_description'] = $this->pro_pricing_description;;
                } else if ($key == 86) {
                    $update_data['plan_description'] = $pro_no_chatpdf_description;
                }
                if ($key!=151){
                    $update_data['stripe_plan_id'] = $this->stripe_pro_id;
                }   
            }


            $locales = $this->getLocales($key);
            if ($locales!=''){
                $update_data['locales'] = $locales;
            }
            
            if (count($update_data) >0 ){
                $builder->where('plan_id', $key)->update($update_data);
            }
        }

        /*
        *
         END Update Plan Data 
        *
        */

        /**
         * UPDATE SecureCardTransaction Plan ID 
         * **/
        $this->updateSCTPlan();
    }
}
