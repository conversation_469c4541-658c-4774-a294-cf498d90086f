import classNames from 'classnames';
import imgAIPROHeader from '../assets/images/AIPRO-200x45-1.svg';
import { getCurrentDomain, getCurrentProtocol } from './functions';

export default function Header() {
  const handleGetStarted = () => {
    const curDom = getCurrentDomain();
    const pricingDom = getCurrentProtocol() + '//' + (curDom.includes('staging') && !curDom.includes('localhost') ? 'staging.' : '') + 'start.ai-pro.org';

    window.location.href = `${pricingDom}/register/?ppg=46&kt8typtb=arcana`;
  };

  return (
    <header className={classNames(`sticky top-0 z-50 w-full border-b bg-white h-[110px] items-center`)}>
        <div className={classNames(`flex items-center h-full mx-auto max-w-[1440px]`)}>    
            <div className={classNames(`container px-4 md:px-6 max-w-[1440px]`)}>
                <div className={classNames(`mx-auto max-w-[1280px] flex h-16 items-center justify-between`)}>
                <div className={classNames(`flex items-center gap-2`)}>
                    <img src={imgAIPROHeader} alt="AI-PRO Logo" width={176} height={48} />
                </div>
                <div className={classNames(`flex justify-end" style={{ width: "calc(50% - 24px)`)}>
                    <button className={classNames(`gradient-button h-10 w-[120px] max-w-[268px] rounded-md text-white font-semibold`)} onClick={handleGetStarted}>
                        Get Started
                    </button>
                </div>
                </div>
            </div>
        </div>
    </header>
  )
}
