  <script nonce="<?= $nonce ?>" async="" src="https://www.clarity.ms/s/0.8.9/clarity.js"></script>
  <script nonce="<?= $nonce ?>" type="text/javascript" id="www-widgetapi-script" src="https://www.youtube.com/s/player/b2858d36/www-widgetapi.vflset/www-widgetapi.js" async=""></script>
  <script nonce="<?= $nonce ?>" async="" src="https://www.clarity.ms/tag/uet/355013816"></script><script src="https://js.hscollectedforms.net/collectedforms.js" type="text/javascript" id="CollectedForms-44168812" crossorigin="anonymous" data-leadin-portal-id="44168812" data-leadin-env="prod" data-loader="hs-scriptloader" data-hsjs-portal="44168812" data-hsjs-env="prod" data-hsjs-hublet="na1"></script>
  <script nonce="<?= $nonce ?>" src="https://js.hs-banner.com/v2/44168812/banner.js" type="text/javascript" id="cookieBanner-44168812" data-cookieconsent="ignore" data-hs-ignore="true" data-loader="hs-scriptloader" data-hsjs-portal="44168812" data-hsjs-env="prod" data-hsjs-hublet="na1"></script>
  <script nonce="<?= $nonce ?>" src="https://js.hs-analytics.net/analytics/1747356900000/44168812.js" type="text/javascript" id="hs-analytics"></script>
  <script nonce="<?= $nonce ?>" src="https://js.hubspot.com/web-interactives-embed.js" type="text/javascript" id="hubspot-web-interactives-loader" crossorigin="anonymous" data-loader="hs-scriptloader" data-hsjs-portal="44168812" data-hsjs-env="prod" data-hsjs-hublet="na1"></script>

<?php /* // this is not compatible with cms
  <script nonce="<?= $nonce ?>" src="https://www.youtube.com/iframe_api"></script>
*/ ?>
  

    <?php 
        $request_uri = $_SERVER['REQUEST_URI']; 
        $arr_link = explode('/', trim($request_uri, '/')); 
        
        $slug1 = isset($arr_link[0]) ? $arr_link[0] : '';
        $slug2 = isset($arr_link[1]) ? $arr_link[1] : '';

        //if (strpos($slug1, 'learn-ai') === true) {
            echo view('cms/analytics_global_settings.php');
        //}
    ?>


	<script nonce="<?= $nonce ?>">
    function ctx() {
      var canvas = document.createElement('canvas');
      var ctx = canvas.getContext('2d');
      var txt = 'ai-pro.org';
      ctx.textBaseline = "top";
      ctx.font = "16px 'Arial'";
      ctx.textBaseline = "alphabetic";
      ctx.rotate(.05);
      ctx.fillStyle = "#f60";
      ctx.fillRect(125,1,62,20);
      ctx.fillStyle = "#069";
      ctx.fillText(txt, 2, 15);
      ctx.fillStyle = "rgba(102, 200, 0, 0.7)";
      ctx.fillText(txt, 4, 17);
      ctx.shadowBlur=10;
      ctx.shadowColor="blue";
      ctx.fillRect(-20,10,234,5);
      var strng=canvas.toDataURL();

      var hash=0;
      if (strng.length==0) return 'nothing!';
      for (i = 0; i < strng.length; i++) {
        char = strng.charCodeAt(i);
        hash = ((hash<<5)-hash)+char;
        hash = hash & hash;
      }

      return hash;
    }
    function tz() {
      var now = new Date();
      var offsetMinutes = now.getTimezoneOffset();
      var offsetHours = Math.abs(offsetMinutes / 60);
      var offsetMinutesPart = Math.abs(offsetMinutes % 60);
      var sign = offsetMinutes > 0 ? "-" : "+";
      var timezoneOffset = sign + ("00" + offsetHours).slice(-2) + ":" + ("00" + offsetMinutesPart).slice(-2);

      return timezoneOffset;
    }
    document.cookie="__ctx__="+ctx()+";domain=.ai-pro.org;path=/";
    document.cookie="user_tz="+tz()+";domain=.ai-pro.org;path=/";
</script>

<!---Mixpanel Start Header----->

    <script nonce="<?= $nonce ?>">
        (function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");
        for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?
        MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\/\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);

        mixpanel.init('510eae1e2d2a79bceee18c49bece1c6a', { debug: true });
    </script>

<!---Mixpanel End----->

<script nonce="<?= $nonce ?>">
    let mixpanel_params = {
    'keyword': "<?php echo isset($_COOKIE['keyword']) ? $_COOKIE['keyword'] : '' ?>",
    'emailid': "<?php echo isset($_COOKIE['emailid']) ? $_COOKIE['emailid'] : '' ?>",
    'adid': "<?php echo isset($_COOKIE['adid']) ? $_COOKIE['adid'] : '' ?>",
    'ppg': "<?php echo isset($_COOKIE['ppg']) ? $_COOKIE['ppg'] : '' ?>",
    'pmt': "<?php echo isset($_COOKIE['pmt']) ? $_COOKIE['pmt'] : '' ?>",
    'howdoiplantouse': "<?php echo isset($_COOKIE['howdoiplantouse']) ? $_COOKIE['howdoiplantouse'] : '' ?>",
    'remakemedloption': "<?php echo isset($_COOKIE['remakemedloption']) ? $_COOKIE['remakemedloption'] : '' ?>",
    '$email': "<?php echo isset($_COOKIE['user_email']) ? $_COOKIE['user_email'] : '' ?>",
    'theme': "<?php echo isset($_COOKIE['theme']) ? $_COOKIE['theme'] : '' ?>",
    'locales': "<?php echo isset($_COOKIE['locales']) ? $_COOKIE['locales'] : '' ?>"
    }
</script>

<script nonce="<?= $nonce ?>">
    (function waitForMixpanel() {
        if (typeof mixpanel !== "undefined" && typeof mixpanel.track === "function") {
            console.log("Mixpanel ready, tracking event...");
            mixpanel.track("<?= $url ?? $slug1; ?>", mixpanel_params);
        } else {
            setTimeout(waitForMixpanel, 100); // check every 100ms
        }
    })();
</script>

<!-- Start of HubSpot Embed Code -->
    <script nonce="<?= $nonce ?>" type="text/javascript" id="hs-script-loader" async defer src="//js-na1.hs-scripts.com/44168812.js"></script>
<!-- End of HubSpot Embed Code -->

	<!-- -OutBrain Start----->
			<!-- <script data-obct type = "text/javascript">
				/** DO NOT MODIFY THIS CODE**/
				!function(_window, _document) {
				var OB_ADV_ID = '00faf51ccaf65fdd9b88552445558ea957';
				if (_window.obApi) {
					var toArray = function(object) {
					return Object.prototype.toString.call(object) === '[object Array]' ? object : [object];
					};
					_window.obApi.marketerId = toArray(_window.obApi.marketerId).concat(toArray(OB_ADV_ID));
					return;
				}
				var api = _window.obApi = function() {
					api.dispatch ? api.dispatch.apply(api, arguments) : api.queue.push(arguments);
				};
				api.version = '1.1';
				api.loaded = true;
				api.marketerId = OB_ADV_ID;
				api.queue = [];
				var tag = _document.createElement('script');
				tag.async = true;
				tag.src = '//amplify.outbrain.com/cp/obtp.js';
				tag.type = 'text/javascript';
				var script = _document.getElementsByTagName('script')[0];
				script.parentNode.insertBefore(tag, script);
				}(window, document);

				obApi('track', 'PAGE_VIEW');
			</script> -->
		<!---OutBrain End--- -->

<!---Bing Start----->
<script nonce="<?= $nonce ?>">
    (function(w,d,t,r,u)
    {
        var f,n,i;
        w[u]=w[u]||[],f=function()
        {
            var o={ti:"355013816", enableAutoSpaTracking: true};
            o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")
        },
        n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
        {
            var s=this.readyState;
            s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)
        },
        i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
    })
    (window,document,"script","//bat.bing.com/bat.js","uetq");
</script>
<!---Bing End----->

<!-- Start NB Pixel Snippet -->
	<!-- <script>
		!(function (e, n, t, i, p, a, s) {
		e[i] ||
			(((p = e[i] =
			function () {
				p.process ? p.process.apply(p, arguments) : p.queue.push(arguments);
			}).queue = []),
			(p.t = +new Date()),
			((a = n.createElement(t)).async = 1),
			(a.src = 'https://static.newsbreak.com/business/tracking/nbpixel.js?t=' + 864e5 * Math.ceil(new Date() / 864e5)),
			(s = n.getElementsByTagName(t)[0]).parentNode.insertBefore(a, s));
		})(window, document, 'script', 'nbpix'),
		nbpix('init', 'ID-1638589671595171841'),
		nbpix('event', 'pageload');
	</script> -->
<!-- End NB Pixel Snippet -->

<!-- VUZO logo -->
	<script nonce="<?= $nonce ?>">
		let ver = '012839';
		let vuzoScript = document.createElement("script");
		var start_URL = "https://start.ai-pro.org/";
		vuzoScript.setAttribute("src", start_URL + 'snippets/com.global.vuzo/js/com.global.vuzo.js?ver=' + ver);
		document.head.appendChild(vuzoScript);
	</script>
<!-- VUZO logo -->
<!-- Quora Pixel Code (JS Helper) -->
<script nonce="<?= $nonce ?>">
!function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
qp('init', '96952f49aa584b478f10b3bce056e08c');
qp('track', 'ViewContent');
</script>
<noscript><img height="1" width="1" style="display:none" src="https://q.quora.com/_/ad/96952f49aa584b478f10b3bce056e08c/pixel?tag=ViewContent&noscript=1"/></noscript>
<!-- End of Quora Pixel Code -->

	<!-- End of LP TRACKING CONDITION -->