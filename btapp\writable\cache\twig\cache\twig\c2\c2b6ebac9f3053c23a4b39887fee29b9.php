<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* includes/head_mixpanel.twig */
class __TwigTemplate_f3a694ced1ac5a5d20f9288c4e87d844 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if ((twig_length_filter($this->env, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 1)) > 0)) {
            // line 2
            echo "

<!--Start Mix_panel code -->
<script>
  (function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(\".\");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;\"undefined\"!==typeof c?a=b[c]=[]:c=\"mixpanel\";a.people=a.people||[];a.toString=function(a){var d=\"mixpanel\";\"mixpanel\"!==c&&(d+=\".\"+c);a||(d+=\" (stub)\");return d};a.people.toString=function(){return a.toString(1)+\".people (stub)\"};i=\"disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove\".split(\" \");
  for(h=0;h<i.length;h++)g(a,i[h]);var j=\"set set_once union unset remove delete\".split(\" \");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=[\"get_group\"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement(\"script\");e.type=\"text/javascript\";e.async=!0;e.src=\"undefined\"!==typeof MIXPANEL_CUSTOM_LIB_URL?
  MIXPANEL_CUSTOM_LIB_URL:\"file:\"===f.location.protocol&&\"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js\".match(/^\\/\\//)?\"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js\":\"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js\";g=f.getElementsByTagName(\"script\")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);
  
  mixpanel.init('510eae1e2d2a79bceee18c49bece1c6a', {debug: ";
            // line 10
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 10), "debug", [], "any", false, false, false, 10), "html", null, true);
            echo "});

";
            // line 12
            if ( !twig_test_empty(twig_trim_filter(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 12), "email", [], "any", false, false, false, 12)))) {
                // line 13
                echo "  ";
                // line 14
                echo "  mixpanel.people.set_once({ \"\$email\": \"";
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 14), "email", [], "any", false, false, false, 14), "html", null, true);
                echo "\" });
  mixpanel.identify(\"";
                // line 15
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 15), "email", [], "any", false, false, false, 15), "html", null, true);
                echo "\");
";
            }
            // line 17
            echo "
  mixpanel.track(\"";
            // line 18
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 18), "mixpanel_name", [], "any", false, false, false, 18), "html", null, true);
            echo "\", {
  'keyword': \"";
            // line 19
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 19), "keyword", [], "any", false, false, false, 19), "html", null, true);
            echo "\",
  'emailid': \"";
            // line 20
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 20), "emailid", [], "any", false, false, false, 20), "html", null, true);
            echo "\",
  'adid': \"";
            // line 21
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 21), "adid", [], "any", false, false, false, 21), "html", null, true);
            echo "\",
  'ppg': \"";
            // line 22
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 22), "ppg", [], "any", false, false, false, 22), "html", null, true);
            echo "\",
  'pmt': \"";
            // line 23
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 23), "pmt", [], "any", false, false, false, 23), "html", null, true);
            echo "\",
  '\$email': \"";
            // line 24
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 24), "email", [], "any", false, false, false, 24), "html", null, true);
            echo "\",
  '\$locales': \"";
            // line 25
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 25), "locales", [], "any", false, false, false, 25), "html", null, true);
            echo "\",
  'howdoiplantouse': \"";
            // line 26
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 26), "howdoiplantouse", [], "any", false, false, false, 26), "html", null, true);
            echo "\",
  'remakemedloption': \"";
            // line 27
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 27), "remakemedloption", [], "any", false, false, false, 27), "html", null, true);
            echo "\",
  });
  
";
            // line 30
            if ( !twig_test_empty(twig_trim_filter(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 30), "user_plan", [], "any", false, false, false, 30)))) {
                // line 31
                echo "  mixpanel.people.set_once({ \"Plan\": '";
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 31), "user_plan", [], "any", false, false, false, 31), "html", null, true);
                echo "' }); 
";
            }
            // line 33
            if ( !twig_test_empty(twig_trim_filter(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 33), "rewardful_via", [], "any", false, false, false, 33)))) {
                // line 34
                echo "  mixpanel.people.set_once({ \"Rewardful_Via\": '";
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 34), "rewardful_via", [], "any", false, false, false, 34), "html", null, true);
                echo "' }); 
";
            }
            // line 36
            if ( !twig_test_empty(twig_trim_filter(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 36), "currency", [], "any", false, false, false, 36)))) {
                // line 37
                echo "  mixpanel.people.set_once({ \"Currency\": '";
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 37), "currency", [], "any", false, false, false, 37), "html", null, true);
                echo "' }); 
";
            }
            // line 39
            if ( !twig_test_empty(twig_trim_filter(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 39), "amount", [], "any", false, false, false, 39)))) {
                // line 40
                echo "  mixpanel.people.set_once({ \"Amount\": '";
                echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 40), "amount", [], "any", false, false, false, 40), "html", null, true);
                echo "' }); 
";
            }
            // line 42
            echo "
</script>
<!--End Mix_panel code -->

";
        }
    }

    public function getTemplateName()
    {
        return "includes/head_mixpanel.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  145 => 42,  139 => 40,  137 => 39,  131 => 37,  129 => 36,  123 => 34,  121 => 33,  115 => 31,  113 => 30,  107 => 27,  103 => 26,  99 => 25,  95 => 24,  91 => 23,  87 => 22,  83 => 21,  79 => 20,  75 => 19,  71 => 18,  68 => 17,  63 => 15,  58 => 14,  56 => 13,  54 => 12,  49 => 10,  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "includes/head_mixpanel.twig", "C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\twig\\includes\\head_mixpanel.twig");
    }
}
