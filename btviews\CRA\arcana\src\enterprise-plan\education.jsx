import gradHatImg from './images/grad-hat.png';
import bookImg from './images/book.png';
import peopleImg from './images/people.png';
import directoryImg from './images/directory.png';
import { getCurrentDomain, getCurrentProtocol } from './functions';

export default function Education() {
    const handleGetEnterprise = () => {
        const curDom = getCurrentDomain();
        const pricingDom = getCurrentProtocol() + '//' + (curDom.includes('staging') && !curDom.includes('localhost') ? 'staging.' : '') + 'start.ai-pro.org';

        window.location.href = `${pricingDom}/pricing/?ppg=71&kt8typtb=arcana`;
    };

    return (
        <section className="py-12 md:py-16 lg:py-20">
            <div className="ep_container px-4 md:px-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <h2 className="font-bold max-w-640:text-[32px] md:text-[36px] max-w-360:text-[30px]">
                        <span className="text-[#1559ED]">AI-PRO</span> for Students & Academic Institutions
                    </h2>
                    <p className="max-w-[900px] text-gray-500 text-[16px]">
                        Discover how we've helped businesses and individuals achieve their goals. Hear directly from our
                        satisfied customers about their experiences and success stories.
                    </p>
                </div>
                <div className="flex flex-row max-w-640:flex-col justify-center gap-8 mx-auto py-16 max-w-640:py-[38px] max-w-640:px-[8px]">
                    <div className="flex flex-col md:w-[266px] md:h-[382px] max-w-640:h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px] md:gap-10">
                        <div className="flex items-center justify-center rounded-full">
                            <img src={gradHatImg} alt="Education " />
                        </div>
                        <div>
                            <div className="md:min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] leading-[32px] max-w-380:text-[19.4px] font-bold">AI-Powered Research Support</h3></div>
                            <div className="md:min-h-[144px]">
                                <p className="text-sm text-gray-500">
                                    Leverage advanced AI productivity tools to streamline literature reviews, summarize academic papers with an AI tool that will summarize PDFs, and spark innovative research ideas.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col md:w-[266px] md:h-[382px] max-w-640:h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px] md:gap-10">
                        <div className="flex items-center justify-center rounded-full">
                            <img src={bookImg} alt="Education about curriculum" />
                        </div>
                        <div>
                            <div className="md:min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] leading-[32px] max-w-380:text-[19.4px] font-bold">Curriculum Innovation</h3></div>
                            <div className="md:min-h-[144px]">
                                <p className="text-sm text-gray-500">
                                    Revolutionize learning by integrating generative AI in higher education. Design interactive lesson plans, develop dynamic content, and foster engaging learning experiences with powerful generative AI tools.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col md:w-[266px] md:h-[382px] max-w-640:h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px] md:gap-10">
                        <div className="flex items-center justify-center rounded-full">
                            <img src={peopleImg} alt="Education about people" />
                        </div>
                        <div>
                            <div className="md:min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] leading-[32px] max-w-380:text-[19.4px] font-bold">Student Support Tools</h3></div>
                            <div className="md:min-h-[144px]">
                                <p className="text-sm text-gray-500">
                                    Provide real-time tutoring, assignment guidance, and writing assistance using an AI chatbot for business tailored to academic environments.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col md:w-[266px] md:h-[382px] max-w-640:h-[280px] items-center text-center bg-white rounded-[13px] shadow-md py-[38px] px-[16px] md:gap-10">
                        <div className="flex items-center justify-center rounded-full">
                            <img src={directoryImg} alt="Education about directory" />
                        </div>
                        <div>
                            <div className="md:min-h-[48px] mb-2"><h3 className="text-[21px] md:text-[24px] leading-[32px] max-w-380:text-[19.4px] font-bold">Faculty Efficiency & Automation</h3></div>
                            <div className="md:min-h-[144px]">
                                <p className="text-sm text-gray-500">
                                    Save time on grading, documentation, and lesson prep using intelligent AI productivity tools for business and education workflows.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex justify-center pt-[20px]">
                    <button className="gradient-button px-6 py-3 rounded-md text-white text-[20px] font-semibold h-[53px]" onClick={handleGetEnterprise}>
                        Get All-In Enterprise Access
                    </button>
                </div>
            </div>
        </section>
    );
}