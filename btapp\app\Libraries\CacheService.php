<?php

namespace App\Libraries;

use App\Libraries\RedisClient;
use App\Libraries\RedisAemoClient;

class CacheService
{
    protected $client;
    protected $useAemo;
    protected $defaultTtl;
    protected $cachePath;

    public function __construct(bool $useAemo = false, int $ttl = 86400)
    {
        $this->useAemo = $useAemo;
        $this->defaultTtl = $ttl;
        $this->cachePath = WRITEPATH . 'cache/';

        try {
            $this->client = $useAemo
                ? (new RedisAemoClient())->getClient()
                : (new RedisClient())->getClient();

        } catch (\Throwable $e) {
            log_message('error', 'Redis init failed: ' . $e->getMessage());
            $this->client = null;
        }
    }

    protected function getFileCachePath(string $key): string
    {
        return $this->cachePath . $key . '.cache';
    }

    public function get(string $key, string $field = null)
    {
        if ($this->client) {
            try {
                $result = $field
                    ? $this->client->hget($key, $field)
                    : $this->client->get($key);

                return $result ? json_decode($result, true) : null;
            } catch (\Throwable $e) {
                log_message('error', 'Redis get failed: ' . $e->getMessage());
            }
        }

        $path = $this->getFileCachePath($key);
        if (file_exists($path) && time() - filemtime($path) < $this->defaultTtl) {
            return json_decode(file_get_contents($path), true);
        }

        return null;
    }

    public function set(string $key, $value, string $field = null, int $ttl = null)
    {
        $ttl = $ttl ?? $this->defaultTtl;

        if ($this->client) {
            try {
                $encoded = json_encode($value);
                if ($field) {
                    $this->client->hset($key, [$field => $encoded]);
                } else {
                    $this->client->set($key, $encoded);
                } 
                $this->client->expire($key, $ttl);
                return true;
            } catch (\Throwable $e) {
                log_message('error', 'Redis set failed: ' . $e->getMessage());
            }
        }

        try {
            return file_put_contents($this->getFileCachePath($key), json_encode($value)) !== false;
        } catch (\Throwable $e) {
            log_message('error', 'File cache set failed: ' . $e->getMessage());
            return false;
        }
    }
}
