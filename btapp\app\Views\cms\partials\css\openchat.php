<style nonce="<?= $nonce ?>">
    #chat-bubble-container {display:none !important; }
    .openchat-fixed {
        display: none !important;
        z-index: 99;
    }
    .openchat-fade-in-top .fade-in.openchat-w-full {
        background-color: #ffffff !important;
    }
    .openchat-fade-in-top .openchat-text-white.openchat-h-12 {
        background-color: #ffffff !important;
    }
    .openchat-fade-in-top .fade-in.openchat-w-full:hover {
        background-color: #cccccc !important;
    }
    .openchat-fade-in-top .openchat-text-white.openchat-h-12:hover {
        background-color: #cccccc !important;
    }
    .openchat-text-accent2 {
        background-color: #ffffff !important;
    }
    .openchat-w-full.openchat-flex.openchat-items-center.openchat-flex-col.openchat-text-sm.openchat-text-black {
        text-align: center !important;
    }
    .openchat-w-full.openchat-flex.openchat-items-start.openchat-gap-3:hover ~ .openchat-text-accent2 {
        background-color: #cccccc !important;
    }
    .close-btn .openchat-w-12 {
        background-color: inherit !important;
    }
    .openchat-text-accent2 {
        background-color: inherit !important;
    }
    .close-btn,
    .openchat-text-primary.fade-in {
        background-color: white !important;
        color: rgb(0 87 255) !important;
    }
    .close-btn:hover {
        background-color: black !important;
        color: rgb(0 87 255) !important;
        opacity: 20%;
    }
    .fade-in.openchat-px-2.openchat-py-1.openchat-font-bold.openchat-text-center {
        color: black !important;
    }

    @media (min-width: 690px) {
        .openchat-fixed {
            display:none !important; 
            margin-bottom: 3rem;
        }
        .chat-bubble {
            height: 75% !important;
        }
    }
</style>