import React, { useState, useRef, useEffect, lazy, Suspense } from "react"
import { FaChevronDown, FaTimes, FaBars } from "react-icons/fa"
import { motion, AnimatePresence } from "framer-motion"
import aiproLogo from '../../assets/images/AIPRO.svg';
import { Auth } from '../../core/utils/auth';

// Lazy load dropdown menus for better performance
const DesktopDropdownMenu = lazy(() => import("./ui-elements/desktop-dropdown-menu.jsx"))
const MobileDropdownMenu = lazy(() => import("./ui-elements/mobile-dropdown-menu.jsx"))

function Header() {
  const [showBanner, setShowBanner] = useState(true)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [activeSubmenu, setActiveSubmenu] = useState(null)
  const timeoutRef = useRef(null)
  const [isClient, setIsClient] = useState(false)
  const auth = Auth()

  useEffect(() => {
    setIsClient(true)
  }, [])


  // Handle submenu opening and closing with delay
  const handleMouseEnter = (menu) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setActiveSubmenu(menu)
  }

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setActiveSubmenu(null)
    }, 300) // 300ms delay before closing submenu
  }

  // Handle keyboard navigation
  const handleKeyDown = (e, menu) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault()
      setActiveSubmenu(activeSubmenu === menu ? null : menu)
    }
  }

  // Detect scroll for header styling
//   useEffect(() => {
//     const handleScroll = () => {
//       setScrolled(window.scrollY > 10)
//     }

//     window.addEventListener("scroll", handleScroll)
//     return () => {
//       window.removeEventListener("scroll", handleScroll)
//       if (timeoutRef.current) {
//         clearTimeout(timeoutRef.current)
//       }
//     }
//   }, [])

  // Animation variants
  const menuItemVariants = {
    hidden: { opacity: 0, y: -5 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
      },
    },
    exit: {
      opacity: 0,
      y: -5,
      transition: {
        duration: 0.2,
      },
    },
  }

  // Menu data for reuse
  const chatbotsMenuItems = [
    { name: "ChatBot Pro", url: "https://ai-pro.org/ai-chatbots/chatbot-pro/" },
    { name: "ChatPDF", url: "https://ai-pro.org/ai-chatbots/pdf-reader/" },
    { name: "Grammar AI", url: "https://ai-pro.org/ai-chatbots/grammar-ai/" },
    { name: "Teacher AI", url: "https://ai-pro.org/ai-chatbots/homework-helper/" },
    { name: "Trips AI", url: "https://ai-pro.org/ai-chatbots/trip-planner/" },
    { name: "AI Recipe Maker", url: "https://ai-pro.org/ai-chatbots/recipe-maker/" },
    { name: "Translate Now", url: "https://ai-pro.org/ai-chatbots/translator/" },
    { name: "Multi-Chat", url: "https://ai-pro.org/ai-chatbots/multiple-ai-response-generator/" },
    { name: "Search AI", url: "https://ai-pro.org/ai-chatbots/search-tool/" },
    { name: "AI Toolbox", url: "https://ai-pro.org/ai-chatbots/toolbox/" },
  ]

  const artMenuItems = [
    { name: "DreamPhoto", url: "https://ai-pro.org/ai-art/image-generator/" },
    { name: "Remove Background", url: "https://ai-pro.org/ai-art/remove-background/" },
    { name: "AI Avatar Maker", url: "https://ai-pro.org/ai-art/avatar-maker/" },
    { name: "Interior AI", url: "https://ai-pro.org/ai-art/interior-design/" },
    { name: "Storybook AI", url: "https://ai-pro.org/ai-art/story-book-generator/" },
    { name: "Restore Photo", url: "https://ai-pro.org/ai-art/photo-restoration/" },
    { name: "Flux ImageGen", url: "https://ai-pro.org/ai-art/flux-image-generation/" },
    { name: "Image Prompt Gallery", url: "https://ai-pro.org/ai-art/image-prompt-gallery/" },
  ]

  const learnMenuItems = [
    { name: "Articles", url: "https://ai-pro.org/learn-ai/articles/" },
    { name: "Premium Tutorials", url: "https://ai-pro.org/learn-ai/tutorials/" },
    { name: "AI Course", url: "https://ai-pro.org/learn-ai/course/" },
    { name: "Learn ChatGPT", url: "https://ai-pro.org/learn-ai/tag/chatgpt/" },
    { name: "Learn AI Image", url: "https://ai-pro.org/learn-ai/tag/ai-image/" },
  ]

  const mainMenuItems = [
    { name: "Enterprise", url: "https://ai-pro.org/enterprise-plan/" },
    { name: "About Us", url: "https://ai-pro.org/about-us/" },
    { name: "Contact Us", url: "https://ai-pro.org/contact-us/" },
    { name: "Pricing", url: "https://ai-pro.org/pricing-redirect" },
    ...(auth ? [
      { name: "My Apps", url: "https://start.ai-pro.org/my-account" },
      { name: "Manage Account", url: "https://start.ai-pro.org/manage-account" }
    ] : [
      { name: "Login", url: "#", onClick: "https://start.ai-pro.org/login" }
    ])
  ]

  return (
    <header className="z-50">
      {showBanner && (
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center py-1 px-4 relative">
          <div className="container mx-auto">
            <p className="text-xs">
              We are not affiliated nor related to OpenAI.{" "}
              <a
                href="https://ai-pro.org/disclaimer/"
                className="underline hover:text-blue-100 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                Learn More
              </a>
            </p>
            <button
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-blue-100 transition-colors"
              onClick={() => setShowBanner(false)}
              aria-label="Close banner"
            >
              <FaTimes className="h-3 w-3" />
            </button>
          </div>
        </div>
      )}

      <div className={`bg-white border-b shadow-md transition-all duration-300`}>
        <div className="container mx-auto flex items-center justify-between h-[7rem] px-4 max-w-[1280px]">
          <a href="https://ai-pro.org/" className="flex items-center" target="_blank" rel="noopener noreferrer">
            <div className="flex items-center gap-2">
              {isClient && (
                <img
                  src={aiproLogo}
                  alt="AI-PRO Logo"
                  width={200}
                  height={67}
                  className="h-16 w-auto"
                />
              )}
            </div>
          </a>

          <nav className="hidden lg:flex items-center space-x-6" aria-label="Main navigation">
            {/* AI Chatbots Dropdown */}
            <div className="relative" onMouseEnter={() => handleMouseEnter("chatbots")} onMouseLeave={handleMouseLeave}>
              <button
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium text-lg transition-colors"
                aria-expanded={activeSubmenu === "chatbots"}
                aria-haspopup="true"
                onKeyDown={(e) => handleKeyDown(e, "chatbots")}
              >
                AI Chatbots
                <FaChevronDown
                  className={`ml-1 h-3 w-3 transition-transform duration-300 ${activeSubmenu === "chatbots" ? "rotate-180" : ""}`}
                  aria-hidden="true"
                />
              </button>
              {isClient && (
                <Suspense fallback={null}>
                  {activeSubmenu === "chatbots" && (
                    <DesktopDropdownMenu items={chatbotsMenuItems} menuItemVariants={menuItemVariants} />
                  )}
                </Suspense>
              )}
            </div>

            {/* AI Art Dropdown */}
            <div className="relative" onMouseEnter={() => handleMouseEnter("art")} onMouseLeave={handleMouseLeave}>
              <button
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium text-lg transition-colors"
                aria-expanded={activeSubmenu === "art"}
                aria-haspopup="true"
                onKeyDown={(e) => handleKeyDown(e, "art")}
              >
                AI Art
                <FaChevronDown
                  className={`ml-1 h-3 w-3 transition-transform duration-300 ${activeSubmenu === "art" ? "rotate-180" : ""}`}
                  aria-hidden="true"
                />
              </button>
              {isClient && (
                <Suspense fallback={null}>
                  {activeSubmenu === "art" && (
                    <DesktopDropdownMenu items={artMenuItems} menuItemVariants={menuItemVariants} />
                  )}
                </Suspense>
              )}
            </div>

            {/* Learn AI Dropdown */}
            <div className="relative" onMouseEnter={() => handleMouseEnter("learn")} onMouseLeave={handleMouseLeave}>
              <button
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium text-lg transition-colors"
                aria-expanded={activeSubmenu === "learn"}
                aria-haspopup="true"
                onKeyDown={(e) => handleKeyDown(e, "learn")}
              >
                Learn AI
                <FaChevronDown
                  className={`ml-1 h-3 w-3 transition-transform duration-300 ${activeSubmenu === "learn" ? "rotate-180" : ""}`}
                  aria-hidden="true"
                />
              </button>
              {isClient && (
                <Suspense fallback={null}>
                  {activeSubmenu === "learn" && (
                    <DesktopDropdownMenu items={learnMenuItems} menuItemVariants={menuItemVariants} />
                  )}
                </Suspense>
              )}
            </div>

            {/* Regular menu items */}
            {mainMenuItems.map((item) => (
              <a
                key={item.name}
                href={item.url}
                className="text-gray-700 hover:text-blue-600 font-medium text-lg transition-colors relative group"
                target={item.onClick ? undefined : "_blank"}
                rel={item.onClick ? undefined : "noopener noreferrer"}
                onClick={
                  item.onClick
                    ? (e) => {
                        e.preventDefault()
                        item.onClick?.()
                      }
                    : undefined
                }
              >
                {item.name}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 group-hover:w-full transition-all duration-300"></span>
              </a>
            ))}
          </nav>

          {/* Mobile menu button */}
          <button
            className="lg:hidden text-gray-700 hover:text-blue-600 transition-colors"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
            aria-expanded={mobileMenuOpen}
            aria-controls="mobile-menu"
          >
            {mobileMenuOpen ? (
              <FaTimes className="h-6 w-6" aria-hidden="true" />
            ) : (
              <FaBars className="h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Mobile menu */}
        <AnimatePresence>
          {mobileMenuOpen && isClient && (
            <motion.div
              className="lg:hidden bg-white border-t"
              id="mobile-menu"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="container mx-auto px-4 py-3">
                <div className="space-y-3">
                  <Suspense fallback={<div className="h-10 bg-gray-100 animate-pulse rounded"></div>}>
                    <MobileDropdownMenu
                      title="AI Chatbots"
                      items={chatbotsMenuItems}
                      isActive={activeSubmenu === "mobile-chatbots"}
                      toggleMenu={() =>
                        setActiveSubmenu(activeSubmenu === "mobile-chatbots" ? null : "mobile-chatbots")
                      }
                    />
                  </Suspense>

                  <Suspense fallback={<div className="h-10 bg-gray-100 animate-pulse rounded"></div>}>
                    <MobileDropdownMenu
                      title="AI Art"
                      items={artMenuItems}
                      isActive={activeSubmenu === "mobile-art"}
                      toggleMenu={() => setActiveSubmenu(activeSubmenu === "mobile-art" ? null : "mobile-art")}
                    />
                  </Suspense>

                  <Suspense fallback={<div className="h-10 bg-gray-100 animate-pulse rounded"></div>}>
                    <MobileDropdownMenu
                      title="Learn AI"
                      items={learnMenuItems}
                      isActive={activeSubmenu === "mobile-learn"}
                      toggleMenu={() => setActiveSubmenu(activeSubmenu === "mobile-learn" ? null : "mobile-learn")}
                    />
                  </Suspense>

                  {mainMenuItems.map((item) => (
                    <a
                      key={item.name}
                      href={item.url}
                      className="block py-2 text-gray-700 hover:text-blue-600 font-medium transition-colors"
                      target={item.onClick ? undefined : "_blank"}
                      rel={item.onClick ? undefined : "noopener noreferrer"}
                      onClick={
                        item.onClick
                          ? (e) => {
                              e.preventDefault()
                              item.onClick?.()
                            }
                          : undefined
                      }
                    >
                      {item.name}
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  )
}

export default Header;