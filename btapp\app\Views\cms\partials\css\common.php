<style nonce="<?= $nonce ?>">
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
    }

    .header-background {
        position: relative;
        padding: 100px 30px;
        color: white;
        text-align: center;
        background-attachment: fixed;
        max-width: 100% !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
        width: 100% !important;
    }

    .header-background::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url(https://assets.ai-pro.org/assets/wp-content/uploads/2020/05/pattern.jpg) repeat center center;
        filter: brightness(60%);
        z-index: 1;
    }

    .header {
        position: relative;
        z-index: 2;
    }

    .header h1 {
        font-size: 60px;
        margin: 0;
        color: white;
        margin-bottom: 20px;
        font-weight: 800;
    }
    .header p {
        font-size: 16px;
        max-width: 700px;
        margin: 0 auto;
    }
    .container {
        display: flex;
        padding: 4rem 2rem;
        gap: 7rem;
        max-width: 1200px;
        margin: 0 auto;
    }
    .telecom_widget {
        display: -webkit-box;
        width: 50%;
    }
    .telecom {
        display: block;
    }
    .iframe_widget {
        margin-top: auto;
        margin-bottom: auto;
    }
    .contact-info,
    .form-section {
        width: 45%;
    }
    .contact-info h2 {
        margin-bottom: 20px;
    }
    .contact-info p {
        margin-bottom: 0px !important;
        font-size: 16px;
    }
    .form-section form {
        display: flex;
        flex-direction: column;
    }
    .message {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    .success {
        background: #d4edda;
        color: #155724;
    }
    .error {
        background: #f8d7da;
        color: #721c24;
        font-size: 14.5px;
    }
    .error * {
        color: red;
    }
    .error-message {
        font-size: 14px;
    }
    .concern_tell {
        font-weight: 700;
        margin-bottom: 10px;
        margin-top: 1rem;
    }
    .cta-sbmt {
        padding-left: 30px;
        padding-right: 30px;
        margin-right: auto;
    }
    label[for="email"] {
        margin-top: 1rem;
    }
    input,
    textarea {
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }
    textarea {
        margin-bottom: 0.5rem;
    }
    button {
        background: #007bff;
        color: white;
        padding: 10px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }
    button:hover {
        background: #0056b3;
    }
    hr.solid {
        border-top: 2px solid #bbb;
        margin-bottom: 6px;
    }
    input.error + .required-asterisk,
    textarea.error + .required-asterisk {
        color: red;
    }

    .name_email {
        margin-bottom: 10px;
        font-weight: 700;
        cursor: default;
    }
    .toast-center {
        top: 10% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
    @media only screen and (max-width: 768px) {
        .header h1 {
            font-size: 48px;
        }
    }
    @media only screen and (max-width: 768px) {
        .container {
            flex-direction: column;
        }
        .contact-info,
        .form-section {
            width: 100%;
        }
    }
    @media only screen and (max-width: 768px) {
        .container .contact-info p {
            font-size: 16px;
        }
    }
    @media only screen and (max-width: 768px) {
        .container {
            gap: 3rem;
        }
    }
    @media only screen and (max-width: 768px) {
        .telecom {
            height: auto;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 890px) {
        .telecom_widget {
            display: inline;
        }
    }
    @media only screen and (min-width: 768px) and (max-width: 890px) {
        .telecom {
            width: 65%;
        }
    }

    @media only screen and (min-width: 320px) and (max-width: 450px) {
        .telecom_widget {
            display: inline;
        }
        .telecom {
            width: 65%;
        }
    }
    @media only screen and (min-width: 890px) {
        .iframe_widget {
            margin-top: 75px;
        }
    }
    @media only screen and (min-width: 890px) {
        .contact-info p {
            font-size: 16px;
        }
    }
</style>