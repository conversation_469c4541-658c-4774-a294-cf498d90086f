<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JWT Token Generator</title>
</head>
<body>
  <h1>JWT Token Generator</h1>
  <form id="jwtForm">
    <label for="privateKey">Select Private Key File (e.g., filename.p8):</label>
    <input type="file" id="privateKey" accept=".p8" required><br><br>

    <label for="teamId">Team ID (sample: '6285BF2XSD'):</label>
    <input type="text" id="teamId" required><br><br>

    <label for="clientId">Client ID (sample: 'org.chatbot-now'):</label>
    <input type="text" id="clientId" required><br><br>

    <label for="keyId">Key ID (sample: '8G2FYKD668'):</label>
    <input type="text" id="keyId" required><br><br>

    <button type="submit">Generate Token</button>
  </form>
  <textarea id="clientSecret" rows="5" cols="50" readonly></textarea><br><br>

  <script src="jsrsasign-all-min.js"></script>
  <script>
    document.getElementById('jwtForm').addEventListener('submit', async function(event) {
      event.preventDefault();

      const privateKeyFile = document.getElementById('privateKey').files[0];
      const teamId = document.getElementById('teamId').value;
      const clientId = document.getElementById('clientId').value;
      const keyId = document.getElementById('keyId').value;

      try {
        const privateKey = await readPrivateKey(privateKeyFile);

        const timestampNow = Math.floor(Date.now() / 1000);
        const validityMonths = 6;
        const timestampExp = timestampNow + (60 * 60 * 24 * 30 * validityMonths);

        const payload = {
          iss: teamId,
          iat: timestampNow,
          exp: timestampExp,
          aud: 'https://appleid.apple.com',
          sub: clientId
        };

        const token = createJWT(payload, privateKey, keyId);

        document.getElementById('clientSecret').value = token;
      } catch (error) {
        console.error('Error:', error);
        document.getElementById('clientSecret').value = 'Error generating token: ' + error.message;
      }
    });

    async function readPrivateKey(privateKeyFile) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          resolve(event.target.result);
        };
        reader.onerror = (error) => {
          reject(error);
        };
        reader.readAsText(privateKeyFile);
      });
    }

    function createJWT(payload, privateKey, keyId) {
      const header = { alg: 'ES256', kid: keyId };
      const sClaim = JSON.stringify(payload);
      const prvKey = KEYUTIL.getKey(privateKey);
      const jwt = KJUR.jws.JWS.sign(header.alg, header, sClaim, prvKey);
      return jwt;
    }
  </script>
</body>
</html>
