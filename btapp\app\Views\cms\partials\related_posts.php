				<?php if(count($recent_posts) > 0) { ?>
				<div class="ct-related-posts-container">
					<div class="ct-container">
						<div class="ct-related-posts">
							<h3 class="ct-block-title">Recent Posts</h3>
							<!-- <div class="ct-related-posts-items" data-layout="grid"> -->
							<!-- RECENT POST HERE -->
							<!-- <article itemscope="itemscope" itemtype="https://schema.org/CreativeWork">
								<a class="ct-image-container" href="/learn-ai/articles/the-ultimate-guide-to-ai-tools-for-boosting-productivity" aria-label="The Ultimate Guide to AI Tools for Boosting Productivity" tabindex="-1"><img width="300" height="200" src="https://assets.ai-pro.org/assets/wp-content/uploads/2025/05/artificial-intelligence-300x200.jpg" class="attachment-medium size-medium wp-post-image" alt="Artificial intelligence has embedded itself into our daily lives" decoding="async" loading="lazy" srcset="https://assets.ai-pro.org/assets/wp-content/uploads/2025/05/artificial-intelligence-300x200.jpg 300w, https://assets.ai-pro.org/assets/wp-content/uploads/2025/05/artificial-intelligence-768x512.jpg 768w, https://assets.ai-pro.org/assets/wp-content/uploads/2025/05/artificial-intelligence.jpg 900w" sizes="(max-width: 300px) 100vw, 300px" itemprop="image" style="aspect-ratio: 16/9;"></a>
								<h4 class="related-entry-title" itemprop="name">
									<a href="/learn-ai/articles/the-ultimate-guide-to-ai-tools-for-boosting-productivity" itemprop="url" rel="bookmark">The Ultimate Guide to AI Tools for Boosting Productivity</a>
								</h4>					
								<ul class="entry-meta" data-type="simple:slash">
									<li class="meta-date" itemprop="datePublished">
										<time class="ct-meta-element-date" datetime="2025-05-09T07:12:10+00:00">May 9, 2025</time>
									</li>
									<li class="meta-categories" data-type="simple">
										<a href="/learn-ai/articles" rel="tag" class="ct-term-30">Articles</a>
									</li>
								</ul>
								</article> -->
							<!-- </div> -->

							<div class="ct-related-posts-items" data-layout="grid">
							    <?php foreach ($recent_posts as $post): ?>
									
							        <article itemscope itemtype="https://schema.org/CreativeWork">
							            <a class="ct-image-container"
							               href="<?= $post['canonical_url'] ?>"
							               aria-label="<?= esc($post['title']) ?>"
							               tabindex="-1">
							                <img
							                    width="300"
							                    height="200"
							                    src="<?= esc($post['image']) ?>"
							                    class="attachment-medium size-medium wp-post-image"
							                    alt="<?= esc($post['title']) ?>"
							                    decoding="async"
							                    loading="lazy"
							                    sizes="(max-width: 300px) 100vw, 300px"
							                    itemprop="image"
							                    style="aspect-ratio: 16/9"
							                />
							            </a>
							            <h4 class="related-entry-title" itemprop="name">
							                <a href="<?= $post['canonical_url'] ?>"
							                   itemprop="url"
							                   rel="bookmark">
							                   <?= esc($post['title']) ?>
							                </a>
							            </h4>
							            <ul class="entry-meta" data-type="simple:slash">
							                <li class="meta-date" itemprop="datePublished">
							                    <time class="ct-meta-element-date"
							                          datetime="<?= esc($post['date_create_gmt']) ?>">
							                          <?= esc($post['date_create']) ?>
							                    </time>
							                </li>
							                <li class="meta-categories" data-type="simple">
							                    <a href="." rel="tag" class="ct-term-30"><?= esc(ucfirst($post['category_slug'])) ?></a>
							                </li>
							            </ul>
							        </article>
							    <?php endforeach; ?>
							</div>

						</div>
					</div>
				</div>
				<?php } ?>