INFO - 2025-06-05 08:28:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-05 08:28:21 --> REQ ----------->
INFO - 2025-06-05 08:28:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-05 08:28:24 --> REQ ----------->
INFO - 2025-06-05 08:28:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-05 08:28:29 --> REQ ----------->test_dev_zapyvi@mailinator.comtest_dev_zapyvi@<EMAIL>
DEBUG - 2025-06-05 08:28:29 --> test_dev_zapyvi@mailinator.comtest_dev_zapyvi@<EMAIL>
DEBUG - 2025-06-05 08:28:29 --> btdbFindBy ---> 
DEBUG - 2025-06-05 08:28:30 --> btdbUpdate ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('9a88aa3fffeb0b91', '<EMAIL>', 'live', '$P$Bh9qxdaFvYfcs4iGvIwnPmerKerGxi.', '', '', '$2y$09$zcV072f47QokvzxUk8vhoOAv/taHQZS8rD05zSl6Ljp/R.7YDw7cG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"basilisk\"}', '2025-06-05 08:28:30', '2025-06-05 08:28:30')
DEBUG - 2025-06-05 08:28:30 --> btdbFindBy ---> INSERT INTO `start-user` (`user_pid`, `email`, `mode`, `password`, `first_name`, `last_name`, `login_token`, `ip_address`, `country`, `flags`, `created_at`, `updated_at`) VALUES ('9a88aa3fffeb0b91', '<EMAIL>', 'live', '$P$Bh9qxdaFvYfcs4iGvIwnPmerKerGxi.', '', '', '$2y$09$zcV072f47QokvzxUk8vhoOAv/taHQZS8rD05zSl6Ljp/R.7YDw7cG', '::1', '', '{\"aiwp_logged_in\":null,\"mode\":\"live\",\"ppg\":\"14\",\"pp_ctaclr\":\"1559ED\",\"pmt\":\"paydl\",\"vpay\":\"\",\"navmenu\":\"show\",\"splash\":\"off\",\"ailplogo\":\"off\",\"emailid\":\"\",\"theme_flag_name\":\"kt8typtb\",\"theme_flag_value\":\"basilisk\"}', '2025-06-05 08:28:30', '2025-06-05 08:28:30')
INFO - 2025-06-05 08:28:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-05 08:28:30 --> btdbFindBy ---> SELECT `start-plan`.*, `start-account`.*, DATE_FORMAT(resumed_at , "%M %e, %Y") as "resumed_at_formatted", `start-user`.`mode`, case when DATE_ADD(end_date, INTERVAL 1 DAY) < now() then 'yes' else 'no' end as 'expired'
FROM `start-account`
JOIN `start-user` ON `start-user`.`user_id` = `start-account`.`user_id`
JOIN `start-plan` ON `start-plan`.`plan_id` = `start-account`.`plan_id`
WHERE `start-account`.`user_id` = '116'
ORDER BY `start-account`.`end_date` DESC
DEBUG - 2025-06-05 08:28:30 --> btdbUpdate ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '9c6a49f3466d81c881a24dd4f81c9447', '2025-06-05 08:28:30', '2025-06-05 08:28:30')
DEBUG - 2025-06-05 08:28:30 --> btdbFindBy ---> INSERT INTO `start-user_app` (`email`, `userapp_pid`, `created_at`, `updated_at`) VALUES ('<EMAIL>', '9c6a49f3466d81c881a24dd4f81c9447', '2025-06-05 08:28:30', '2025-06-05 08:28:30')
INFO - 2025-06-05 08:28:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-05 08:28:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-05 08:28:31 --> REQ ----------->14
DEBUG - 2025-06-05 08:31:15 --> REQ ----------->14
DEBUG - 2025-06-05 08:31:49 --> REQ ----------->14
DEBUG - 2025-06-05 08:37:41 --> REQ ----------->14
