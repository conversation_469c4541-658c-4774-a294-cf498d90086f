<?php

use App\Models\UserLogModel;

/**
 * btLog
 *
 * @uses btenum_helper.php
 * @uses btdb_helper.php
 * @uses btflag_helper.php
 *
 */

function btlogUserActivity($_account_id, $log_data, $user)
{
    $desc = btutilReplaceKey('key_user_pid', $user->user_pid, $log_data['description']);

    $event_data =
        array_merge(
            array(
                "email" => $user->email,
                'activity' => $log_data['activity'],
                "description" => $desc,
            ),
            $log_data['event_data']
        );

    $event_data = sanitizeEventData($event_data);

    $userLogModel = new UserLogModel();
    $userLogModel->insert([
        "userlog_pid" => btutilGenerateHashId([$_account_id, $log_data['event']]),
        "user_pid" => $user->user_pid,
        "event" => $log_data["event"],
        "event_data" => json_encode($event_data)
    ]);
}


function getUserByPid($user_pid)
{
    $user = btdbFindBy('UserModel', 'user_pid', $user_pid);
    if ($user['success'] && $user['res']) return ['success' => 1, 'data' => $user['res'][0]];
    return ['success' => 0, 'data' => []];
}

// function getUserById($user_id)
// {
//     $user = btdbFindBy('UserModel', 'user_id', $user_id);
//     if ($user['success'] && $user['res']) return ['success' => 1, 'data' => $user['res'][0]];
//     return ['success' => 0, 'data' => []];
// }


function logUserActivity($event = "", $event_data = [])
{
    if ($event == "") {
        return;
    }

    $isAdmin = btflag('admin','0');
    if ($isAdmin=='1'){
        return;
    }

    $user_pid = "";

    if (btsessionIsUserLoggedIn()) {
        $user_pid = btsessionGet('USER')->user_pid;
    }

    if ($event_data && array_key_exists('user_pid', $event_data)) {
        $user_pid = $event_data['user_pid'];
    }

    // btutilDebug($event_data);

    if ($user_pid) {
        $user = getUserByPid($user_pid);

        if ($user['success']) {
            // log event
            $btEnum = btenum();
            if (isset($btEnum['user_log']) && array_key_exists($event, $btEnum['user_log'])) {
                $log_data = $btEnum['user_log'][$event];
                $log_data['event_data'] = $event_data;
                btlogUserActivity($user["data"]->user_id, $log_data,  $user["data"]);

                $fingerprint_data = getDeviceFingerprint();
                $df_data = [];
                $df_data['event'] = $event . '-fingerprint';
                $df_data['description'] = ucfirst($event) . " device fingerprint";
                $df_data['activity'] = 'Backend device fingerprint tracing.';
                $df_data['event_data'] = is_string($fingerprint_data) ? json_decode($fingerprint_data, true) : $fingerprint_data;
                btlogUserActivity($user["data"]->user_id, $df_data, $user["data"]);
            }
        }
    }
}

if (!function_exists('sanitizeEventData')) {
    function sanitizeEventData($event_data)
    {
        $keys_to_remove = [
            "password", "login_token", "user_id", "status", "ip_address", "mode",
            "website", "merchant_customer_id", "trial_end", "acctpmt_pid", "res",
            "account_id", "start_date", "end_date", "created_at", "updated_at",
            "cc_number", "user_createdAt", "user_updatedAt",
            "subscription_type", "subscription_start", "subscription_end", "subscription_endT",
        ];

        foreach ($event_data as $key => $value) {  // remove selected fields or remove empty fields
            if (in_array($key, $keys_to_remove) || empty($value) || $value == "0000-00-00 00:00:00") {
                unset($event_data[$key]);
            }
        }

        return $event_data;
    }
}