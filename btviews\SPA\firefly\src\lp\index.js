import React, { useState, useEffect, lazy, Suspense, useRef } from 'react';
import './index.css';

import bgPNG from "../assets/images/hero1.png";
import bgWebP from "../assets/images/hero1.png?as=webp";
import bgmobile_PNG from "../assets/images/hero.png";
import bgbgmobile_WebP from "../assets/images/hero.png?as=webp";
import { Loading } from "../loading";

const Bg = ({ srcSet, src, alt }) => {

    const [isMobile, setIsMobile] = useState(false);
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= 768);
        };

        handleResize();

        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);
    
    const bgSrcSet = isMobile ? bgbgmobile_WebP : srcSet;
    const bgSrc = isMobile ? bgmobile_PNG : src;

    return (
        <div className="inherit overflow-hidden">
            <Image
                srcSet={bgSrcSet}
                src={bgSrc}
                alt={alt}
                width="auto"
                height="auto"
                className="bgtop absolute top-[40px] left-0 md:top-1/2 md:left-1/2 md:transform md:-translate-x-[50%] md:-translate-y-[28%] lg:-translate-y-[32%]"
            />
        </div>
    );
};

const Image = lazy(() => import("./components/lazyimage"));
const Header = lazy(() => import("../header"));
const Footer = lazy(() => import("../footer"));
const BackToTopButton = lazy(() => import("../footer/backtotop"));
const HowLp = lazy(() => import('./components/how'));
const DemoLp = lazy(() => import('./components/demo'));
const CreateLp = lazy(() => import('./components/create'));
const PricingLp = lazy(() => import('./components/pricing'));
const ReviewLp = lazy(() => import('./components/reviews'));
const ExamplesLp = lazy(() => import('./components/examples'));
const FeaturesLp = lazy(() => import('./components/features'));

function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};domain=ai-pro.org;path=/`;
}

const isValidCookieName = (name) => {
    // A basic regex allowing only alphanumeric characters and underscores
    return /^[a-zA-Z0-9_]+$/.test(name);
};

export const getCookie = (name) => {
    if (!isValidCookieName(name)) {
        throw new Error("Invalid cookie name");
    }
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [cookieName, cookieValue] = cookie.trim().split('=');
        if (cookieName === name) {
            return cookieValue;
        }
    }
    return null;
};

function Lp() {
    const headerRef = useRef(null);
    const [isHeaderVisible, setIsHeaderVisible] = useState(true);
    const [ctaClr, setCtaClr] = useState('#0147AB');

    const handleButtonLink = () => {
        const cpdflink = getCookie('cpdflink');
        if (cpdflink === 'wp') {
            window.location.href = 'https://ai-pro.org/registration-redirect/';
        } else {
            window.location.href = 'https://start.ai-pro.org/register';
        }
    };

    useEffect(() => {
        const handleScroll = () => {
            const headerBottom = headerRef.current.getBoundingClientRect().bottom;
            setIsHeaderVisible(window.pageYOffset < headerBottom);
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    useEffect(() => {
        const ctaClrCookie = document.cookie.match(/(?:^|;) *cta_clr_lp=([^;]*)/);
        if (ctaClrCookie) {
            setCtaClr(`#${ctaClrCookie[1]}`);
        }
    }, [document.cookie]); 

    useEffect(() => {
        setCookie('flow', 'chatpdf', 30, '/', '*.ai-pro.org');
        setCookie('flow', 'chatpdf', 30, '/', '.ai-pro.org');
        setCookie('flow', 'chatpdf', 30, '/');
    }, []);

    return (
    <>
        <Header handleButtonLink={handleButtonLink} />
        <div ref={headerRef}></div>

        <div className="mx-auto pt-0 pb-10 px-2 md:pt-20 md:px-20 overflow-hidden relative">
            <div className="lg:flex lg:justify-center text-center">
                <Suspense fallback={<Loading />}>
                    <Bg srcSet={bgWebP} src={bgPNG} alt="background" />
                    <div className="w-full relative max-w-screen-lg mt-24 md:mt-16 z-20">
                        <h1 className="text-[#0049AF] text-4xl font-bold md:text-7xl mb md:mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-blue-900">
                        ChatPDF
                        </h1>
                        <h2 className="w-full mx-auto p-4 text-center text-gray-800 text-4xl md:text-7xl font-bold rounded-md bg-opacity-80 md:bg-opacity-0 bg-white md:bg-none">
                        Summarize and Chat with your Documents with AI
                        </h2>
                        <p className="w-full mx-auto p-4 text-center text-[16px] md:text-2xl text-gray-600 roundend-md bg-opacity-80 md:bg-opacity-0 bg-white md:bg-none">
                        Upload your PDF Files and have a chat with it. Ask questions, get a summary, get feedback, and much more. Start meaningful conversations with your PDF.
                        </p>
                        <div className="py-4 px-4 mx-auto">
                        <button
                            onClick={handleButtonLink}
                            style={{ backgroundColor: ctaClr }}
                            className="hover:bg-blue-400 w-full md:w-[300px] text-white font-bold py-4 px-6 rounded-lg mx-auto shadow-md text-xl lg:text-2xl"
                        >
                            Start Now
                        </button>
                        </div>
                    </div>
                </Suspense>
            </div>
        </div>
        <Suspense fallback={<Loading />}>
            <ReviewLp />
            <DemoLp />
            <FeaturesLp />
            <HowLp />
            <ExamplesLp />
            <PricingLp ctaClr={ctaClr} handleButtonLink={handleButtonLink}/>
            <CreateLp ctaClr={ctaClr} handleButtonLink={handleButtonLink}/>
            <BackToTopButton isHeaderVisible={isHeaderVisible} />
            <Footer />
        </Suspense>
    </>
    );
}

export default Lp;
