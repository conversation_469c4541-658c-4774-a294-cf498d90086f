<?php

declare(strict_types=1);

if (!function_exists('generateMonthlyPeriods')) {
    /**
     * Generates monthly periods based on an anniversary date.
     * Adjusts for varying month lengths, ensuring consistent date handling.
     * 
     * @param DateTime $anniversary The base date to start the periods from.
     * @param int $months The number of months to generate (default: 12).
     * @param DateTime|null $specificDate If provided, only the period containing this date will be returned.
     * @return array|string|null List of period ranges, a single period if specificDate is set, or null on invalid input.
     */
    function generateMonthlyPeriods(DateTime $anniversary, int $months = 12, ?DateTime $specificDate = null): array|string|null
    {
        // Validate inputs
        if (!($anniversary instanceof DateTime) || ($specificDate !== null && !($specificDate instanceof DateTime))) {
            return null;
        }
        if ($months <= 0) {
            return null;
        }

        $periods = [];
        $current = clone $anniversary;

        for ($i = 0; $i < $months; $i++) {
            $lastDayCurrent = (int) $current->format('t'); // Last day of the current month
            
            // Get the last day of the previous and next months
            $lastDayPrevious = (int) (clone $current)->modify('last day of previous month')->format('d');
            $lastDayNext = (int) (clone $current)->modify('last day of next month')->format('d');

            $day = (int) $anniversary->format('d');
            $adjustedDay = min($day, $lastDayCurrent); // Ensure the day does not exceed the month's last day
            $current->setDate((int) $current->format('Y'), (int) $current->format('m'), $adjustedDay);
            $range = clone $current;
            $to_date = "";

            // Special handling for edge cases
            if ($lastDayNext == $lastDayCurrent && $day == $lastDayCurrent && $day == $adjustedDay) {
                $to_date = $range->modify('last day of next month')->format('F j, Y');
            } elseif ($lastDayPrevious == $lastDayCurrent && $lastDayCurrent == $day && $adjustedDay == $day) {
                $to_date = $range->modify('last day of next month')->format('F j, Y');
            } elseif ($lastDayPrevious == $lastDayNext && $lastDayNext == $day && $day != $adjustedDay) {
                $to_date = $range->modify('last day of next month')->format('F j, Y');
            } elseif ($lastDayCurrent == $day && $day == $adjustedDay && $day != $lastDayPrevious && $day != $lastDayNext) {
                if ($day == 31) { // Only for dates ending in 31
                    $to_date = $range->modify('last day of next month')->format('F j, Y');
                }
            }

            // Default case for standard month-to-month transitions
            if (!$to_date) {
                $use_day = min($day, $lastDayNext);
                if ((int) $range->format('m') == 1) { // January to February handling
                    $to_date = $range->setDate((int) $range->format('Y'), 2, $use_day)->format('F j, Y');
                } elseif ((int) $range->format('m') == 12) { // December, move to next year
                    $to_date = $range->setDate((int) $range->format('Y') + 1, (int) $range->modify('1 month')->format('m'), $use_day)->format('F j, Y');
                } else {
                    $to_date = $range->setDate((int) $range->format('Y'), (int) $range->modify('1 month')->format('m'), $use_day)->format('F j, Y');
                }
            }

            // Store period information
            $period = ['from' => $current->format('Y-m-d'), 'to' => $to_date];
            
            if ($specificDate) {
                $specificTimestamp = $specificDate->getTimestamp();
                $startTimestamp = $current->getTimestamp();
                $endTimestamp = (new DateTime($to_date))->getTimestamp();
                
                if ($specificTimestamp >= $startTimestamp && $specificTimestamp <= $endTimestamp) {
                    return array($period);
                }
            }

            $periods[] = $period;
            
            // Move to the first day of the next month
            $current->modify('first day of next month');
        }
        
        return !empty($periods) ? $periods : null;
    }
}
