<?php

use Config\Services as CI4Services;

require_once 'btemail_templates/btemail_dagger.php';

/**
 * BTEmail.
 */
function btemailInit($config)
{
    $email = CI4Services::email();
    $email->initialize($config);
    
    return $email;
}

function btRetrySendEmail($email, $config, $email_data) {
    $emailRetry = clone $email;
    $emailRetry->initialize($config);

    $emailRetry->setFrom($email_data['sender_email'], $email_data['sender_email_name']);
    $emailRetry->setTo($email_data['send_to']);
    // $emailRetry->setBCC($email_data['send_bcc']);
    $emailRetry->setSubject($email_data['subject']);
    $emailRetry->setMessage($email_data['message']);

    if (isset($email_data['attach'])) {
        $emailRetry->attach($email_data['attach']);
    }

    if (isset($email_data['reply_to'])) {
        $reply_to = $email_data['reply_to'];
        $reply_to_list = [];

        if (is_array($reply_to)) {
            foreach ($reply_to as $email_data) {
                if (!is_array($email_data)) {
                    $reply_to_list[] = $email_data;
                } else {
                    $reply_to_list[] = sprintf('%s <%s>', $email_data['name'], $email_data['email']);
                }
            }
        } else {
            $reply_to_list = [$reply_to];
        }

        $emailRetry->setReplyTo($reply_to_list[0]);

        if (count($reply_to_list) > 1) {
            $emailRetry->setHeader('Reply-To', implode(', ', $reply_to_list));
        }
    }

    return $emailRetry->send();
}

function btemailSend($_email_data = [])
{
    $primaryConfig = [
        'userAgent' => 'AI-Pro',
        'protocol' => 'smtp',
        'SMTPHost' => $_ENV['SMTP_HOST'] ?? null,
        'SMTPUser' => $_ENV['SMTP_USER'] ?? null,
        'SMTPPass' => $_ENV['SMTP_PASS'] ?? null,
        'SMTPPort' => '587',
        'charset' => 'utf-8',
        'mailType' => 'html',
        'newline' => "\r\n",
        'SMTPCrypto' => 'tls',
        'wordWrap' => true
    ];

    $backupConfig = [
        'userAgent' => 'AI-Pro',
        'protocol' => 'smtp',
        'SMTPHost' => $_ENV['SMTP_HOST_BACKUP'] ?? null,
        'SMTPUser' => $_ENV['SMTP_USER_BACKUP'] ?? null,
        'SMTPPass' => $_ENV['SMTP_PASS_BACKUP'] ?? null,
        'SMTPPort' => '587',
        'charset' => 'utf-8',
        'mailType' => 'html',
        'newline' => "\r\n",
        'SMTPCrypto' => 'tls',
        'wordWrap' => true
    ];

    $sender_email = (isset($_email_data['sender_email'])) ? $_email_data['sender_email'] : BTEMAIL_NOREPLY;
    $sender_email_name = (isset($_email_data['sender_email_name'])) ? $_email_data['sender_email_name'] : BTEMAIL_NOREPLYNAME;
    $send_to = (isset($_email_data['send_to'])) ? $_email_data['send_to'] : BTEMAIL_BCC;
    if (isset($_email_data['send_bcc'])) {
        if (!is_array($_email_data['send_bcc'])) {
            $_email_data['send_bcc'] = (array) $_email_data['send_bcc'];
        }
        array_push($_email_data['send_bcc'], BTEMAIL_BCC);
    }
    // $send_bcc = (isset($_email_data['send_bcc'])) ? $_email_data['send_bcc'] : BTEMAIL_BCC;
    $subject = (isset($_email_data['subject'])) ? $_email_data['subject'] : 'Test Email';
    $message = (isset($_email_data['message'])) ? $_email_data['message'] : 'This is a test email, please ignore.';
    $reply_to = $_email_data['reply_to'] ?? null;

    $email = CI4Services::email();

    if (!btRetrySendEmail($email, $primaryConfig, [
        'sender_email' => $sender_email,
        'sender_email_name' => $sender_email_name,
        'send_to' => $send_to,
        // 'send_bcc' => $send_bcc,
        'subject' => $subject,
        'message' => $message,
        'attach' => $_email_data['attach'] ?? null,
        'reply_to' => $reply_to
    ])) {
        if (!btRetrySendEmail($email, $backupConfig, [
            'sender_email' => $sender_email,
            'sender_email_name' => $sender_email_name,
            'send_to' => $send_to,
            // 'send_bcc' => $send_bcc,
            'subject' => $subject,
            'message' => $message,
            'attach' => $_email_data['attach'] ?? null,
            'reply_to' => $reply_to
        ])) {
            // Uncomment to debug
            // echo $email->printDebugger(['headers']);
            error_log('Failed to send email with both primary and backup configurations.');
        }
    }
}

/**------------------------------------------------------------------------------
 * Email: Welcome Email
------------------------------------------------------------------------------*/
function btemailSendWelcomeEmail($_email_data = [])
{
    $message = dagger_btemailSendWelcomeEmail($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'Welcome to AI-Pro!',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Thank you Email
------------------------------------------------------------------------------*/
function btemailSendThankyouEmail($_email_data = [])
{
    $message = dagger_btemailSendThankyouEmail($_email_data);
    $message = btutilUrlSafe($message);

    $pos = strpos($_email_data['send_to'], 'dispostable');
    if ($pos !== false) {
        $email_data = [
            'subject' => 'Thank You for Subscribing!',
            'message' => $message,
            'send_to' => $_email_data['send_to']
        ];
    } else {
        $email_data = [
            'subject' => 'Thank You for Subscribing!',
            'message' => $message,
            'send_to' => $_email_data['send_to'],
            'send_bcc' => '<EMAIL>'
        ];
    }
    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Reset Password
------------------------------------------------------------------------------*/
function btemailSendResetPassword($_email_data = [])
{
    $message = '';
    $message = dagger_btemailSendResetPassword($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'Reset Password Request',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}
/**------------------------------------------------------------------------------
 * Email: Delete Account Email
------------------------------------------------------------------------------*/
function btemailSendDeleteAccountEmail($_email_data = [])
{
    $message = dagger_btemailSendDeleteAccountEmail($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'Your Account Has Been Deleted',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Payment Information
------------------------------------------------------------------------------*/
function btemailSendEnterprisePaymentInfo($_email_data = [])
{
    $message = dagger_btemailSendEnterprisePaymentInfo($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'AI-PRO | Enterprise - Payment Details',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Payment Confirmation
------------------------------------------------------------------------------*/
function btemailSendEnterprisePaymentConfirmation($_email_data = [])
{
    $message = dagger_btemailSendEnterprisePaymentConfirmation($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'AI-PRO | Enterprise Payment Confirmation - Ref#'. $_email_data['reference_number'],
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    if (isset($_email_data['attach'])){
        $email_data['attach'] = $_email_data['attach'];
    }

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Welcome Email for Members
------------------------------------------------------------------------------*/
function btemailSendEnterpriseWelcomeMembers($_email_data = [])
{
    $message = dagger_btemailSendEnterpriseWelcomeMembers($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'AI-PRO | Enterprise Account Member Confirmation',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Enterprise Resend Password for Members
------------------------------------------------------------------------------*/
function btemailResendPasswordEnterpriseMembers($_email_data = [])
{
    $message = dagger_btemailResendPasswordEnterpriseMembers($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'AI-PRO | Enterprise Member Temporary Password',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Account Temporary Locked Email
------------------------------------------------------------------------------*/
function btemailSendAccountTempLockedEmail($_email_data = [])
{
    $message = dagger_btemailSendAccountTempLockedEmail($_email_data);
    $message = btutilUrlSafe($message);

    $email_data = [
        'subject' => 'AI-PRO | Unauthorized Access Detected',
        'message' => $message,
        'send_to' => $_email_data['send_to'],
    ];

    btemailSend($email_data);
}


/**------------------------------------------------------------------------------
 * Email: Alert
------------------------------------------------------------------------------*/
function btemailSendToAlert($_req = [])
{
    $subject = btutilUrlSafe($_req['subject']);
    $message = btutilUrlSafe($_req['message']);

    $send_to = BTEMAIL_ALERT;
    if (str_contains(base_url(), 'staging')) {
        $send_to = "<EMAIL>";
    } elseif (str_contains(base_url(), 'dev') || str_contains(base_url(), 'localhost')) {
        $send_to = "<EMAIL>";
    }

    $email_data = [
        'subject' => $subject,
        'message' => $message,
        'send_to' => $send_to,
    ];

    btemailSend($email_data);
}

/**------------------------------------------------------------------------------
 * Email: Send to support
------------------------------------------------------------------------------*/
function btemailSendToSupport($email_data = []) {
    $send_to = BTEMAIL_SUPPORT;
    $sender_name = 'AI-Pro.org';

    $email_data['send_to'] = $send_to;
    $email_data['sender_email_name'] = $sender_name;

    $email_data['reply_to'] = [
        $email_data['reply_to'],
        [
            'email' => $send_to,
            'name' => $sender_name,
        ]
    ];

    btemailSend($email_data);
}


function btcheckIfEmailValid($email){
    //if all fail due api error or no credit, just return as valid email
    // and return failed to verify for debugging

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => 1, 'valid' => 0, 'message' => 'You must have a valid email address'];
    }

    $apiKey = getenv('QUICKENEMAILVERIFICATION');
    if (!$apiKey) {
        return ['success' => 0, 'valid' => 1, 'message' => 'API key is missing.'];
    }

    try {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, 'https://api.quickemailverification.com/v1/verify?email='.$email.'&apikey=' . $apiKey);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            return ['success'=> 0, 'valid'=>1, 'message'=>curl_error($ch)]; 
        }
        curl_close($ch);

        $result = json_decode($result);

        if ($result->success=='true'){
            if (($result->result=='valid' || $result->result=='unknown') && $result->disposable=='false'){
                return ['success'=> 1, 'valid'=>1, 'message'=>''];
            }else{
                return ['success'=> 1, 'valid'=>0, 'message'=>'You must have a valid email address'];
            }
        }else{
            return ['success' => 0, 'valid' => 1, 'message' => 'API verification failed.'];
        }
    }catch(Exception $e) {
        return ['success'=> 0, 'valid'=>1, 'message'=> 'Exception: ' . $e->getMessage()];
    }
    
    
}