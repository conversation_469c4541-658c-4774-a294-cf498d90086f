FROM php:8.1.2-fpm

# Add a non-root user and group
RUN groupadd -r appuser && useradd -r -g appuser appuser

COPY /btapp/ /var/www/html/btapp/

WORKDIR /var/www/html/

RUN apt-get update && \
    apt-get install -y \
    git \
    zip \
    curl \
    libicu-dev

RUN docker-php-ext-install intl
RUN docker-php-ext-install pdo pdo_mysql mysqli

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Clean up
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Switch to non-root user
USER appuser

EXPOSE 9000
EXPOSE 80

CMD ["php-fpm"]