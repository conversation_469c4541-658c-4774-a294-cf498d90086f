<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

/** 
 *  Run: php spark db:seed AlterPlanTable20230623
 */
class AlterPlanTable20230623 extends Seeder
{
	protected $table = "plan";

    public function run()
    {
        $data = array(
            '2' => '7-Day Trial',
            '4' => '14-Day Trial',
            '6' => '14-Day Trial',
            '7' => '14-Day Trial',
            '8' => '14-Day Trial',
            '9' => '3-Day Trial',
            '13' => '7-Day Trial',
            '14' => '14-Day Trial',
            '17' => '14-Day Trial'
        );
        //
        foreach($data as $key => $value) {
            $builder = $this->db->table($this->table);
            $update_data = ['label' => $value];
            $builder->where('plan_id', $key)->update($update_data);
        }

        //paddle_plan_id
        $dataPaddle_plan_id = array(
            '1' => '{"test":"52617","live":"833100"}',
            '2' => '{"test":"52622","live":"833106"}',
            '3' => '{"test":"52614","live":"833097"}',
            '4' => '{"test":"52620","live":"833103"}',
            '5' => '{"test":"52615","live":"833098"}',
            '6' => '{"test":"52620","live":"833103"}',
            '7' => '{"test":"52620","live":"833103"}',
            '9' => '{"test":"52623","live":"833107"}',
            '10' => '{"test":"52617","live":"833100"}',
            '11' => '{"test":"52615","live":"833098"}',
            '12' => '{"test":"52617","live":"833100"}',
            '13' => '{"test":"52622","live":"833106"}',
            '14' => '{"test":"52621","live":"833104"}',
            '15' => '{"test":"52618","live":"833101"}',
            '16' => '{"test":"52619","live":"833102"}',
            '17' => '{"test":"53687","live":"837603"}',
            '19' => '{"test":"54608","live":"839874"}',
            '20' => '{"test":"54609","live":"839875"}',
        );
        foreach($dataPaddle_plan_id as $key => $value) {
            $builder = $this->db->table($this->table);
            $update_data = ['paddle_plan_id' => $value];
            $builder->where('plan_id', $key)
            ->update($update_data);
        }
    }
}
