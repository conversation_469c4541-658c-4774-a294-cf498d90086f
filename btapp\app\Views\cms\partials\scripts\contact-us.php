<script nonce="<?= $nonce ?>">
    (function(){"use strict";class _0x1a{constructor(_0x2a={}){this._c={..._0x2a};this._i()}_i(){this._k();this._l()}_k(){const _0x3a=document.getElementById('ai-pro-call-widget');if(_0x3a)_0x3a.remove();this._d=document.createElement('div');this._d.id='ai-pro-call-widget';if(this._c.container){const _0x4a=typeof this._c.container==='string'?document.querySelector(this._c.container):this._c.container;if(_0x4a)_0x4a.appendChild(this._d)}else{document.body.appendChild(this._d)}}_l(){const _0x5a=document.createElement('iframe');_0x5a.className='iframe_widget';_0x5a.src='https://dolttbbx.ai-pro.org/widget';_0x5a.style.cssText='height:60px;border:none;width:100%;overflow:auto;margin-left:-10px;display:inline;';_0x5a.allow='microphone';_0x5a.referrerPolicy='strict-origin-when-cross-origin';_0x5a.sandbox='allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation';_0x5a.loading='lazy';this._d.appendChild(_0x5a);this._s(_0x5a)}_s(_0x6a){window.addEventListener('message',_0x7a=>{if(_0x7a.origin!=='https://dolttbbx.ai-pro.org')return;if(_0x7a.data.type==='CALL_STARTED'){this.onCallStart?.(_0x7a.data.info);this._e('call-start',_0x7a.data.info)}else if(_0x7a.data.type==='CALL_ENDED'){this.onCallEnd?.(_0x7a.data.conversation);this._e('call-end',_0x7a.data.conversation)}})}_e(_0x8a,_0x9a){const _0xaa=new CustomEvent(`ai-pro-widget-${_0x8a}`,{detail:_0x9a});document.dispatchEvent(_0xaa)}show(){this._d.style.display='block'}hide(){this._d.style.display='none'}destroy(){if(this._d)this._d.remove()}}function _0xb(){const _0xca=document.querySelector('script[data-ai-pro-widget]');if(_0xca){const _0xda={agentId:_0xca.dataset.agentId,buttonText:_0xca.dataset.buttonText,theme:_0xca.dataset.theme,position:_0xca.dataset.position,width:_0xca.dataset.width,height:_0xca.dataset.height,container:_0xca.dataset.container};Object.keys(_0xda).forEach(_0xea=>{if(_0xda[_0xea]===undefined)delete _0xda[_0xea]});new _0x1a(_0xda)}}if(document.readyState==='loading'){document.addEventListener('DOMContentLoaded',_0xb)}else{_0xb()}window.AIProCallWidget=_0x1a})();
    <?php /* unminified and unobfuscated version of the AI Pro Call Widget script
        (function() {
          'use strict';
  
          class CallWidget {
            constructor(config = {}) {
              this.config = { ...config };
              this.init();
            }
        
            init() {
              // Create widget container
              this.createContainer();
              this.loadWidget();
            }
        
            createContainer() {
              // Remove existing widget if any
              const existing = document.getElementById('ai-pro-call-widget');
              if (existing) existing.remove();
            
              // Create container
              this.container = document.createElement('div');
              this.container.id = 'ai-pro-call-widget';
            
              // Add to page
              if (this.config.container) {
                const targetContainer = typeof this.config.container === 'string' 
                  ? document.querySelector(this.config.container)
                  : this.config.container;
                if (targetContainer) {
                  targetContainer.appendChild(this.container);
                }
              } else {
                document.body.appendChild(this.container);
              }
            }
       
            loadWidget() {
                const iframe = document.createElement('iframe');

                iframe.className = 'iframe_widget';
                iframe.src = 'https://dolttbbx.ai-pro.org/widget';
                iframe.style.cssText = `
                    height: 60px;
                    border: none;
                    width: 100%;
                    overflow: auto;
                    margin-left: -10px;
                    display: inline;
                `;
                iframe.allow = 'microphone';
                iframe.referrerPolicy = 'strict-origin-when-cross-origin';
                iframe.sandbox = 'allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation';
                iframe.loading = 'lazy';

                this.container.appendChild(iframe);
                this.setupEventListeners(iframe);
            }
        
            setupEventListeners(iframe) {
              window.addEventListener('message', (event) => {
                if (event.origin !== 'https://dolttbbx.ai-pro.org') return;
                
                if (event.data.type === 'CALL_STARTED') {
                  this.onCallStart?.(event.data.info);
                  this.dispatchCustomEvent('call-start', event.data.info);
                } else if (event.data.type === 'CALL_ENDED') {
                  this.onCallEnd?.(event.data.conversation);
                  this.dispatchCustomEvent('call-end', event.data.conversation);
                }
              });
            }
        
            dispatchCustomEvent(type, detail) {
              const event = new CustomEvent(`ai-pro-widget-${type}`, { detail });
              document.dispatchEvent(event);
            }
        
            // Public methods
            show() {
              this.container.style.display = 'block';
            }
        
            hide() {
              this.container.style.display = 'none';
            }
        
            destroy() {
              if (this.container) {
                this.container.remove();
              }
            }
          }
      
          // Auto-initialize if script has data attributes
          function autoInit() {
            const script = document.querySelector('script[data-ai-pro-widget]');
            if (script) {
              const config = {
                agentId: script.dataset.agentId,
                buttonText: script.dataset.buttonText,
                theme: script.dataset.theme,
                position: script.dataset.position,
                width: script.dataset.width,
                height: script.dataset.height,
                container: script.dataset.container
              };
          
              // Remove undefined values
              Object.keys(config).forEach(key => {
                if (config[key] === undefined) delete config[key];
              });
          
              new CallWidget(config);
            }
          }
      
          // Initialize when DOM is ready
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', autoInit);
          } else {
            autoInit();
          }
      
          // Export for manual initialization
          window.AIProCallWidget = CallWidget;
      
        })(); 
    */ ?>
</script>