<?php

/**
 *  BT Utility
 */


/**
 *  Dev Debug Use
 */
function btutilDebug($_arr = [])
{
    echo '<pre>';
    print_r($_arr);
    echo '</pre>';
    die;
}


/**
 *  Token Use
 */
function btutilGenerateUniqueId($_prefix = 'JUNE2020')
{
    return md5($_prefix . uniqid(rand(), true));
}


/**
 *  PID Use
 */
function btutilGenerateHashId($_key = [])
{
    $ctx = hash_init('fnv1a64');
    hash_update($ctx, $_key[0]);

    if (isset($_key[1])) {
        hash_update($ctx, $_key[1]);
    }

    if (isset($_key[2])) {
        hash_update($ctx, $_key[2]);
    }

    hash_update($ctx, time());

    return hash_final($ctx);
}

function btutilGeneratePassword($_value = '')
{
    $cost = 9;
    return password_hash($_value, PASSWORD_BCRYPT, ['cost' => $cost]);
}

/**
 *  URL Use
 */
function btutilUrlSafe($_string)
{
    return urldecode($_string);
}

/**
 *  Access Token Use
 */
function btutilGenerateAccessToken($_prefix = '4cc355')
{
    return btutilGenerateUniqueId($_prefix);
}

function btutilCurlGetRequest($url)
{
    // Initialize a cURL session
    $ch = curl_init();

    // Set the cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    // Execute the cURL request and get the response
    $response = curl_exec($ch);

    // Check for cURL errors
    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
    }

    // Close the cURL session
    curl_close($ch);

    return $response;
}

function btutilCurlPostRequest($url, $body)
{
    // Convert the request body to JSON format
    $json_request_body = json_encode($body);

    // Initialize a cURL session
    $ch = curl_init();

    // Set the cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_request_body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($json_request_body)
    ));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    // Execute the cURL request and get the response
    $response = curl_exec($ch);

    // Check for cURL errors
    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
    }

    // Close the cURL session
    curl_close($ch);

    return $response;
}

function btutilReplaceKey($_search = null, $_replace = null, $_subject = null)
{
    return str_replace('***|' . $_search . '|***', $_replace, $_subject);
}
function btutilAffixKey($_string)
{
    return '***|' . $_string . '|***';
}

function obj_to_arr($obj)
{
    $arr = json_decode(json_encode($obj), true);
    return $arr;
}

if (!function_exists('btutilEncrypt')) {
    function btutilEncrypt($_string, $_key = "May062024") {
        return base64_encode($_key . $_string);
    }
}

if (!function_exists('btutilDecrypt')) {
    function btutilDecrypt($_string, $_key = "May062024") {
        $withkey = base64_decode($_string);
        return str_replace($_key, "", $withkey);;
    }
}

/**
 *  VWO status check
 *  
 */
function btutilIsVwoOn()
{
    $vwo = false;
    $vwoFlag = btflag('vwo');
    $adidFlag = btflag('adid');
    $keywordFlag = btflag('keyword');
    if ($vwoFlag === null && ($adidFlag !== null && $keywordFlag !== null)) {
        $vwo = true;
    } elseif ($vwoFlag === null && ($adidFlag === null && $keywordFlag === null)) {
        $vwo = false;
    } elseif ($vwoFlag === 'on' && ($adidFlag !== null && $keywordFlag !== null)) {
        $vwo = true;
    } elseif ($vwoFlag === 'on' && ($adidFlag === null && $keywordFlag === null)) {
        $vwo = true;
    } elseif ($vwoFlag === 'off' && ($adidFlag !== null && $keywordFlag !== null)) {
        $vwo = false;
    } elseif ($vwoFlag === 'off' && ($adidFlag === null && $keywordFlag === null)) {
        $vwo = false;
    } else {
        $vwo = false;
    }

    return $vwo;
}	

/**
 *  Load balance for Payment Transactions
 *  
 */
function isLoadBalance($asTotalTransaction=0, $asPercentage=100){
    $first_digit = substr($asTotalTransaction, 0, 1);
    $last_digit = substr($asTotalTransaction, -1);
    $process_count = ($asTotalTransaction*($asPercentage/100));

    if ($asPercentage==5 && $last_digit==0){
      if(floor($process_count) == $process_count) { 
        if (($asTotalTransaction%2)==0){
          return true;
        }
      }
    }else if ($asPercentage==10 && $last_digit==0){
      return true;
    }else if ($asPercentage==15 && ($last_digit>=0&&$last_digit<=1)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==20 && ($last_digit>=0&&$last_digit<=1)){
      return true;
    }else if ($asPercentage==25 && ($last_digit>=0&&$last_digit<=2)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==30 && ($last_digit>=0&&$last_digit<=2)){
      return true;
    }else if ($asPercentage==35 && ($last_digit>=0&&$last_digit<=3)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==40 && ($last_digit>=0&&$last_digit<=3)){
      return true;
    }else if ($asPercentage==45 && ($last_digit>=0&&$last_digit<=4)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==50 && ($last_digit>=0&&$last_digit<=4)){
      return true;
     }else if ($asPercentage==55 && ($last_digit>=0&&$last_digit<=5)){
      if(floor($process_count) != $process_count) { 
          return true;
      }
    }else if ($asPercentage==60 && ($last_digit>=0&&$last_digit<=5)){
      return true;
    }else if ($asPercentage==65 && ($last_digit>=0&&$last_digit<=6)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==70 && ($last_digit>=0&&$last_digit<=6)){
      return true;
    }else if ($asPercentage==75 && ($last_digit>=0&&$last_digit<=7)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==80 && ($last_digit>=0&&$last_digit<=7)){
      return true;
    }else if ($asPercentage==85 && ($last_digit>=0&&$last_digit<=8)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==90 && ($last_digit>=0&&$last_digit<=8)){
      return true;
    }else if ($asPercentage==95 && ($last_digit>=0&&$last_digit<=9)){
      if(floor($process_count) != $process_count) { 
        return true;
      }
    }else if ($asPercentage==100 && ($last_digit>=0&&$last_digit<=9)){
      return true;
    }

    return false;
}

function transformAiProUrl($url) {
    $search = 'https://ai-pro.org/';
    $replace = 'https://assets.ai-pro.org/assets/';
    
    if ($url && strpos($url, $search) !== false) {
        return str_replace($search, $replace, $url);
    }
    
    return $url;
}

function getHostUrl()
{
    return getenv('app.baseURL') ?: '';
}

function getAllFlags()
{
  $parameters = array(
    'ppg',
    'keyword',
    'creative',
    'emailid',
    'adid',
    'mode',
    'flow',
    'splash',
    'pmt',
    'gateway',
    'force_gateway',
    'swipe',
    'qW1eMlya',
    'via',
    'vprice',
    'tp_reviews',
    'members',
    'reg',
    'regRedirectWP',
    'WcvYPABR',
    'reg_google',
    'kt8typtb',
    'cta_clr_lp',
    'cta_pmt',
    'poweredby',
    'mobDisplayPlan',
    'enterprise',
    'cmpny',
    'r_flow',
    'locales',
    'f_sle',
    'verify',
    'email_login',
    'login_token',
    'emailopt',
    'p_toggle',
    'mode_test_no_pcheck',
    'force_pmt',
    'rdct',
    'price_click',
    'reg_apple',
    'vwo',
    'pp_ctaclr',
    'pp_cta',
    'chatpdf',
    'cpdflink',
    'click_id',
    'daily',
    'acwp_ux',
    'rec2',
    'ty_rdct',
    'desc_align',
    'flux_app',
    'pricing'
  );

  $flagData = [];

  foreach ($parameters as $param) {
    $flagData[$param] = btflag($param, "");
  }

  return $flagData;
}