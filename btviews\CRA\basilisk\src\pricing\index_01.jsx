import React, { useState, useEffect, useRef } from 'react';
import './style.css';
import { motion } from "framer-motion";
import { useQuery } from "react-query";
import axios from 'axios';
import { GetCookie, SetCookie } from '../core/utils/cookies';
import { hexHash, hoverLighten } from '../core/utils/helper';
import { displayTextFormatted, PriceFormatted, displayFormattedLabel } from '../core/utils/main';
import { Helmet } from 'react-helmet';
import _ from 'underscore';
import { useTranslation } from 'react-i18next';
import BackToTop from './components/BackToTop'

import TpReviews from '../../../basilisk/src/features/tpreviews';

var plan = null;
var showToggle = false;
var hasAnnual = false;

async function getPPG() {
  var ppg = GetCookie("ppg");
  if (ppg==='') {
    ppg =  process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : '14';
  }
  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-pricing`, { ppg }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });
  const output = response.data;
 
  if(output.success) {
    plan = output.data;
    plan = output.data.filter((value) => {
      if (parseInt(ppg) === 59) return value.plan_id !== process.env.REACT_APP_ENTERPRISE_ID
      return true
    });
    hasAnnual = _.some(plan, function(o) { return o.payment_interval.toLowerCase() === "yearly"; });
    showToggle = _.some(plan, function(o) { return o.payment_interval.toLowerCase() === "monthly"; }) && hasAnnual;
    return plan;
  } else {
    return [];
  }
}

function Pricing() {
  const { t } = useTranslation();
  const headerRef = useRef(null);
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : "";
  var pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "2872FA";
  var ppg = GetCookie("ppg") ? GetCookie("ppg") : process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : "14";
  const ppgArrayWithToggle = ['97', '109', '101'];
  showToggle = (ppgArrayWithToggle.includes(ppg) && showToggle) ? showToggle : false;
  const ptoggle = GetCookie("p_toggle") ? GetCookie("p_toggle") : "";
  var billedAnnualDisplay = false;

  useEffect(() => {
    const handleScroll = () => {
      // const headerBottom = headerRef.current.getBoundingClientRect().bottom;
      // setIsHeaderVisible(window.pageYOffset < headerBottom);
    };
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  },);

  if (ppg==='48'){
    billedAnnualDisplay = true;
  }
  if (ptoggle ==='01' || ptoggle === '03' || ppg === 109 || ppg === 101){
    billedAnnualDisplay = true;
  }

  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? "yearly" : "monthly");
  const intervalChange = function() {
    if(planInterval === "monthly") {
      setPlanInterval("yearly");
    } else {
      setPlanInterval("monthly");
    }
  };

  useEffect(() => {
    const elements = document.getElementsByClassName("poppin");

    const fontFor97 = "Poppins, 'Segoe UI', Roboto, Oswald, arial, sans-serif";
    const defaultFont = `-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`;

    const selectedFont = ppg === '97' || ppg === '109' || ppg === '101' ? fontFor97 : defaultFont;

    Array.from(elements).forEach(el => {
      el.style.fontFamily = selectedFont;
      el.style.setProperty("font-family", selectedFont, "important");
    });
  }, [ppg]);

  const checkPlanInterval = function(plan) {
    if(!showToggle) return true;
    if(plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }

  const { data } = useQuery("users", getPPG);
  if(data === undefined) return;
  // const tk = GetCookie("access");

  const setPricing = function(id) {
    SetCookie('pricing', id, { path: '/' });
    window.location.href = "/pay";
  };

  return (
    <>
      <Helmet>
        <title>{t('basilisk.pricing.index.meta_title')}</title>
        <meta name="description" content="Discover AI-Pro's pricing plans tailored to your needs for high-quality content creation. Explore our enterprise solutions for groups..." />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      </Helmet>
        <div ref={headerRef}></div>
        <BackToTop />
        <div className="pricing bg-white">
          <div className="pricing_columns container mx-auto py-6">
            <div className="flex flex-col pricing-page items-center pb-6 lg:pb-6">
              {(ppg === '97' || ppg === '109' || ppg === '101') && ptoggle === '03' ? 
                <h1 className="text-[36px] text-[#192A3D] !font-semibold text-center poppins-font">
                  {t('basilisk.pricing.index.get_full_access_01')}
                </h1> : <h1 className="text-[40px] text-[#192A3D] !font-bold text-center poppin">
                  {t('basilisk.pricing.index.get_full_access_01')}
                </h1> 
              }
              { showToggle ? (
                <div className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "poppins-font" : "system"} p-1`}>
                    <div className="text-1xl lg:text-1xl font-bold text-center mb-8">
                    <div>{t('basilisk.pricing.index.choose_options')}</div>
                    </div>
                    <div className="flex items-center justify-center w-full mb-8">
                    <label for="toggleB" className="flex items-center cursor-pointer">
                        <div className={`${planInterval === 'monthly' ? "text-[#2872fa] font-bold" : "text-gray-700"} mr-3 uppercase`}>
                        {t('basilisk.pricing.index.monthly')}
                        </div>
                        <div className="relative">
                        <input type="checkbox" id="toggleB" className="sr-only toggle" onChange={intervalChange} defaultChecked={billedAnnualDisplay}/>
                        <div className="block bg-gray-400 w-12 h-6 rounded-full"></div>
                        <div className="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                        </div>
                        <div className={`${planInterval === 'yearly' ? "text-[#2872fa] font-bold" : "text-gray-700"} ml-3 uppercase`}>
                        {t('basilisk.pricing.index.yearly')}
                        </div>
                    </label>
                    </div>
                </div>
                ) : ""}
            { showToggle ? (
              <div className={`pricing-toggle md:flex flex-col md:flex-row justify-center ${ppg === '97' ? '' :'mt-8'}`}>
                {data?.map((plan, index) => (
                    checkPlanInterval(plan) ? (
                        <div key={index} className={`price_col text-center mb-8 mx-auto sm:mx-3 hover:transform hover:translate-y-[-6px] ${ index === 1 ? "relative" : "" }`}>
                            <div className="bg-white rounded-md shadow-lg hover:shadow-xl overflow-hidden mx-auto lg:w-[328px]">
                                <div className="py-6 price-content">
                                     {plan.payment_interval.toLowerCase() === "yearly" && (
                                        <div className="block bg-blue-100 text-blue-500 px-2 py-1 text-[11px] font-semibold mt-[-28px]">
                                          Up to 20% OFF on an annual subscription
                                        </div>
                                      )}
                                    <h3 className={`${ppg === '97' || ppg === '109' || ppg === '101' ?'poppins-font text-[25px]':'oswald-font text-[22px]'} mb-4 text-[#2872FA]`}>{["03", "05", "72", "111"].includes(ppg)
                                                                          ? t('basilisk.pricing.index.trial_days') : displayFormattedLabel(plan)}</h3>
                                    <div className={`bg-[#2872FA] content-center ${ billedAnnualDisplay ? "min-h-[90px]" : "" }`}>                                         
                                        { 
                                        plan.label.toLowerCase()==="enterprise" ? <p className="text-[25px] text-white lg:h-[50px] p-[6px] poppin">{t('basilisk.pricing.index.custom_plan')}</p> :
                                        <PriceFormatted plan={plan}/>
                                        }
                                    </div>
                                    <div className={`${(ppg === '97' || ppg === '101' || ppg === '109') && ptoggle === '03' ? 'pt-[0px]' : 'pt-[10px] '} px-8 lg:min-h-[360px]`}>
                                        <ul className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "poppins-font" : "system"} text-sm text-[#2872FA] text-left`}>
                                            { plan.display_txt2 ? <li className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "leading-[24px]" : "leading-[19px]"} mb-2`} dangerouslySetInnerHTML={{__html: displayTextFormatted(plan)}}></li> : null }
                                        </ul>
                                    </div>
                                    <motion.button
                                        className="arial-font bg-[#2872fa] text-[20px] text-white font-bold py-2 px-6 rounded-md w-[201px]"
                                        style={{backgroundColor: hexHash(pp_ctaclr)}}
                                        whileHover={{backgroundColor: hoverLighten(pp_ctaclr)}}
                                        whileTap={{ scale: 0.9 }}
                                        onClick={() => setPricing(plan.plan_id)} 
                                    >
                                    {plan.label.toLowerCase()==="enterprise" ? t('basilisk.pricing.index.build_my_plan') : t('basilisk.pricing.index.continue')}
                                    </motion.button>
                                </div>
                            </div>
                        </div>
                   ) : ""
                ))}
              </div>
              ) : (
                <div className={`md:flex flex-col md:flex-row justify-center ${ppg === '97' ? '' : 'mt-8'}`}>
                    {data?.map((plan, index) => (
                    <div key={index} className={`price_col text-center mb-8 mx-auto sm:mx-3 hover:transform hover:translate-y-[-6px] ${ index === 1 ? "relative" : "" }`}>
                        <div className="bg-white rounded-md shadow-lg hover:shadow-xl overflow-hidden mx-auto lg:w-[328px]">
                            <div className="py-6 price-content">
                                <h3  className={`${ppg === ' 97' || ppg === '109' || ppg === '101' ?'poppins-font text-[25px]':'oswald-font text-[22px]'}  mb-4 text-[#2872FA]`}>
                                  {["03", "05", "72", "111"].includes(ppg)
                                                                        ? t('basilisk.pricing.index.trial_days') : displayFormattedLabel(plan)}
                                </h3>
                                <div className={`bg-[#2872FA] ${ billedAnnualDisplay ? "" : "" }`}> 
                                { 
                                plan.label.toLowerCase()==="enterprise" ? <p className="text-[25px] text-white lg:h-[50px] p-[6px] poppin">{t('basilisk.pricing.index.custom_plan')}</p> :
                                <PriceFormatted plan={plan}/>
                                }
                                {
                                (plan.ppg==='108' && plan.plan_type==='Pro') ? 
                                <div className='discount-text poppin'>
                                  or $197 annual <span className='discount-bubble poppin'>30% off</span>
                                </div>
                                : 
                                (plan.ppg==='108' && plan.plan_type==='ProMax') ? 
                                <div className='discount-text poppin'>
                                  or $932 annual <span className='discount-bubble poppin'>20% off</span>
                                </div>
                                : ''
                                }
                                </div>
                                {/* mb-12 px-8 pt-[10px] lg:min-h-[360px] mt-[12px] poppin */}
                                <div className={`${(ppg === '97' || ppg === '101' || ppg === '109') && ptoggle === '03' ? 'pt-[0px]' : 'pt-[10px] '} px-8 lg:min-h-[360px] poppin`}>
                                  <ul className="text-sm text-[#2872FA] text-left">
                                      { plan.display_txt2 ? <li className={`${ppg === '97' || ppg === '109' || ppg === '101' ? "leading-[24px]" : "leading-[19px]"} mb-2`} dangerouslySetInnerHTML={{__html: displayTextFormatted(plan)}}></li> : null }
                                  </ul>
                                </div>
                                <motion.button
                                    className="arial-font bg-[#2872fa] text-[20px] text-white font-bold py-2 px-6 rounded-md w-[201px]"
                                    style={{backgroundColor: hexHash(pp_ctaclr)}}
                                    whileHover={{backgroundColor: hoverLighten(pp_ctaclr)}}
                                    whileTap={{ scale: 0.9 }}
                                    onClick={() => setPricing(plan.plan_id)}
                                >
                                {plan.label.toLowerCase()==="enterprise" ? t('basilisk.pricing.index.build_my_plan') : t('basilisk.pricing.index.continue')}
                                </motion.button>
                            </div>
                        </div>
                    </div>
                    ))}
                </div>
              )}
              {
                tpreviews === "on" ? 
                <>
                  <TpReviews/>
                </>
                : 
                <p className="text-xs text-center leading-relaxed my-8 lg:my-10">
                  <img src='https://assets.ai-pro.org/assets/wp-content/uploads/2024/06/1.png' alt="chatgpt" className="lp_img"/>
                </p>   
              }
            </div>
          </div>

          {/*<Faq />
          <div className="ask container mx-auto">
            <div className="flex flex-col items-center py-10 lg:py-16 border-2 border-gray-200 rounded-2xl">
              <h2 className="text-3xl font-bold text-center mb-6 lg:mb-8">
                Ready to take your website<br/>to the next level?
              </h2>
              <p className="text-md max-w-xl text-center leading-relaxed mb-10 lg:mb-12">
                Create your own AI chatbot with no code and watch it learn from your website content in real-time.
              </p>
              <button className="ctabtn gradient-hover-effect text-white font-bold py-3 px-6 rounded-lg">
                Create Your Chatbot Now&nbsp;
                 <i className="fas fa-sign-in-alt"></i>
              </button>
            </div>
            <div className="bgradient"></div>
          </div>*/}
        </div>
      {/*<Footer />*/}
    </>
  );
}

export default Pricing;