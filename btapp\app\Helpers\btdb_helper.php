<?php

/**
 * Entities
 * - arrange in alphabetical order
 */

use App\Entities\User;
use App\Entities\Account;
use App\Entities\Payment;
use App\Entities\PaymentMobile;
use App\Entities\Plan;
use App\Entities\UserLog;
use App\Entities\UserApp;
use App\Entities\UserIp;
use App\Entities\ThreeDSecureLog;
// use App\Entities\WPPost;
// use App\Entities\WPYoastIndex;

/**
 * Models
 * - arrange in alphabetical order
 */

use App\Models\UserModel;
use App\Models\AccountModel;
use App\Models\PaymentModel;
use App\Models\PaymentMobileModel;
use App\Models\PlanModel;
use App\Models\UserLogModel;
use App\Models\UserAppModel;
use App\Models\UserIpModel;
use App\Models\ThreeDSecureLogModel;
// use App\Models\WPPostModel;
// use App\Models\WPYoastIndexModel;
// use App\Models\WPRedirectionItemsModel;

/**
 *  BT Database.
 */
function btdbInitModel($_model)
{
    $model = null;

    switch ($_model) {
    case 'UserModel':
        $model = new UserModel();
        break;
    case 'AccountModel':
        $model = new AccountModel();
        break;
    case 'PaymentModel':
        $model = new PaymentModel();
        break;
    case 'PaymentMobileModel':
        $model = new PaymentMobileModel();
        break;
    case 'PlanModel':
        $model = new PlanModel();
        break;
    case 'UserLogModel':
        $model = new UserLogModel();
        break;
    case 'UserAppModel':
        $model = new UserAppModel();
        break;
    case 'UserIpModel':
        $model = new UserIpModel();
        break;
    case 'ThreeDSecureLogModel':
        $model = new ThreeDSecureLogModel();
        break;
    // case 'WPPostModel':
    //     $model = new WPPostModel();
    //     break;
    // case 'WPYoastIndexModel':
    //     $model = new WPYoastIndexModel();
    //     break;
    // case 'WPRedirectionItemsModel':
    //     $model = new WPRedirectionItemsModel();
    //     break;
    default:
        throw new Exception('Missing Model: '.$_model);
    }

    return $model;
}

function btdbInitEntity($_entity)
{
    $entity = null;

    switch ($_entity) {
    case 'User':
        $entity = new User();
        break;
    case 'Account';
        $entity = new Account();
        break;
    case 'Payment':
        $entity = new Payment();
        break;
    case 'PaymentMobile':
        $entity = new PaymentMobile();
        break;
    case 'Plan':
        $entity = new Plan();
        break;
    case 'UserLog':
        $entity = new UserLog();
        break;
    case 'UserApp':
        $entity = new UserApp();
        break;
    case 'UserIp':
        $entity = new UserIp();
        break;
    case 'ThreeDSecureLog':
        $entity = new ThreeDSecureLog();
        break;
    // case 'WPPost':
    //     $entity = new WPPost();
    //     break;
    // case 'WPYoastIndex':
    //     $entity = new WPYoastIndex();
    //     break;
    default:
        throw new Exception('Missing Entity: '.$_entity);
    }

    return $entity;
}

/**
 * Undocumented function
 *
 * @param [type] $_model
 * @param [type] $_entity
 * @param array $_request_data
 * @param [type] $_account_id
 * @return void
 */
function btdbUpdate($_model, $_entity, $_request_data = [], $_account_id = null) {
    return btdbUpsert($_model, $_entity, $_request_data, $_account_id);
}

/**
 *  Insert/Update Single Row.
 */
function btdbUpsert($_model, $_entity, $_request_data, $_account_id = null)
{
    if($_account_id == 'update') {

    } else if (!is_cli() && $_account_id != 'new') {
        if (is_null($_account_id)) {
            return ['success' => 0];
        }

        if ($_account_id != btsessionGetAccountUser()->account_id) {
            return ['success' => 0];
        }
    }

    $model = btdbInitModel($_model);
    // btutilDebug($model->db);
    $entity = btdbInitEntity($_entity);
    // btutilDebug($_entity);

    $entity->fill($_request_data);
    // btutilDebug($_request_data);
    $model->save($entity);
    $model->db->save_queries = true;
    // btutilDebug($model->db);
    log_message('debug', 'btdbUpdate ---> ' . $model->db->getLastQuery());

    $response_data = [];
    if ($model->errors()) {
        $response_data = $model->errors();
        // $response_data['req'] = $_request_data;
        return ['success' => 0, 'msg' => $response_data];
    }


    $internal_data = [
        'insert_id' => $model->db->insertID(),
    ];

    return [
        '_' => $internal_data, //remove this internal data on class
        'success' => 1,
    ];
}

function btdbFindBy($_model, $_field, $_value)
{
    $model = btdbInitModel($_model);
	// btutilDebug($model->db);
    log_message('debug', 'btdbFindBy ---> ' . $model->db->getLastQuery());

    $res = $model->findBy($_field, $_value);

    if (empty($res['res'])) {
        return ['success' => 3, 'msg' => 'no record/s found.', 'res' => []];
    }
    // btutilDebug($res);

    return $res;
}

/**
 *  https://codeigniter.com/user_guide/models/model.html?highlight=soft%20delete#working-with-data.
 */
function btdbDelete($_model, $_field, $_value, $_account_id = null)
{
    if (is_null($_account_id)) {
        return ['success' => 0];
    }

    $val = btdbFindBy($_model, $_field, $_value);
    $key = [];
    // btutilDebug($val);

    if (!is_array($val['res'])) {
        return ['success' => 3];
    }

    if (empty($val['res'])) {
        return ['success' => 2];
    }

    foreach ($val['res'] as $item) {
        switch ($_model) {
        case 'UserModel':
            array_push($key, $item->user_id);
            break;
        case 'AccountModel':
        case 'UserIpModel':
            break;
        default:
            throw new Exception('Missing Model: '.$_model);
        }
    }

    $where = [];
	for ($x = 0; $x < sizeof($_field); $x++) {
        $where[$_field[$x]] = $_value[$x];
    }

    $model = btdbInitModel($_model);
    $model->where($where)->delete($key);
    //log_message('debug', 'btdbDelete ---> ' . $model->getCompiledDelete(false));

    $response_data = [];
    if ($model->errors()) {
        $response_data = $model->errors();
        $response_data['req'] = $_request_data;

        return ['success' => 0, 'res' => $response_data];
    }

    return ['success' => 1];
}
