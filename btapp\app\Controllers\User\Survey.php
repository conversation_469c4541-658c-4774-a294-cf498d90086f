<?php

namespace App\Controllers\User;

use App\Controllers\BaseController;

class Survey extends BaseController
{
    private $theme = 'arcana';
    private $themeSlug = '';
    private $themePageVersion = 'v1';
    private $pageSlug = '';

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        if(!btsessionIsUserLoggedIn()) {
            header("Location: " . base_url('login'));
            die;
        }
        $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
        if(!$user['success'] || !$user['res']) {
            $this->destroySession();
        }
        
        btSessionSetUserSubscription(btsessionGET("USER"), 1);
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->theme_data();
        $this->theme_pageVersion();

        switch ($this->theme) {
            case 'basilisk-02':
            case 'basilisk':
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        if(btsessionIsUserLoggedIn()) {
            $viewData['USER'] = json_encode([
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'status' => btsessionGet('USER')->status
            ]);
        }

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_session' => true,
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => true,
            'include_fullstory' => true,
        ];

        if (btflag('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}

