<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class Home extends BaseController
{
    private $theme = FLAG_THEME_DEFAULT;
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename

    private $pageSlug = '';

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        return view('index');
        // $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        // $uri = current_url(true);
        // $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        // $this->theme_data();
        // $this->theme_pageVersion();

        // switch ($this->theme) {
        //     case 'basilisk-03':
        //     case 'basilisk-02':
        //     case 'basilisk':
        //         $this->theme_basilisk();
        //         break;
        //     case 'arcana':
        //     default:
        //         $this->theme_arcana();
        // }
    }

    public function active_session()
    {
        if(btsessionIsUserLoggedIn()) {
            $ip = getClientIP();
            $email = btsessionGet('USER')->email;
            $uid = btutilGenerateHashId([$email, $ip]);

            $ip_exists = btdbFindBy('UserIpModel', ['ip_address', 'email'], [$ip, $email]);
            if( !$ip_exists['success'] || !$ip_exists['res'] ) {
                $insert_data = [
                    'userip_pid' => $uid,
                    'email' => $email,
                    'ip_address' => $ip
                ];
                btdbUpdate('UserIpModel', 'UserIp', $insert_data, 'new');
            }

            $this->index();
            return;
        }

        $this->stop_session();
    }

    public function stop_session()
    {
        $this->destroySession();

        header("Location: " . base_url('login?rd=on'));
        exit;
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_basilisk()
    {
        // basilisk has no custom HOME page so will use the default theme
        $this->theme_arcana();
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro | Home',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            // 'include_vwo' => true,
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            // 'include_bing' => true,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag('admin','0')=='1'){
            // unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }
    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}

