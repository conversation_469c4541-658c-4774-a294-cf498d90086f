<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('FlowPages\LandingPage');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override('App\Controllers\FlowPages\LandingPage::show_404');
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
$routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.

$fullHostName = $_SERVER['HTTP_HOST'];
$checkDomain = str_contains($fullHostName, "chatapp") || str_contains($fullHostName, "chat-app");

if ($checkDomain) {
    $routes->get('/', 'FlowPages\LandingPage::index');
} else {
    $routes->get('/', 'Home::index');
    $routes->get('/chat-app', 'FlowPages\LandingPage::index');
}

$routes->get('/start-chat-gpt-b', 'ChatController::startChat');
$routes->get('/start-chatgpt-ar', 'ChatController::startChatGPTAR');
$routes->get('/start-grok-ai', 'ChatController::startGrokAI');
$routes->get('/start-stable-diffusion-w', 'ChatController::startStableDiffusion');
$routes->get('/start-deepseek-ai-c', 'ChatController::startDeepSeekAIc');


$routes->get('/active-session', 'Home::active_session');
$routes->get('/register-auth', 'FlowPages\RegisterAuth::index');

// WIP flow 4
$routes->get('/register-d', 'FlowPages\Authentication::index');
$routes->get('/registration-redirect', 'Redirect::register');
// $routes->get('/registration-redirect', 'FlowPages\RegistrationRedirect::index');
$routes->get('/redirect-register-root', 'Redirect::register');
// $routes->get('/registration-redirect', 'FlowPages\Authentication::index');
$routes->get('/select-account-type-d', 'FlowPages\Authentication::index');

//
$routes->get('/pricing-redirect', 'Redirect::pricing');

$routes->get('/enterprise-plan', 'FlowPages\LandingPage::index');
//$routes->get('/landing', 'FlowPages\LandingPage::index');
$routes->get('/free-chat', 'FlowPages\LandingPage::free_chat');
$routes->get('/free-chatbot', 'FlowPages\LandingPage::free_chatbotpro');
$routes->get('/home', 'FlowPages\LandingPage::index');
$routes->get('/register', 'FlowPages\Authentication::index');
$routes->get('/register-e', 'FlowPages\Authentication::index');
$routes->get('/signup', 'FlowPages\Authentication::index');
$routes->get('/pricing', 'FlowPages\Pricing::index');
$routes->get('/subscription-plan', 'FlowPages\Pricing::index');
$routes->get('/plans-and-pricing-d', 'FlowPages\Pricing::index');
$routes->get('/subscription-e', 'FlowPages\Pricing::index');
$routes->get('/subscription', 'FlowPages\Pricing::index');
$routes->get('/upgrade', 'FlowPages\Pricing::upgrade');
$routes->get('/upgrade-ent/(:any)', 'FlowPages\Pricing::upgradeEnt/$1');
$routes->get('/downgrade', 'FlowPages\Pricing::downgrade');
$routes->get('/pay', 'FlowPages\Payment::index');
$routes->get('/upgrade/(:any)', 'FlowPages\Payment::upgrade/$1');
$routes->get('/resume', 'FlowPages\Payment::resume');
$routes->get('/downgrade/(:any)', 'FlowPages\Payment::downgrade/$1');
$routes->get('/pay/(:any)', 'FlowPages\Payment::index/$1');
$routes->get('/login', 'FlowPages\Authentication::index');
$routes->get('/forgot', 'FlowPages\Authentication::index');
$routes->get('/resetpass', 'FlowPages\Authentication::index');
$routes->get('/thankyou', 'FlowPages\ThankYou::index');
$routes->get('/ty', 'FlowPages\ThankYou::index_fake');
$routes->get('/redirect-account-required', 'User\Home::redirectAccountRequired');
$routes->get('/myaccount', 'User\Home::index');
$routes->get('/my-account', 'User\Home::index');
$routes->get('/manage', 'User\Settings::index');
$routes->get('/manage-account', 'User\Settings::index');
$routes->get('/change-card', 'User\Settings::changeCard');
$routes->get('/survey', 'User\Survey::index');
$routes->get('/google/login', 'Api::google_login');
$routes->get('/google/login_process', 'Api::google_login_process');
$routes->get('/google/register', 'Api::google_register');
$routes->get('/google/register_process', 'Api::google_register_process');
$routes->get('/apple/login', 'Api::apple_login');
$routes->post('/apple/handle-apple-login', 'Api::handle_apple_login');
$routes->get('/apple/register/(:any)', 'Api::apple_register/$1');
$routes->post('/apple/handle-apple-register/(:any)', 'Api::handle_apple_register/$1');

$routes->get('/check-reg-social-login', 'Api::check_reg_social_login');

$routes->post('/api/m/(:any)', 'Api::v1_m/$1');
$routes->post('/api/t/(:any)', 'Api::v1/$1',['filter' => "throttle"]);
$routes->post('/api/(:any)', 'Api::v1/$1');
$routes->get('/api/logout', 'Api::v1/logout');
$routes->get('/stop-session', 'Home::stop_session');

$routes->get('/api/geotest', 'Api::geotest');

$routes->get('/api/execute-paypal-agreement/(:any)(:any)(:any)', 'Api::executePayPalAgreement/$1/$2/$3');
$routes->get('/api/process-sct-dl', 'Api::executeSctDirectLink');
$routes->get('/api/process-st-dl/(:any)', 'Api::executeStripeDirectLink/$1');
$routes->get('/api/execute-threed-secure-update', 'Api::executeThreeDSecureUpdate');
$routes->get('/api/execute-threed-secure-new', 'Api::executeThreeDSecureNew');
$routes->get('/api/execute-threed-secure-changecard', 'Api::executeThreeDSecureChangeCard');

$routes->get('/(:any)', 'Redirect::page');

// $routes->get('/api/test', 'Api::test');

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
// if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
//     require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
// }

// if (is_file(APPPATH . 'Config/' . getenv('SITE_ENVIRONMENT') . '/Routes.php')) {
//     require APPPATH . 'Config/' . getenv('SITE_ENVIRONMENT') . '/Routes.php';
// }