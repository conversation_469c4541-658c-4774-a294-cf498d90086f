DEBUG - 2025-05-28 04:57:59 --> REQ ----------->
INFO - 2025-05-28 04:57:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:06 --> REQ ----------->
INFO - 2025-05-28 04:58:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:08 --> REQ ----------->
INFO - 2025-05-28 04:58:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:09 --> REQ ----------->
INFO - 2025-05-28 04:58:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:11 --> REQ ----------->
INFO - 2025-05-28 04:58:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:11 --> REQ ----------->
INFO - 2025-05-28 04:58:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:39 --> REQ ----------->
INFO - 2025-05-28 04:58:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:40 --> REQ ----------->
INFO - 2025-05-28 04:58:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:55 --> REQ ----------->
INFO - 2025-05-28 04:58:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:56 --> REQ ----------->
INFO - 2025-05-28 04:58:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:58:57 --> REQ ----------->
INFO - 2025-05-28 04:58:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:00 --> REQ ----------->
INFO - 2025-05-28 04:59:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:01 --> REQ ----------->
INFO - 2025-05-28 04:59:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:13 --> REQ ----------->
INFO - 2025-05-28 04:59:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:19 --> REQ ----------->
INFO - 2025-05-28 04:59:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:23 --> REQ ----------->
INFO - 2025-05-28 04:59:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 04:59:23 --> REQ ----------->
INFO - 2025-05-28 04:59:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:02:08 --> REQ ----------->
INFO - 2025-05-28 05:02:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:02:57 --> REQ ----------->
INFO - 2025-05-28 05:02:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:02:58 --> REQ ----------->
INFO - 2025-05-28 05:02:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:03:03 --> REQ ----------->
INFO - 2025-05-28 05:03:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:04:06 --> REQ ----------->
INFO - 2025-05-28 05:04:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:05:52 --> REQ ----------->
INFO - 2025-05-28 05:05:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:05:58 --> REQ ----------->
INFO - 2025-05-28 05:05:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:06:05 --> REQ ----------->
INFO - 2025-05-28 05:06:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:13:57 --> REQ ----------->
INFO - 2025-05-28 05:13:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 05:13:58 --> REQ ----------->
INFO - 2025-05-28 05:13:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:26:39 --> REQ ----------->
INFO - 2025-05-28 06:26:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:26:50 --> REQ ----------->
INFO - 2025-05-28 06:26:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:26:55 --> REQ ----------->
INFO - 2025-05-28 06:26:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:26:56 --> REQ ----------->
INFO - 2025-05-28 06:26:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:29:46 --> REQ ----------->
INFO - 2025-05-28 06:29:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:29:50 --> REQ ----------->
INFO - 2025-05-28 06:29:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:48:17 --> REQ ----------->
INFO - 2025-05-28 06:48:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:48:20 --> REQ ----------->
INFO - 2025-05-28 06:48:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:49:46 --> REQ ----------->
INFO - 2025-05-28 06:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:49:47 --> btdbFindBy ---> 
ERROR - 2025-05-28 06:49:47 --> mysqli_sql_exception: Table 'aipro_local.start-posts' doesn't exist in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(26): btdbFindBy('WPPostModel', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-posts`
WHERE `post_name` = :post_name:
AND `post_status` = :post_status:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(26): btdbFindBy('WPPostModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 06:52:54 --> btdbFindBy ---> 
ERROR - 2025-05-28 06:52:54 --> mysqli_sql_exception: Unknown column 'created_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(26): btdbFindBy('WPPostModel', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-posts`
WHERE `post_name` = :post_name:
AND `post_status` = :post_status:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(26): btdbFindBy('WPPostModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:06:32 --> btdbFindBy ---> 
ERROR - 2025-05-28 07:06:32 --> mysqli_sql_exception: Unknown column 'created_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(26): btdbFindBy('WPPostModel', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-posts`
WHERE `post_name` = :post_name:
AND `post_status` = :post_status:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(26): btdbFindBy('WPPostModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:39:44 --> btdbFindBy ---> 
ERROR - 2025-05-28 07:39:44 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(26): btdbFindBy('WPPostModel', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-posts`
WHERE `post_name` = \'asdasd\'
AND `post_status` = \'publish\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-posts`
WHERE `post_name` = :post_name:
AND `post_status` = :post_status:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(26): btdbFindBy('WPPostModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:47:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:47:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asdasd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:47:39 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asdasd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asdasd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asdasd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asdasd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:49:35 --> REQ ----------->
INFO - 2025-05-28 07:49:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:49:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:49:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:49:39 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('asd')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/asd/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('asd')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:49:39 --> REQ ----------->
INFO - 2025-05-28 07:49:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:50:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:50:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 07:50:36 --> REQ ----------->
INFO - 2025-05-28 07:50:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:51:10 --> REQ ----------->
INFO - 2025-05-28 07:51:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:51:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:51:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:51:11 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(27): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:51:11 --> REQ ----------->
INFO - 2025-05-28 07:51:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:54:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:54:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:54:43 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:54:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:54:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:54:44 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:54:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 07:54:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 07:54:45 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/$1/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('$1')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 07:55:29 --> REQ ----------->
INFO - 2025-05-28 07:55:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:56:07 --> REQ ----------->
INFO - 2025-05-28 07:56:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:56:26 --> REQ ----------->
INFO - 2025-05-28 07:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 08:06:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:06:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 08:06:14 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help...')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(28): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help-businesses-make-smarter-choices')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 08:06:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:06:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:11:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:11:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:11:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:11:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 08:11:37 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help...')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help-businesses-make-smarter-choices')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 08:22:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:22:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 08:22:49 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help...')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help-businesses-make-smarter-choices')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 08:22:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:22:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
ERROR - 2025-05-28 08:22:55 --> mysqli_sql_exception: Unknown column 'deleted_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexMod...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help...')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = \'https://ai-pro.org/learn-ai/articles/how-ai-can-help-businesses-make-smarter-choices/\'
AND `object_sub_type` = \'post\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = :permalink:
AND `object_sub_type` = :object_sub_type:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(196): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(29): btdbFindBy('WPYoastIndexModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('how-ai-can-help-businesses-make-smarter-choices')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 08:25:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:25:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:26:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:26:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:22 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:27:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:27:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:29:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:29:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:29:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:29:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:32:23 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:32:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 08:32:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 08:32:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'poem-generator-guide-everything-you-need-to-know'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 08:58:26 --> Undefined property: App\Controllers\Redirect::$load
in APPPATH\Controllers\Redirect.php on line 38.
 1 APPPATH\Controllers\Redirect.php(38): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: App\\Controllers\\Redirect::$load', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 38)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 08:58:41 --> REQ ----------->
INFO - 2025-05-28 08:58:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 09:05:26 --> REQ ----------->
INFO - 2025-05-28 09:05:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-28 09:05:27 --> Missing Model: WPRedirectionItemsModel
in APPPATH\Helpers\btdb_helper.php on line 79.
 1 APPPATH\Helpers\btdb_helper.php(192): btdbInitModel('WPRedirectionItemsModel')
 2 APPPATH\Controllers\Redirect.php(40): btdbFindBy('WPRedirectionItemsModel', [...], [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:05:27 --> REQ ----------->
INFO - 2025-05-28 09:05:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-28 09:05:59 --> Missing Model: WPRedirectionItemsModel
in APPPATH\Helpers\btdb_helper.php on line 79.
 1 APPPATH\Helpers\btdb_helper.php(192): btdbInitModel('WPRedirectionItemsModel')
 2 APPPATH\Controllers\Redirect.php(39): btdbFindBy('WPRedirectionItemsModel', [...], [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:06:58 --> btdbFindBy ---> 
ERROR - 2025-05-28 09:06:58 --> mysqli_sql_exception: Unknown column 'created_at' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(200): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Redirect.php(39): btdbFindBy('WPRedirectionIt...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-redirection_items`
WHERE `url` = \'/contact/\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-redirection_items`
WHERE `url` = \'/contact/\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-redirection_items`
WHERE `url` = \'/contact/\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-redirection_items`
WHERE `url` = :url:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(200): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Redirect.php(39): btdbFindBy('WPRedirectionItemsModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:08:46 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:08:46 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 44.
 1 APPPATH\Controllers\Redirect.php(44): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 44)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:09:17 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:09:17 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:09:59 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:09:59 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:10:22 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:10:22 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:11:20 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:11:20 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:11:34 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:11:34 --> Undefined property: stdClass::$action_url
in APPPATH\Controllers\Redirect.php on line 45.
 1 APPPATH\Controllers\Redirect.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$action_url', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Redirect.php', 45)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:32:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:32:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:37:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:37:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:37:26 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:37:26 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(45): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:37:26 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:37:28 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:37:28 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(45): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:37:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:38:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:38:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:38:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:39:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:39:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:40:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:41:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:41:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:42:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:42:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:42:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:02 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:43:02 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(48): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:43:02 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:03 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:43:03 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(48): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:43:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:03 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:43:03 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(48): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:43:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:04 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:43:04 --> The route for "https://ai-pro.org/contact-us/" cannot be found.
in SYSTEMPATH\HTTP\RedirectResponse.php on line 60.
 1 SYSTEMPATH\HTTP\RedirectResponse.php(60): CodeIgniter\HTTP\Exceptions\HTTPException::forInvalidRedirectRoute('https://ai-pro.org/contact-us/')
 2 SYSTEMPATH\Common.php(874): CodeIgniter\HTTP\RedirectResponse->route(false)
 3 APPPATH\Controllers\Redirect.php(48): redirect('https://ai-pro.org/contact-us/')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('contact')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:43:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:43:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:44:00 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:44:02 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:45:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:45:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:08 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:46:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:47:00 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:47:00 --> Call to undefined function App\Controllers\show_404()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('.well-known', 'appspecific', 'com.chrome.devtools.json')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:47:02 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:47:02 --> Call to undefined function App\Controllers\show_404()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('asdasd')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:47:02 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:47:02 --> Call to undefined function App\Controllers\show_404()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('.well-known', 'appspecific', 'com.chrome.devtools.json')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:48:00 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:48:00 --> Call to undefined function App\Controllers\show_404()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('404')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:48:00 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:48:00 --> Call to undefined function App\Controllers\show_404()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('.well-known', 'appspecific', 'com.chrome.devtools.json')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:48:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:26 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:48:51 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:48:51 --> Call to undefined method App\Controllers\Redirect::index()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('404')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:48:51 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 09:48:51 --> Call to undefined method App\Controllers\Redirect::index()
in APPPATH\Controllers\Redirect.php on line 58.
 1 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Redirect->page('.well-known', 'appspecific', 'com.chrome.devtools.json')
 2 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Redirect))
 3 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 09:49:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:49:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:50:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:50:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 09:50:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:50:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:50:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '$1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 09:51:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:51:19 --> REQ ----------->
INFO - 2025-05-28 09:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 09:51:34 --> REQ ----------->
INFO - 2025-05-28 09:51:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 09:51:34 --> REQ ----------->
INFO - 2025-05-28 09:51:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 09:51:34 --> btdbFindBy ---> 
DEBUG - 2025-05-28 09:51:35 --> REQ ----------->
INFO - 2025-05-28 09:51:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 09:53:30 --> REQ ----------->
INFO - 2025-05-28 09:53:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:00:23 --> REQ ----------->
INFO - 2025-05-28 10:00:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:11 --> REQ ----------->
INFO - 2025-05-28 10:02:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:14 --> REQ ----------->
INFO - 2025-05-28 10:02:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:15 --> REQ ----------->
INFO - 2025-05-28 10:02:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:21 --> REQ ----------->
INFO - 2025-05-28 10:02:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:21 --> REQ ----------->
INFO - 2025-05-28 10:02:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:26 --> REQ ----------->
INFO - 2025-05-28 10:02:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:26 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> REQ ----------->
INFO - 2025-05-28 10:02:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:28 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:37 --> REQ ----------->
INFO - 2025-05-28 10:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:38 --> REQ ----------->
INFO - 2025-05-28 10:02:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:39 --> REQ ----------->
INFO - 2025-05-28 10:02:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:51 --> REQ ----------->
INFO - 2025-05-28 10:02:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:02:52 --> REQ ----------->
INFO - 2025-05-28 10:02:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:02:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:04:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:04:16 --> REQ ----------->
INFO - 2025-05-28 10:04:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:04:22 --> REQ ----------->
INFO - 2025-05-28 10:04:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:04:26 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:04:27 --> REQ ----------->
INFO - 2025-05-28 10:04:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:04:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:04:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:04:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:05:00 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:05:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:05:01 --> REQ ----------->
INFO - 2025-05-28 10:05:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:05:27 --> REQ ----------->
INFO - 2025-05-28 10:05:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:05:30 --> REQ ----------->
INFO - 2025-05-28 10:05:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:05:32 --> REQ ----------->
INFO - 2025-05-28 10:05:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:05:39 --> REQ ----------->
INFO - 2025-05-28 10:05:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:05:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:05:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:05:39 --> REQ ----------->
INFO - 2025-05-28 10:05:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:20 --> REQ ----------->
INFO - 2025-05-28 10:06:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:06:21 --> REQ ----------->
INFO - 2025-05-28 10:06:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:22 --> REQ ----------->
INFO - 2025-05-28 10:06:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:23 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:23 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:24 --> REQ ----------->
INFO - 2025-05-28 10:06:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:32 --> REQ ----------->
INFO - 2025-05-28 10:06:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:33 --> REQ ----------->
INFO - 2025-05-28 10:06:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:06:36 --> REQ ----------->
INFO - 2025-05-28 10:06:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asdasd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:06:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:39 --> REQ ----------->
INFO - 2025-05-28 10:06:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:53 --> REQ ----------->
INFO - 2025-05-28 10:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:54 --> REQ ----------->
INFO - 2025-05-28 10:06:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:06:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:06:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'asdasd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:07:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:07:15 --> REQ ----------->
INFO - 2025-05-28 10:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:07:47 --> REQ ----------->
INFO - 2025-05-28 10:07:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:07:48 --> REQ ----------->
INFO - 2025-05-28 10:07:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:07:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:07:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:07:49 --> REQ ----------->
INFO - 2025-05-28 10:07:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:07:58 --> REQ ----------->
INFO - 2025-05-28 10:07:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:07:58 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:07:58 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:07:59 --> REQ ----------->
INFO - 2025-05-28 10:07:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:08:28 --> REQ ----------->
INFO - 2025-05-28 10:08:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:08:31 --> REQ ----------->
INFO - 2025-05-28 10:08:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:08:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:08:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:08:32 --> REQ ----------->
INFO - 2025-05-28 10:08:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:08:35 --> REQ ----------->
INFO - 2025-05-28 10:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:08:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:11:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:11:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:11:56 --> REQ ----------->
INFO - 2025-05-28 10:11:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:12:03 --> REQ ----------->
INFO - 2025-05-28 10:12:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:12:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:12:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:12:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:12:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:08 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:57 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:14:57 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:15:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:07 --> REQ ----------->
INFO - 2025-05-28 10:16:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:11 --> REQ ----------->
INFO - 2025-05-28 10:16:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:11 --> REQ ----------->
INFO - 2025-05-28 10:16:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:20 --> REQ ----------->
INFO - 2025-05-28 10:16:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:28 --> REQ ----------->
INFO - 2025-05-28 10:16:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'sasd'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:16:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:29 --> REQ ----------->
INFO - 2025-05-28 10:16:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:37 --> REQ ----------->
INFO - 2025-05-28 10:16:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:16:43 --> REQ ----------->
INFO - 2025-05-28 10:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:16:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:16:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:16:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:27:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:27:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:27:30 --> REQ ----------->
INFO - 2025-05-28 10:27:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:27:44 --> REQ ----------->
INFO - 2025-05-28 10:27:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:27:44 --> REQ ----------->
INFO - 2025-05-28 10:27:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:27:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:27:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:27:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:27:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:28:01 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:28:02 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:28:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:28:02 --> REQ ----------->
INFO - 2025-05-28 10:28:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:28:17 --> REQ ----------->
INFO - 2025-05-28 10:28:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:28:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:28:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = ''
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:28:17 --> REQ ----------->
INFO - 2025-05-28 10:28:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:28:17 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:28:17 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'com.chrome.devtools.json'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:28:18 --> REQ ----------->
INFO - 2025-05-28 10:28:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:28:42 --> REQ ----------->
INFO - 2025-05-28 10:28:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:28:42 --> REQ ----------->
INFO - 2025-05-28 10:28:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:32:26 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:32:27 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:32:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:32:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:35:46 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:35:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:35:47 --> REQ ----------->
INFO - 2025-05-28 10:35:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:36:39 --> REQ ----------->
INFO - 2025-05-28 10:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:36:54 --> REQ ----------->
INFO - 2025-05-28 10:36:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-28 10:37:23 --> Invalid file: "articles/index.html"
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(214): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('articles/index.html')
 2 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index.html', [], true)
 3 APPPATH\Controllers\Articles.php(19): view('articles/index.html')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('articles', 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 10:37:24 --> REQ ----------->
INFO - 2025-05-28 10:37:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:37:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 10:38:18 --> Invalid file: "learn-ai/articles/pages.php"
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(214): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('learn-ai/articles/pages.php')
 2 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('learn-ai/articles/pages', [], true)
 3 APPPATH\Controllers\Articles.php(51): view('learn-ai/articles/pages', [...])
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 10:38:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:38:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:40 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:38:40 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:39:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:39:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-for-teachers-to-enhance-teaching-skills'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:39:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:40:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:40:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:40:07 --> REQ ----------->
INFO - 2025-05-28 10:40:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:40:26 --> REQ ----------->
INFO - 2025-05-28 10:40:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:40:36 --> REQ ----------->
INFO - 2025-05-28 10:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 10:40:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:47:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:47:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:47:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:58:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 10:58:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-generate-prompts-for-stable-diffusion'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 10:58:07 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 11:06:36 --> Invalid file: "tutorials/index.html"
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(214): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('tutorials/index.html')
 2 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('tutorials/index.html', [], true)
 3 APPPATH\Controllers\Articles.php(19): view('tutorials/index.html')
 4 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('tutorials')
 5 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 6 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 11:06:36 --> btdbFindBy ---> 
CRITICAL - 2025-05-28 11:07:56 --> include(../../global.php): Failed to open stream: No such file or directory
in APPPATH\Views\articles\tutorials.php on line 555.
 1 APPPATH\Views\articles\tutorials.php(555): CodeIgniter\Debug\Exceptions->errorHandler(2, 'include(../../global.php): Failed to open stream: No such file or directory', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\tutorials.php', 555)
 2 APPPATH\Views\articles\tutorials.php(555): include()
 3 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\tutorials.php')
 4 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 5 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/tutorials.php', [], true)
 6 APPPATH\Controllers\Articles.php(19): view('articles/tutorials.php')
 7 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->index('tutorials')
 8 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 9 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 11:07:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:48 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:54 --> REQ ----------->
INFO - 2025-05-28 11:09:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:09:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:57 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:57 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:09:58 --> REQ ----------->
INFO - 2025-05-28 11:09:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:11:44 --> REQ ----------->
INFO - 2025-05-28 11:11:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:11:49 --> REQ ----------->
INFO - 2025-05-28 11:11:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:12:32 --> REQ ----------->
INFO - 2025-05-28 11:12:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:12:32 --> REQ ----------->
INFO - 2025-05-28 11:12:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:12:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:05 --> REQ ----------->
INFO - 2025-05-28 11:13:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:13:18 --> REQ ----------->
INFO - 2025-05-28 11:13:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:13:18 --> REQ ----------->
INFO - 2025-05-28 11:13:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:13:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:13:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:13:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-create-stunning-studio-ghibli-inspired-art-using-ai-a-comprehensive-guide'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:13:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:13:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:14:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:15:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:22 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:23 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:23 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:23 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:30 --> REQ ----------->
INFO - 2025-05-28 11:16:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:35 --> REQ ----------->
INFO - 2025-05-28 11:16:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:39 --> REQ ----------->
INFO - 2025-05-28 11:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:39 --> REQ ----------->
INFO - 2025-05-28 11:16:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:43 --> REQ ----------->
INFO - 2025-05-28 11:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> REQ ----------->
INFO - 2025-05-28 11:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:16:44 --> REQ ----------->
INFO - 2025-05-28 11:16:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:17:02 --> REQ ----------->
INFO - 2025-05-28 11:17:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:19 --> REQ ----------->
INFO - 2025-05-28 11:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:17:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:17:20 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:20 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:17:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:19:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:19:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:19:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:19:15 --> REQ ----------->
INFO - 2025-05-28 11:19:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:24:05 --> REQ ----------->
INFO - 2025-05-28 11:24:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:26:58 --> REQ ----------->
INFO - 2025-05-28 11:26:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:28:58 --> REQ ----------->
INFO - 2025-05-28 11:28:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:46:31 --> REQ ----------->
INFO - 2025-05-28 11:46:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 11:46:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:46:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/how-to-use-chatgpt-to-easily-summarize-youtube-videos/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:46:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-to-use-chatgpt-to-easily-summarize-youtube-videos'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 11:46:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 11:46:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:04:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:04:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:04:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:04:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:04:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:05:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:06:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/contact/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/contact/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'contact'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:31 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:32 --> REQ ----------->
INFO - 2025-05-28 12:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:11:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:16:38 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:38 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:16:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-to-improve-business-customer-service'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '25-ways-to-use-chatgpt-to-improve-business-customer-service'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:18:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:21:01 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:21:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'mistral-ai-bets-big-with-mistral-small-3-1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:22:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:22:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'mistral-ai-bets-big-with-mistral-small-3-1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 12:22:37 --> Attempt to read property "post_title" on array
in APPPATH\Views\articles\index.php on line 389.
 1 APPPATH\Views\articles\index.php(389): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read property "post_title" on array', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 389)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(51): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'mistral-ai-bets-big-with-mistral-small-3-1')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 12:22:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:22:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'mistral-ai-bets-big-with-mistral-small-3-1'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:23:08 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:23:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:25:08 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:25:08 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:27:02 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:27:02 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:00 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:00 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-101-a-beginners-guide-to-understanding-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:00 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'how-ai-can-help-businesses-make-smarter-choices'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:07 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-101-a-beginners-guide-to-understanding-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:14 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:29:14 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:29:14 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:33:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:33:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:33:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:59 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:33:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:33:59 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:33:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:32 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:34:32 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:32 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:32 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:34:45 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:34:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:46 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:34:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:46 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:34:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:58 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:59 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:35:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:59 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:35:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:54 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:36:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:37:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:38:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:38:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:40:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:40:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 12:40:24 --> Undefined variable $next_post
in APPPATH\Controllers\Articles.php on line 39.
 1 APPPATH\Controllers\Articles.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $next_post', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 39)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'understanding-how-ai-tools-can-boost-income')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 12:40:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:40:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:40:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:40:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:44:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:44:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:45:55 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:45:55 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:13 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:55:13 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
INFO - 2025-05-28 12:55:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 12:55:26 --> REQ ----------->12
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cdn-cgi/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cdn-cgi/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cdn-cgi/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cdn-cgi/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:55:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:56:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:56:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/cdn-cgi/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:56:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:56:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/cdn-cgi/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:56:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'cdn-cgi'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:57:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:57:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 12:58:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 12:58:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:00:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:00:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:02:38 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:38 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:02:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'assets'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:02:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:02:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'assets'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:22 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:22 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:22 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:22 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:03:43 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'learn-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:05:59 --> REQ ----------->
INFO - 2025-05-28 13:05:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:06:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:07:42 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:12 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:15 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:15 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:19 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:19 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:19 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:19 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:47 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:08:47 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:47 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:08:47 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:04 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:04 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:04 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:04 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-ultimate-guide-to-ai-tools-for-boosting-productivity'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:36 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-ultimate-guide-to-ai-tools-for-boosting-productivity'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:38 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:38 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:09:40 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:10:38 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:10:38 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:18:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:18:41 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/ai-101-a-beginners-guide-to-understanding-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:18:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:18:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:18:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 13:18:41 --> Class "App\Models\WPPostsMetaModel" not found
in APPPATH\Helpers\btdb_helper.php on line 84.
 1 APPPATH\Helpers\btdb_helper.php(200): btdbInitModel('WPPostsMetaModel')
 2 APPPATH\Controllers\Articles.php(40): btdbFindBy('WPPostsMetaModel', [...], [...])
 3 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'ai-101-a-beginners-guide-to-understanding-ai')
 4 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 5 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 13:19:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:19:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:19:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `id` > '21013'
AND `post_status` = 'publish'
AND `post_type` = 'post'
 LIMIT 1
ERROR - 2025-05-28 13:19:21 --> mysqli_sql_exception: Unknown column 'post_id' in 'where clause' in C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:295
Stack trace:
#0 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
#5 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Helpers\btdb_helper.php(204): App\Models\BTModel->findBy(Array, Array)
#6 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\app\Controllers\Articles.php(40): btdbFindBy('WPPostsMetaMode...', Array, Array)
#7 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'ai-101-a-beginn...')
#8 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
#9 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\public\index.php(67): CodeIgniter\CodeIgniter->run()
#11 C:\Users\<USER>\Documents\git.baytech.ph\ai-pro.org\btapp\vendor\codeigniter4\framework\system\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\Database\MySQLi\Connection.php on line 295.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(295): mysqli->query('SELECT *
FROM `start-redirection_items`
WHERE `post_id` = \'21013\'
AND `meta_key` = \'_thumbnail_id\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(692): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `start-redirection_items`
WHERE `post_id` = \'21013\'
AND `meta_key` = \'_thumbnail_id\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 3 SYSTEMPATH\Database\BaseConnection.php(606): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `start-redirection_items`
WHERE `post_id` = \'21013\'
AND `meta_key` = \'_thumbnail_id\'
AND `created_at` > \'0000-00-00 00:00:00\'
AND `deleted_at` = \'0000-00-00 00:00:00\'
ORDER BY `created_at` DESC')
 4 SYSTEMPATH\Database\BaseBuilder.php(1616): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `start-redirection_items`
WHERE `post_id` = :post_id:
AND `meta_key` = :meta_key:
AND `created_at` > :created_at:
AND `deleted_at` = :deleted_at:
ORDER BY `created_at` DESC', [...], false)
 5 APPPATH\Models\BTModel.php(74): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Helpers\btdb_helper.php(204): App\Models\BTModel->findBy([...], [...])
 7 APPPATH\Controllers\Articles.php(40): btdbFindBy('WPPostsMetaModel', [...], [...])
 8 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'ai-101-a-beginners-guide-to-understanding-ai')
 9 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
10 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 13:19:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:19:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:24:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:24:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:41:03 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:41:03 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:43:01 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:43:01 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:43:21 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:43:21 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 13:43:21 --> Undefined property: stdClass::$post_title
in APPPATH\Controllers\Articles.php on line 46.
 1 APPPATH\Controllers\Articles.php(46): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$post_title', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Controllers\\Articles.php', 46)
 2 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'ai-101-a-beginners-guide-to-understanding-ai')
 3 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 4 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 13:43:37 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:43:37 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:43:53 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:43:53 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:44:09 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:44:09 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:23 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:48:23 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:30 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:48:30 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'ai-101-a-beginners-guide-to-understanding-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:46 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:48:46 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:46 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:48:46 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:58 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:48:58 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/author/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'author'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:58 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/author/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:58 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'author'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:48:58 --> REQ ----------->
INFO - 2025-05-28 13:48:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 13:49:00 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:49:00 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:49:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:33 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:49:33 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/author/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'author'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:33 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/author/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:33 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'author'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:49:33 --> REQ ----------->
INFO - 2025-05-28 13:49:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 13:49:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:49:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:50:18 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:50:18 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:50:24 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:50:24 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:50:41 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:50:41 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:50:56 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:50:56 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:51:07 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:51:07 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income.3'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:51:07 --> REQ ----------->
INFO - 2025-05-28 13:51:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 13:51:11 --> btdbFindBy ---> 
DEBUG - 2025-05-28 13:51:11 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 13:51:11 --> REQ ----------->
INFO - 2025-05-28 13:51:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 14:13:52 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:13:52 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:16:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:16:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:17:20 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:17:20 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:17:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:17:51 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:17:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:17:51 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:17:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:35 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:22:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:22:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:35:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:35:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:35:25 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:35:25 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/.well-known/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:35:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:35:25 --> btdbFindBy ---> SELECT *
FROM `start-yoast_indexable`
WHERE `permalink` = 'https://ai-pro.org/learn-ai/articles/.well-known/'
AND `object_sub_type` = 'post'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:35:25 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = '.well-known'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:37:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:37:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:37:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:37:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:38:15 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:38:15 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:38:44 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:38:44 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:39:16 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:39:16 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:41:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:41:39 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/understanding-how-ai-tools-can-boost-income/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:41:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:41:39 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:41:39 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:41:49 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:41:49 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:42:29 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:42:29 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
CRITICAL - 2025-05-28 14:42:29 --> Undefined property: stdClass::$name
in APPPATH\Views\articles\index.php on line 374.
 1 APPPATH\Views\articles\index.php(374): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: stdClass::$name', 'C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php', 374)
 2 SYSTEMPATH\View\View.php(226): include('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\articles\\index.php')
 3 SYSTEMPATH\View\View.php(229): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1174): CodeIgniter\View\View->render('articles/index', [], true)
 5 APPPATH\Controllers\Articles.php(55): view('articles/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(932): App\Controllers\Articles->page('articles', 'understanding-how-ai-tools-can-boost-income')
 7 SYSTEMPATH\CodeIgniter.php(497): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Articles))
 8 SYSTEMPATH\CodeIgniter.php(366): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 FCPATH\index.php(67): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Commands\Server\rewrite.php(47): require_once('C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\public\\index.php')
DEBUG - 2025-05-28 14:42:45 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:42:45 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'understanding-how-ai-tools-can-boost-income'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:42:50 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:42:50 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/a-complete-guide-to-ai-text-generators/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:42:50 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:42:51 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:42:51 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:43:05 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:43:05 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:43:06 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:43:06 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'a-complete-guide-to-ai-text-generators'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:43:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:43:10 --> btdbFindBy ---> SELECT *
FROM `start-redirection_items`
WHERE `url` = '/the-power-of-ai/'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:43:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
DEBUG - 2025-05-28 14:43:10 --> btdbFindBy ---> 
DEBUG - 2025-05-28 14:43:10 --> btdbFindBy ---> SELECT *
FROM `start-posts`
WHERE `post_name` = 'the-power-of-ai'
AND `post_status` = 'publish'
AND `created_at` > '0000-00-00 00:00:00'
AND `deleted_at` = '0000-00-00 00:00:00'
ORDER BY `created_at` DESC
