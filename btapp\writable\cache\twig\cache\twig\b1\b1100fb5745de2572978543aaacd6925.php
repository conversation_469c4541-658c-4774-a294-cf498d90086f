<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* includes/head_gtag.twig */
class __TwigTemplate_62e5421b7e4ae3842ea1e946d551d57d extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if ((twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_gtag_AW", [], "any", false, false, false, 1) == true)) {
            // line 2
            echo "

<!-- Google Conversion Code -->
<script async src=\"https://www.googletagmanager.com/gtag/js?id=AW-532672904\"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'AW-532672904'); 
  ";
            // line 12
            if ((twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_gtag_GA4", [], "any", false, false, false, 12) == true)) {
                echo " gtag('config', 'G-70TZ628CHH'); ";
            }
            // line 13
            echo "  
</script>

<!-- Event snippet for AI Sale conversion page -->
<script>
\twindow.addEventListener('load', function(){
    ty_url = window.location.href.toLowerCase();
    if(ty_url.indexOf(\"/thankyou\")!=-1) {
      if(ty_url.indexOf(\"/thankyou/?plan=basic\")!=-1){
        gtag('event', 'conversion', {'send_to': 'AW-532672904/tXcgCK_JtpMYEIjj__0B', 'currency': '";
            // line 22
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 22), "currency", [], "any", false, false, false, 22), "html", null, true);
            echo "', 'value': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 22), "value", [], "any", false, false, false, 22), "html", null, true);
            echo "', 'transaction_id': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 22), "planid", [], "any", false, false, false, 22), "html", null, true);
            echo "'});  
      }
      else if(ty_url.indexOf(\"/thankyou/?plan=pro\")!=-1){
        gtag('event', 'conversion', {'send_to': 'AW-532672904/AUuSCMiqheoYEIjj__0B', 'currency': '";
            // line 25
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 25), "currency", [], "any", false, false, false, 25), "html", null, true);
            echo "', 'value': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 25), "value", [], "any", false, false, false, 25), "html", null, true);
            echo "', 'transaction_id': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 25), "planid", [], "any", false, false, false, 25), "html", null, true);
            echo "'});    
      }
      else {
        gtag('event', 'conversion', {'send_to': 'AW-532672904/LXT6CK-gpMQYEIjj__0B', 'currency': '";
            // line 28
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 28), "currency", [], "any", false, false, false, 28), "html", null, true);
            echo "', 'value': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 28), "value", [], "any", false, false, false, 28), "html", null, true);
            echo "', 'transaction_id': '";
            echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_mixpanel", [], "any", false, false, false, 28), "planid", [], "any", false, false, false, 28), "html", null, true);
            echo "'});  
      }
    }
\t});
</script>

";
        } else {
            // line 35
            echo "  ";
            if ((twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "include_gtag_GA4", [], "any", false, false, false, 35) == true)) {
                // line 36
                echo "

<!-- Google tag (gtag.js) -->
<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-70TZ628CHH\"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-70TZ628CHH');
  gtag('config', 'AW-532672904');
</script>

  ";
            }
            // line 50
            echo "
";
        }
    }

    public function getTemplateName()
    {
        return "includes/head_gtag.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  119 => 50,  103 => 36,  100 => 35,  86 => 28,  76 => 25,  66 => 22,  55 => 13,  51 => 12,  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "includes/head_gtag.twig", "C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\twig\\includes\\head_gtag.twig");
    }
}
