import React, { Suspense, lazy } from "react";
import "./style.css";
import { useQuery } from "react-query";
import axios from "axios";
import { GetCookie, SetCookie } from "../core/utils/cookies.jsx";
import { Helmet } from "react-helmet";
import _ from "underscore";
import { useTranslation } from 'react-i18next';
import VPrice from "./components/vprice.jsx";
const VPriceDL = lazy(() => import("./components/vprice_dl.jsx"));
const VPrice01 = lazy(() => import("./components/vprice_01.jsx"));
const VPrice02 = lazy(() => import("./components/vprice_02.jsx"));
const VPrice03 = lazy(() => import("./components/vprice_03.jsx"));
const VPriceClusterPlan = lazy(() =>
  import("./components/vprice_cluster_plan.jsx")
);

var plan = null;
var showToggle = false;
var hasAnnual = false;
async function getPPG() {
  var ppg = GetCookie("ppg");

  if (ppg==='') {
    ppg =  process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : '14';
  }

  if (plan) return plan;
  const response = await axios.post(
    `${process.env.REACT_APP_API_URL}/get-pricing`,
    { ppg },
    { headers: { "content-type": "application/x-www-form-urlencoded" } }
  );
  const output = response.data;
  if (output.success) {
    plan = output.data.filter((value) => {
      if (parseInt(ppg) === 59)
        return value.plan_id !== process.env.REACT_APP_ENTERPRISE_ID;

      return true;
    });
    hasAnnual = _.some(plan, function (o) {
      return o.payment_interval.toLowerCase() === "yearly";
    });
    showToggle =
      _.some(plan, function (o) {
        return o.payment_interval.toLowerCase() === "monthly";
      }) && hasAnnual;
    return plan;
  } else {
    return [];
  }
}

function Pricing() {
  const { data } = useQuery("users", getPPG);
  const { t } = useTranslation();
  if (data === undefined) return;
  // const tk = GetCookie("access");

  const checkPlanEnt = (id) => {
    const user = data.find((item) => item.plan_id === id);
    if (user) {
      return user.plan_type.toLowerCase() === "enterprise";
    }
    return false;
  };

  const setPricing = function (id, ent) {
    SetCookie("pricing", id, { path: "/" });
    if (checkPlanEnt(id)) {
      SetCookie("iSplanEnt", '1', { path: "/" });
      window.location.href = "/pay/mcWiDilmgQ";
    } else {
      document.querySelector(".loader-container").classList.add('active');
      SetCookie("iSplanEnt", '0', { path: "/" });
      window.location.href = "/pay";
    }
  };
  const vPrice_token = GetCookie("vprice") ? GetCookie("vprice") : "";
  const commonProps = { showToggle, hasAnnual, data, setPricing };
  const ppg = GetCookie("ppg");

  return (
    <>
      <Helmet>
        <title>{t('arcana.pricing.index.pageTitle')}</title>
        <meta
          name="description"
          content={t('arcana.pricing.index.pageDescription')}
        />
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      </Helmet>
      <Suspense fallback={null}>     
        <div className="flow6-bg min-h-screen w-full absolute inset-0 overflow-auto poppins">
          <div className={`md:px-[20px] w-auto mx-auto inline justify-center h-full w-full md:w-auto`}>
            <div className="mx-auto w-full md:min-w-[450px] md:w-[90%] py-[30px]">
                {(() => {
                  if (ppg === "108") {
                    return <VPriceDL {...commonProps} />;
                  } else if (ppg === "118") {
                    return <VPriceClusterPlan {...commonProps} />;
                  } else if (
                    (vPrice_token === "" && ppg === "46") ||
                    vPrice_token === "vP1zx12mXk"
                  ) {
                    return <VPrice01 {...commonProps} />;
                  } else if (vPrice_token === "vP2xyYxjjj") {
                    return <VPrice02 {...commonProps} />;
                  } else if (vPrice_token === "waYmXFAgLs") {
                    return <VPrice03 {...commonProps} />;
                  } else {
                    return <VPrice {...commonProps} />;
                  }
                })()}
            </div>
          </div>
        </div>
      </Suspense>
    </>
  );
}

export default Pricing;
