<?php

namespace App\Controllers;

include_once '../app/Libraries/GoogleApi/Google_Client.php';
include_once '../app/Libraries/GoogleApi/contrib/Google_Oauth2Service.php';

use BT<PERSON>ore\Payment\Stripe as BTStripe;
use BT<PERSON>ore\Payment\Recurly as BTRecurly;
use BTCore\Payment\Paddle as BTPaddle;
use BTCore\Payment\PayPal as BTPayPal;
use BTCore\Payment\FastSpring as BTFastSpring;
use BTCore\Payment as BTPayment;
use BTCore\PasswordHash as BTPasswordHash;

use BTCore\Mailer\HubSpot as BTHubSpot;
use BT<PERSON><PERSON>\Mailer as BTMailer;

// MODEL
use App\Models\UserAppModel;
use App\Models\AccountModel;
use App\Models\PaymentModel;
use App\Models\PlanModel;
use App\Models\UserModel;
use App\Models\UserIpModel;
use App\Models\NegativeModel;
use App\Models\MemberAreaModel;
use App\Models\UsageTrackingModel;
use App\Models\TrusPilotLogic;
use App\Models\ThreeDSecureLogModel;
use App\Models\FluxUsageTrackingModel;

// LIBRARY
use App\Libraries\AwsS3;
use App\Libraries\RedisAemoClient;
use App\Models\ContactUsModel;

use function PHPUnit\Framework\isEmpty;

use Google_Client;
use Google_Oauth2Service;

use App\Libraries\RedisClient;

class Api extends BaseController
{
    //---------------------------------------------------------------------------------------------
    //  variables
    //---------------------------------------------------------------------------------------------

    private $req;
    private $stripe_api_key = [];
    private $recurly_api_key = [];
    private $paddle_api_key = [];
    private $paypal_api_key = [];
    private $fastspring_api_key = [];

    private $mailer_key = [];
    private $date_now;
    private $wp_hasher;

    private $google_client_id = '';
    private $google_client_secret = '';
    private $google_redirect_url = '';

    private $redis_api_key = [];
    private $proxy_check_key = [];
    //---------------------------------------------------------------------------------------------
    //  public
    //---------------------------------------------------------------------------------------------

    public function __construct()
    {
        parent::__construct();

        $this->stripe_api_key = [
            'api_key_test' => getenv('STRIPE_API_KEY_TEST'),
            'api_key_live' => getenv('STRIPE_API_KEY_LIVE')
        ];
        $this->recurly_api_key = [
            'api_key_test' => getenv("RECURLY_API_KEY_TEST"),
            'api_key_live' => getenv("RECURLY_API_KEY_LIVE")
        ];
        $this->paddle_api_key = [
            'vendor_id_test' => getenv("PADDLE_TEST_KEY"),
            'vendor_auth_code_test' => getenv("PADDLE_TEST_AUTH_CODE"),
            'vendor_id_live' => getenv("PADDLE_LIVE_KEY"),
            'vendor_auth_code_live' => getenv("PADDLE_LIVE_AUTH_CODE"),
        ];
        $this->paypal_api_key = [
            'api_key_test_clientid' => getenv("PAYPAL_TEST_CLIENTID"),
            'api_key_test_secret' => getenv("PAYPAL_TEST_SECRET"),
            'api_key_live_clientid' => getenv("PAYPAL_LIVE_CLIENTID"),
            'api_key_live_secret' => getenv("PAYPAL_LIVE_SECRET"),
        ];
        $this->fastspring_api_key = [
            'username' => getenv("FASTSPRING_API_USERNAME"),
            'password' => getenv("FASTSPRING_API_PASSWORD")
        ];

        $this->mailer_key = [ 'api_key' => getenv("MAILER_KEY") ];
        $this->wp_hasher = new BTPasswordHash( 8, true );

        $this->google_client_id = getenv('GOOGLE_CLIENT_ID');
        $this->google_client_secret = getenv('GOOGLE_CLIENT_SECRET');
		$this->google_redirect_url = getenv('GOOGLE_REDIRECT_URL');
        $this->proxy_check_key = [
            'live_key' => getenv("PROXYCHECK_KEY_LIVE"),
            'test_key' => getenv("PROXYCHECK_KEY_TEST")
        ];



        $this->redis_api_key = [
            'username' => getenv('REDIS_USER'),
            'password' => getenv('REDIS_PASS'),
            'host'   => getenv('REDIS_HOST'),
            'port'   => getenv('REDIS_PORT'),
        ];

        $this->date_now = date('Y-m-d H:i:s', time());

    }

    public function v1($_module = '')
    {
        if($_module == 'logout') {
            return $this->logout();
        }
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return;
        }

        $this->req = $this->request->getPost();

        log_message('debug', "REQ ----------->" . implode($this->req));

        switch($_module) {
            case 'check-auth':
                return $this->checkAuth();
            break;
            case 'login':
                return $this->login();
            break;
            case 'register':
                return $this->register();
            case 'is-registered':
                return $this->isRegistered();
            break;
            case 'forgot-password':
                return $this->forgotPassword();
            break;
            case 'get-pricing':
                return $this->getPPG();
            break;
            case 'change-card':
                return $this->changeCard();
            case 'get-plan':
                return $this->getPlan();
            break;
            case 'create-subscription':
                return $this->createSubscription();
            break;
            case 'create-subscription-stripe':
                return $this->createSubscription('Stripe');
            break;
            case 'create-subscription-stripe-apple-google':
                return $this->createSubscription('stripe-apple-google');
            break;
            case 'create-subscription-paypal':
                return $this->createSubscriptionPayPal();
            break;
            case 'create-subscription-fs':
                return $this->createSubscription('FastSpring');
            break;
            case 'get-pad-payment':
                return $this->getPaddlePayment();
            break;
            case 'subscribe-pad':
                return $this->subscribePaddle();
            break;
            case 'update-subscription':
                return $this->updateSubscription();
            break;
            case 'update-subscription-ent':
                return $this->updateSubscriptionENT();
            break;
            case 'get-subscription':
                return $this->getSubscription();
            break;
            case 'get-payment':
                return $this->getPayment();
            break;
            case 'cancel-subscription':
                return $this->cancelSubscription();
            break;
            case 'update-account':
                return $this->updateAccount();
            break;
            case 'change-password':
                return $this->changePassword();
            break;
            case 'save-survey':
                return $this->saveSurveyData();
            break;
            case 'delete-account':
                return $this->deleteAccount();
            break;
            case 'check-access-link':
                return $this->checkAccessLink();
            break;
            case 'send-enterprise-payment-info':
                return $this->sendEnterprisePaymentInfo();
            break;
            case 'send-enterprise-payment-confirm':
                return $this->sendEnterprisePaymentConfirmation();
            break;
            case 'add-enterprise-member':
                return $this->addEnterpriseMember();
            break;
            case 'validate-enterprise-email':
                return $this->validateEnterpriseEmail();
            break;
            case 'get-enterprise-members':
                return $this->getEnterpriseMembers();
            break;
            case 'delete-enterprise-member':
                return $this->deleteEnterpriseMember();
            break;
            case 'edit-enterprise-member':
                return $this->editEnterpriseMember();
            break;
            case 'resend-pass-enterprise-member':
                return $this->resendPassEnterpriseMember();
            break;
            case 'click-app-upgrade':
                return $this->clickAppUpgrade();
            break;
            case 'get-token-usage':
                return $this->getTokenUsage();
            break;
            case 'get-banner':
                return $this->getBanner();
            break;
            case 'check-reg-social-login':
                return $this->check_reg_social_login();
            break;
            case 'pause-subscription':
                return $this->pauseSubscription();
            break;
            case 'resume-subscription':
                return $this->resumeSubscription();
            break;
            case 'get-avatar':
                return $this->getAvatarMakerImage();
            break;
            case 'upload-avatar':
                return $this->uploadAvatarMakerImage();
            break;
            case 'set-mood-rating':
                return $this->setMoodRating();
            break;
            case 'contact-us':
                return $this->contactUs();
            break;
            case 'register-apple':
                return $this->apple_registerv1();
            break;
            case 'register-google';
                return $this->google_registerv1();
            break;
						case 'check-usage':
							return $this->checkUsage();
					break;

        }
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------

    private function pauseSubscription()
    {
        $req = $this->req;
        $subscription_id = isset($req['subscription_id']) ? $req['subscription_id'] : '';
        $merchant = isset($req['merchant']) ? strtolower($req['merchant']) : '';
        $account_pid = isset($req['account_pid']) ? strtolower($req['account_pid']) : '';
        $billing_cycle = isset($req['billing_cycle']) ? strtolower($req['billing_cycle']) : '';

        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
        } else return $this->response->setJSON($this->res_error);

        if ($subscription_id==''||$merchant==''){
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Unable to pause subscription. Please contact support." ]]);
        }

        $params = [
            'subscription_id' => $subscription_id,
            'billing_cycle' => $billing_cycle
        ];

        if ($merchant=='recurly'){
            $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->pauseSubscription($params);
        }elseif($merchant=='stripe'){
            $paymenttype = new BTStripe($this->stripe_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->pauseSubscription($params);
        }elseif($merchant=='fastspring'){
            $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->pauseSubscription($params);
        }elseif($merchant=='cardtransaction'){
            $result = $this->sctPausePlan($params,$mode);
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Unable to pause subscription. Payment type not supported." ]]);
        }

        if ($result['status']!="1"){
            return $this->response->setJSON(['success' => 0, 'data' => ['msg' => $result['message']]]);
        }

        $resumed_date = isset($result['data']['subscription_resume_date']) ? $result['data']['subscription_resume_date'] : 0 ;

        $account = btdbFindBy('AccountModel', ['account_pid', 'user_id'], [$account_pid, $user_id]);
        if($account['success'] && $account['res']) {
            $account = $account['res'][0];
            $account_id = $account->account_id;

            $account_data = [
                'account_id' => $account_id,
                'status' => 'paused',
                'updated_at' => $this->date_now,
                'resumed_at' => $resumed_date
            ];
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

            $user_data = [
                'user_id' => $user_id,
                'status' => 'paused'
            ];
            btsessionGet('USER')->status = 'paused';
            $usr = btdbUpdate('UserModel', 'User', $user_data, 'update');

            $event_data = [];
            logUserActivity('pause-subscription', $event_data);
        }

        return $this->response->setJSON(['success' => 1, 'data' => ['msg' => '']]);

    }

    private function resumeSubscription()
    {
        $req = $this->req;
        $subscription_id = isset($req['subscription_id']) ? $req['subscription_id'] : '';
        $merchant = isset($req['merchant']) ? strtolower($req['merchant']) : '';
        $account_pid = isset($req['account_pid']) ? strtolower($req['account_pid']) : '';

        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
        } else return $this->response->setJSON($this->res_error);

        if ($subscription_id==''||$merchant==''){
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Unable to pause subscription. Please contact support." ]]);
        }

        $params = [
            'subscription_id' => $subscription_id,
            'resume_date' => date("Y-m-d", time() + 86400)
        ];

        if ($merchant=='recurly'){
            $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->resumeSubscription($params);
        }elseif($merchant=='stripe'){
            $paymenttype = new BTStripe($this->stripe_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->resumeSubscription($params);
        }elseif($merchant=='fastspring'){
            $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->resumeSubscription($params);
        }elseif($merchant=='cardtransaction'){
            $result = $this->sctResumePlan($params,$mode);
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Unable to resume subscription. Payment type not supported." ]]);
        }

        if ($result['status']!="1"){
            return $this->response->setJSON(['success' => 0, 'data' => ['msg' => $result['message']]]);
        }

        $account = btdbFindBy('AccountModel', ['account_pid', 'user_id'], [$account_pid, $user_id]);
        if($account['success'] && $account['res']) {
            $account = $account['res'][0];
            $account_id = $account->account_id;

            $account_data = [
                'account_id' => $account_id,
                'status' => 'active',
                'updated_at' => $this->date_now,
                'resumed_at' => 0
            ];
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

            $user_data = [
                'user_id' => $user_id,
                'status' => 'active'
            ];
            btsessionGet('USER')->status = 'active';
            $usr = btdbUpdate('UserModel', 'User', $user_data, 'update');

            $event_data = [];
            logUserActivity('resume-subscription', $event_data);
        }

        return $this->response->setJSON(['success' => 1, 'data' => ['msg' => '']]);

    }

		private function getAvatarMakerImage()
		{
			$req = $this->req;
			if(!isset($req['folder'])) return $this->response->setJSON(['success' => 0]);
			$folder = $req['folder'];

			try {
				$s3Client = new AwsS3();
				$environment = getenv("S3_ENVIRONMENT") ? getenv("S3_ENVIRONMENT") : "live";
				$key = "avatarmaker/". $environment ."/". $folder . "/image_0.png";
				$link = $s3Client->getSignedUrl($key);

				return $this->response->setJSON(['success' => 1, 'link' => $link]);
			} catch(Exception $e) {
				return $this->response->setJSON(['success' => 0]);
			}
		}

		private function uploadAvatarMakerImage()
		{
			$req = $this->req;
			if(!isset($req['folder']) || !isset($req['base'])) return $this->response->setJSON(['success' => 0]);

			$folder = $req['folder'];
			$base64 = base64_decode($req['base']);

			try {
				$s3Client = new AwsS3();
				$environment = getenv("S3_ENVIRONMENT") ? getenv("S3_ENVIRONMENT") : "live";
				$key = "avatarmaker/". $environment ."/". $folder . "/image_0.png";
				$link = $s3Client->uploadImage($key, $base64);

				return $this->response->setJSON(['success' => 1]);
			} catch(Exception $e) {
				return $this->response->setJSON(['success' => 0]);
			}
		}

    private function login()
    {
        $req = $this->req;
        $email = $req['email'];
        $data = btdbFindBy('UserModel', 'email', $req['email']);

        if (!$data['success']) {
            return $this->response->setJSON($this->res_error);
        }

        if ((!isset($data['res'][0])) || !$this->wp_hasher->checkPassword($req['password'], $data['res'][0]->password)) {
            $res = [
                'msg' => 'User and password did not match.',
            ];
            return $this->response->setJSON(['success' => 0, 'data' => $res]);
        }


        $res = $data['res'][0];
        $is_verified = $res->verify;
        if ($is_verified == 'unverified') {
            $host = getHostUrl();
            $parsedUrl = parse_url(base_url(), PHP_URL_HOST);
            $hostParts = explode('.', $parsedUrl);
            $subdomain = $hostParts[0];

            if ($subdomain == 'staging') {
                $host = 'https://staging.ai-pro.org/';
            }

            if ($subdomain == 'uat') {
                $host = 'https://uat.ai-pro.org/';
            }

            $res = [
                'status' => $is_verified,
                'redirect_url' => $host.'check-email',
                'email' => $email
            ];
            return $this->response->setJSON(['success' => 0, 'data' => $res]);
        }

        $ip = getClientIP();
        $ip_exists = btdbFindBy('UserIpModel', ['ip_address', 'email'], [$ip, $email]);
        if( !$ip_exists['success'] || !$ip_exists['res'] ) {
            $userIpModel = new UserIpModel();
            $count_ip = $userIpModel->countIpAddress($email);

            if($count_ip['success'] && $count_ip['res']) {
                $ip_count = intval($count_ip['res'][0]->ip_count);
                $max_ip = getenv("MAX_IP") ? intval(getenv("MAX_IP")) : 10;
                $access_token = btutilGenerateAccessToken($email);

                if($ip_count >= ($max_ip - 1)) {
                    btdbUpdate('UserModel', 'User', [ 'user_id' => $res->user_id, 'login_token' => null, 'access_token' => $access_token ], 'update');
                    btemailSendAccountTempLockedEmail(['send_to' => $email, 'token' => $access_token]);

                    return $this->response->setJSON(['success' => 2, 'data' => [ 'msg' => 'Your account is currently temporarily locked. Kindly review your email for instructions on how to regain access to your account.' ]]);
                }
            }
        }

        $login_token = btutilGenerateAccessToken($email);
        if($res->login_token) {
            $login_token = $res->login_token;
        } else {
            btdbUpdate('UserModel', 'User', [ 'user_id' => $res->user_id, 'login_token' => $login_token ], 'update');
        }
        btsessionSetAccount($res, 1);

        $authCookie = $this->setAuthCookies($email, $data, $login_token);
				$aiwp_logged_in = isset($authCookie['aiwp_logged_in']) ? $authCookie['aiwp_logged_in'] : null;
        log_message('debug', 'login-------> ' . $email);
        logUserActivity('login');

        // LOG user login for chat usage
        $url = getenv('API_URL') . '/e/add-login-log';
        $ch_data = [
            'email' => $email
        ];
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($ch_data));
        $response = curl_exec($ch);
        curl_close($ch);

        return $this->response->setJSON(['success' => 1, 'data' => $login_token, 'logged_in' => $aiwp_logged_in]);
    }
    private function isRegistered(){
        $req = $this->req;
        $email = $req['email'];

        $user = btdbFindBy('UserModel', 'email', $email);

        if($user['success'] && $user['res']) {
             return $this->response->setJSON(['success' => 1, 'data' => [ 'msg' => 'User already exists' ]]);
        } else {
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => 'User does not exist' ]]);
        }
    }
    private function register()
    {
        $req = $this->req;
        log_message('debug', implode($req));

        $user_mode = btflag('mode', FLAG_MODE_LIVE);
        if(isset($req['mode']) && $req['mode'] === '1HG6wlsRSZ') $user_mode = FLAG_MODE_TEST;
        $email = $req['email'];
        $uid = btutilGenerateHashId([$email, $user_mode]);
        $login_token = btutilGeneratePassword($email . time());

        $user = btdbFindBy('UserModel', 'email', $email);

        if($user['success'] && $user['res']) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => 'User already exists' ]]);
        if($req['password'] != $req['pass_con']) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Password did not match" ]]);

        if ($this->isEmailBlock($email)){
          return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Email address blocked" ]]);
        }

        if ($this->checkNegative($email,'') == 1){
            return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Email address blocked" ]]);
        }

        // Check if valid email ---------------------------------------
        if ($user_mode!='test'){
            $checkemail = btcheckIfEmailValid($email);
            if (!$checkemail['valid']){
                return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "You must have a valid email address" ]]);
            }

            if (!$checkemail['success']){
              log_message('debug', 'btcheckIfEmailValid message -------> ' . $checkemail['message']);
            }

        }

        // User -------------------------------------------------------
        $user_ip = getClientIP();
        $user_ip = explode(",", $user_ip);
        $user_ip = trim($user_ip[0]);
        $ip = btflag("user_ip", $user_ip);
        $ip_address = '';
        if ($ip!=''){
            $arr_ip = explode(',',$ip);
            $ip_address = isset($arr_ip[0]) ?  $arr_ip[0] : '';
        }

        $country_code = getGeoCountryCode();


        $user_data = [
            'user_pid' => $uid,
            'email' => $email,
            'mode' => $user_mode,
            'website' => 'ai-pro.org',
            'password' => $this->wp_hasher->HashPassword( $req['password'] ),
            'first_name' => isset($req['first_name']) ? $req['first_name'] : '',
            'last_name' => isset($req['last_name']) ? $req['last_name'] : '',
            'login_token' => $login_token,
            'ip_address' => $ip_address,
            'country' => $country_code,
            'flags' => json_encode($this->flagsData)
        ];

        $res = btdbUpdate('UserModel', 'User', $user_data, 'new');

        if(!$res['success']) {
            if( isset($res['msg']['email']) ) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => $res['msg']['email'] ]]);
            if(!$req['password']) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "The password field is required" ]]);
            foreach($res['msg'] as $key => $value) {
                return $this->response->setJSON(['success' => 0, 'data' => ['msg' => $res['msg'][$key]]]);
            }
        }
        $data = btdbFindBy('UserModel', 'email', $email);

        if (!$data['success']) {
            return $this->response->setJSON($this->res_error);
        }
        $user_data = $data['res'][0];

        btsessionSetAccount($user_data, 1);
        //btemailSendWelcomeEmail(['send_to' => $email]);


        // MAILERLITE
        $mailertype = new BTHubSpot($this->mailer_key);
        $mailertool = new BTMailer($mailertype);
        if($this->isEmailTest($email)) {
            // Do nothing
        } else if(!$mailertool->checkExist($email)) {
          $ip = getClientIP();
          $location = getGeoIP($ip);
          $location = json_decode($location);
          $country = '';

          foreach($location as $key => $value) {
              if ($country==''){
                $country =  isset($value->countryName) ? $value->countryName : '';
              }
          }

          if (trim($country)==''){
            $country = isset($location->countryName) ? $location->countryName : '';
          }

          $adid = btflag("adid","");
          $landingpage = btflag("landingpage","");
          $splashdata = btflag_cookie('howdoiplantouse', '');

          $subscriber = [
              'email' => $email,
              'ispaid' => "0",
              'isstart' => "1",
              'country' => $country,
              'adid' => $adid,
              'landingpage' => $landingpage,
              'howdoiplantouse' => $splashdata,
              'emailoptin' => isset($req['emailOptIn']) ? $req['emailOptIn'] : ''
            ];

          $result = $mailertool->addSubscriber($subscriber);
      }

				// END MAILERLITE

				$authCookie = $this->setAuthCookies($email, $data, $login_token);
				$aiwp_logged_in = isset($authCookie['aiwp_logged_in']) ? $authCookie['aiwp_logged_in'] : null;
				
				// Set user_email flag immediately after registration
				btflag_set('user_email', $email);
				
				$event_data = obj_to_arr($user_data);
				logUserActivity('register', $event_data);

        return $this->response->setJSON(['success' => $res['success'], 'data' => $user_data, 'logged_in' => $aiwp_logged_in]);
    }

    private function logout()
    {
        $this->destroySession(false);

        return $this->response->setJSON($this->res_ok);
    }

    public function check_reg_social_login() {
        $userModel = new UserModel();

        // Get the current user's ID from the session or wherever you store it
        $user_id = session()->get('USER')->user_id; // Change this according to how you store the user ID

        // Query to check if the current user has a password set
        $user = $userModel->find($user_id);

        // Check if user register via social login
        if ($user && !empty($user->social_login)) {
            return $this->response->setJSON(['success' => 1]);
        } else {
            return $this->response->setJSON(['success' => 0]);
        }
    }
		private function checkUsage(){
			if(btsessionIsUserLoggedIn()) {
				if (btsessionGet('USER')->ent_parent_user_id!=''){
					$user_id = btsessionGet('USER')->ent_parent_user_id;
				}else{
						$user_id = btsessionGet('USER')->user_id;
				}
	
				$accountModel = new AccountModel();
				$account = $accountModel->getActiveSubscription($user_id);
	
				if($account['success']==1 && $account['res']){
					btsessionGet('USER')->expired = $account['res'][0]->expired;
				}else{
					btsessionGet('USER')->expired = 'no';
				}
	
				$maxTokenRange = $this->computeTokenDateRange($account);
	
				$totalUsage = 0;
				$tokenUse = $this->getTokenUsage();
				$tokenUse = $this->response->getJSON($tokenUse);
				$tokenUse = json_decode($tokenUse, true);
	
				if ($tokenUse['success']=='1'){
						$totalUsage = $tokenUse['total'];
				}
				
				return $this->response->setJSON(['success' => 1, 'usage' => [
					'max_members' => isset($account['res'][0]->members) ? $account['res'][0]->members : '',
					'max_tokens' => isset($account['res'][0]->max_tokens) ? $account['res'][0]->max_tokens : '0',
					'max_start' => $maxTokenRange['max_start'],
					'max_end' => $maxTokenRange['max_end'],
					'total_usage' => $totalUsage,
					'resumed_at' => isset($account['res'][0]->resumed_at_formatted) ? $account['res'][0]->resumed_at_formatted : '0',
				]]);
			}

		}
    private function checkAuth()
    {
        $req = $this->req;

				$aiwp_logged_in = btflag('aiwp_logged_in','');
				$arr_aiwp_logged_in = explode('|', urldecode($aiwp_logged_in));
                $userAppId = isset($arr_aiwp_logged_in[1]) ? $arr_aiwp_logged_in[1] : '';

        if(btsessionIsUserLoggedIn()) {
						$this->setUnauthorized();

            $plan_name = strtolower(str_replace(' ', '', $this->getUserPlanName()));
            if (btsessionGet('USER')->ent_parent_user_id!=''){
                $user_plan = "enterprise";
                $user_id = btsessionGet('USER')->ent_parent_user_id;
            }else{
                $user_plan = $this->getUserPlan();
                $user_id = btsessionGet('USER')->user_id;
            }
						$expired = 'no';

						if (btsessionHas('ACCOUNT')) {
								$end_date = btsessionGet('ACCOUNT')->end_date; 
								$now = new \DateTime(); 
								$end_date_obj = new \DateTime($end_date);
								$end_date_obj->add(new \DateInterval('P1D'));
								$expired =  $end_date_obj < $now ? 'yes' : 'no';
						}
						
            $this->reInitCookies();
						$email = btsessionGet('USER')->email;

            $plan_restriction = $this->getPlanRestriction(btsessionHas('PLAN') ? btsessionGet('PLAN')->plan_id : null);
            return $this->response->setJSON(['success' => 1, 'user' => [
                'user_id' => btsessionGet('USER')->user_id,
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'first_name' => btsessionGet('USER')->first_name,
                'last_name' => btsessionGet('USER')->last_name,
                'status' => btsessionGet('USER')->status,
                'ent_parent_user_id' => btsessionGet('USER')->ent_parent_user_id,
                'is_social' => btsessionGet('USER')->social_login,
                'plan' => $user_plan,
                'plan_name' => $plan_name,
								'merchant' => btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->merchant : '',
                'currency' => btsessionHas('PLAN') ? btsessionGet('PLAN')->currency : '',
                'interval' => btsessionHas('PLAN') ? btsessionGet('PLAN')->payment_interval : '',
                'price_per_member' => btsessionHas('PLAN') ? btsessionGet('PLAN')->price_per_member : '',
								'expired' => $expired,
                'current_date' => date("F j, Y"),
                'surveydata' => btsessionGet('USER')->survey_data,
                'authversion' => $this->getUserAuthVersion(btsessionGet('USER')->created_at),
                'plan_restriction' => $plan_restriction,
                'mode' => btsessionGet('USER')->mode,
                'user_ppg' => btsessionHas('PLAN') ? btsessionGet("PLAN")->ppg : '',
                'aiproStart' => $userAppId,
                'chatpro_hash' => $email . "|".$userAppId
            ], 'tk' => btflag('access')]);
        } else if(isset($req['tk']) && btflag('authorized', 1)) {
            $this->setUnauthorized();
            $user = $this->getUserByToken($req['tk']);
            if($user['success']) {
                $login_token = $req['tk'];
                $email = $user['data']->email;
                $this->reInitCookies();
                $this->setAuthCookies($email, $user['raw'], $login_token);
                btsessionSetAccount($user['data'], 1);

                if (isset(btsessionGet('USER')->ent_parent_user_id) && btsessionGet('USER')->ent_parent_user_id!=''){
                    $user_id = btsessionGet('USER')->ent_parent_user_id;
                    $user_plan = "enterprise";
                }else{
                    $user_id = $user['data']->user_id;
                    $user_plan = $this->getUserPlan();
                }

                $accountModel = new AccountModel();
                $account = $accountModel->getActiveSubscription($user_id);
                if ($account['success']==1 && $account['res']){
                  $user['data']->expired = $account['res'][0]->expired;
                }else{
                 $user['data']->expired = 'no';
                }

                $maxTokenRange = $this->computeTokenDateRange($account);

                $totalUsage = 0;
                $tokenUse = $this->getTokenUsage();
                $tokenUse = $this->response->getJSON($tokenUse);
                $tokenUse = json_decode($tokenUse, true);

                if ($tokenUse['success']=='1'){
                    $totalUsage = $tokenUse['total'];
                }

                $plan_restriction = $this->getPlanRestriction(btsessionHas('PLAN') ? btsessionGet('PLAN')->plan_id : null);
                return $this->response->setJSON(['success' => 1, 'user' => [
                    'user_id' => $user['data']->user_id,
                    'user_pid' => $user['data']->user_pid,
                    'email' => $user['data']->email,
                    'first_name' => $user['data']->first_name,
                    'last_name' => $user['data']->last_name,
                    'status' => $user['data']->status,
                    'expired' => $user['data']->expired,
                    'ent_parent_user_id' => btsessionGet('USER')->ent_parent_user_id,
                    'is_social' => btsessionGet('USER')->social_login,
                    'plan' => $user_plan,
                    'merchant' => btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->merchant : '',
                    'currency' => btsessionHas('PLAN') ? btsessionGet('PLAN')->currency : '',
                    'interval' => btsessionHas('PLAN') ? btsessionGet('PLAN')->payment_interval : '',
                    'max_members' => isset($account['res'][0]->max_members) ? $account['res'][0]->max_members : '',
                    'max_tokens' => isset($account['res'][0]->max_tokens) ? $account['res'][0]->max_tokens : '0',
                    'max_start' => $maxTokenRange['max_start'],
                    'max_end' => $maxTokenRange['max_end'],
                    'total_usage' => $totalUsage,
                    'price_per_member' => btsessionHas('PLAN') ? btsessionGet('PLAN')->price_per_member : '',
                    'current_date' => date("F j, Y"),
                    'surveydata' => $user['data']->survey_data,
                    'authversion' => $this->getUserAuthVersion($user['data']->created_at),
                    'plan_restriction' => $plan_restriction,
                    'resumed_at' => btsessionHas('ACCOUNT') ? btsessionGet('ACCOUNT')->resumed_at_formatted : '',
                    'mode' => btsessionGet('USER')->mode,
                    'user_ppg' => btsessionHas('PLAN') ? btsessionGet("PLAN")->ppg : '',
                    'aiproStart' => $userAppId,
                    'chatpro_hash' => $email . "|".$userAppId
                ]]);
            }
        }
        // if(btsessionIsUserLoggedIn()) return $this->response->setJSON($this->res_ok);
        return $this->response->setJSON($this->res_error);
    }

		private function setUnauthorized() {
			setcookie('authorized', 0, time() + ((86400 / 24 / 60) * 5)); // 5min
		}

		private function computeTokenDateRange($account){
			$payment_interval =  isset($account['res'][0]->payment_interval) ? strtolower($account['res'][0]->payment_interval) : '';
			$end_date =  isset($account['res'][0]->end_date) ? strtolower($account['res'][0]->end_date) : '';
            $current_date = null; //reserved

            //for testing only. must delete when deploying to prod
            $end_date = array_key_exists('8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7', $_COOKIE) ? $_COOKIE['8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7'] : $end_date;
            $current_date = array_key_exists('6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529', $_COOKIE) ? $_COOKIE['6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529'] : $current_date;

            if ($payment_interval=='monthly') {
				$date = new \DateTime($end_date);
				$date->modify('-1 month');
				$max_start =  $date->format('Y-m-d 00:00:00');

				$date_end = new \DateTime($end_date);
				$max_end = $date_end->format('F j, Y');
			} else {
                helper('bttoken_helper');

                $anniversary = new \DateTime($end_date);
            $current_date = new \DateTime($current_date ?? 'now');
                $start_date = clone $anniversary; // this is to preserve the original anniversary date(end_date). Will be the basis date for setting the beginning of the cycle.

                switch ($payment_interval) {
                    case "yearly":
                    default: // default yearly
                        // compute for the months
                        // this will show the current period even if the end_date is more than 1 year
                        // this support only months, not weeks.
                        $interval = $anniversary->diff($current_date);
                        $months = abs(($interval->y * 12) + $interval->m) + 2; // ensure positive value
                }

                $anniversary->modify("-$months months"); // subtracting the year would be easier but this supports months
                 // use start_date to set the day. Because subtracting the months does not account for the day when considering the period cycle.
                $anniversary->setDate((int) $anniversary->format('Y'), $anniversary->format('m'), $start_date->format('d'));
                $date = generateMonthlyPeriods($anniversary, $months, $current_date);

                if (!$date) {
                    // when unable to get the date, fail with no date set to prevent sql error.
                    $max_start = "0000-00-00";
                    $max_end = "0000-00-00";
                } else {
                    switch (count($date)) {
                        case 1: // return the found period
                        default: // return the last element
                            $date = end($date);
                            $max_start = $date['from'];
                            $max_end = $date['to'];
                    }
                }
			}

			return ['max_start'=>$max_start,'max_end'=>$max_end];
		}

		private function getTokenUsage(){
			$gpt_4o_max_token_usage = getenv('GPT_4o_MAX_TOKEN_USAGE') ?: 0;
			$dalle_total_image = 0;
			$dalle_max_image = 0;
			$deepseek_max_token_usage = 0;
			$deepseek_total_token = 0;
			$claude_35_total_token = 0;
			$claude_35_max_token_limit = 0;
			$claude_37_total_token = 0;
			$claude_37_max_token_limit = 0;
            $grok_v3_max_token_limit = 0;
            $grok_v3_total_token = 0;

			// Enterprise Plan Owner (Team Max, Office Max, Enterprise Max)
			$o1_prompt = 0;
			$o1_total_prompt = 0;
			$members_total_o1_prompts = 0;
			$flux_prompt = 0;
			$flux_total_prompt = 0;
			$members_total_flux_prompts = 0;

			if(btsessionIsUserLoggedIn()) {
				$email = btsessionGet('USER')->email;
				$user_id = btsessionGet('USER')->user_id;
				$ent_parent_user_id = isset(btsessionGet('USER')->ent_parent_user_id) ? btsessionGet('USER')->ent_parent_user_id : '';

				$accountModel = new AccountModel();
				$usermodel = new UserModel();
				$UsageTracking = new UsageTrackingModel();
				$FluxUsage = new FluxUsageTrackingModel();

				if ($ent_parent_user_id!==''){
					$account = $accountModel->getActiveSubscription($ent_parent_user_id);
					$subscription = $account['res'][0];
					if($account['success'] && $account['res']) {
						$maxTokenRange = $this->computeTokenDateRange($account);

						if (strtolower(str_replace(' ', '', $subscription->plan_name)) != 'enterprise') {

								$prompt_limit = $this->getCacheRedis("PromptCreditLimit", "default", true);
								$flux_limit = $this->getCacheRedis("FluxCreditLimit", "default", true);

								$o1_limit = json_decode($prompt_limit[strtolower(str_replace(' ', '', $subscription->plan_name))], true);

								$o1_ent_member_usage = $UsageTracking->getInModelTokenUsage($email, $maxTokenRange['max_start'], $o1_limit['model']);
								$o1_prompt = (isset($o1_ent_member_usage[0]->total_prompt) ? $o1_ent_member_usage[0]->total_prompt : 0);
								$flux_ent_member_usage = $FluxUsage->getFluxUsage($email, $maxTokenRange['max_start']);
								$flux_prompt = (isset($flux_ent_member_usage[0]->total_prompt) ? $flux_ent_member_usage[0]->total_prompt : 0);

								$o1_total_prompt = $o1_limit['limit'];
								$flux_total_prompt = (int) $flux_limit[strtolower(str_replace(' ', '', $subscription->plan_name))];
						}

				}else{
					return $this->response->setJSON(['success' => 0, 'data' => '', 'total' => 0]);
				}
				}else{
					$account = $accountModel->getActiveSubscription($user_id);
					if($account['success'] && $account['res']) {

					    $maxTokenRange = $this->computeTokenDateRange($account);
							$subscription = $account['res'][0];

							$claudeInfo = $this->getClaudeTokenInfo($subscription, $UsageTracking, $email, $maxTokenRange);
							$claude_35_token_limit = $claudeInfo['claude_35_token_limit'];
							$claude_35_total_token = $claudeInfo['claude_35_total_token'];
							$claude_37_token_limit = $claudeInfo['claude_37_token_limit'];
							$claude_37_total_token = $claudeInfo['claude_37_total_token'];

							$credit_limit = $this->getCacheRedis("DalleCreditLimit", "default", true);
							$prompt_limit = $this->getCacheRedis("PromptCreditLimit", "default", true);
							$flux_limit = $this->getCacheRedis("FluxCreditLimit", "default", true);
							$deepseek_token_limit = $this->getCacheRedis("DeepseekCreditLimit", "default", true);
                            $grok_v3_token_limit = $this->getCacheRedis("GrokV3CreditLimit", "default", true);
							switch (strtolower($subscription->plan_type)) {
									case 'basic':
											$dalle_max_image = isset($credit_limit['basic']) && $credit_limit['basic'] ? $credit_limit['basic'] : 20;
											$claude_35_max_token_limit = $claude_35_token_limit ? $claude_35_token_limit : 25000;
											$claude_37_max_token_limit = 0;
											break;
									case "pro":
											$dalle_max_image = isset($credit_limit['pro']) && $credit_limit['pro'] ? $credit_limit['pro'] : 50;
											$claude_35_max_token_limit = $claude_35_token_limit ? $claude_35_token_limit : 50000;
											$claude_37_max_token_limit = $claude_37_token_limit  ? $claude_37_token_limit  : 50000;
											$deepseek_max_token_usage = isset($deepseek_token_limit['pro']) && $deepseek_token_limit['pro'] ? $deepseek_token_limit['pro'] : 50000;
											$grok_v3_max_token_limit = isset($grok_v3_token_limit['pro']) && $grok_v3_token_limit['pro'] ? $grok_v3_token_limit['pro'] : 50000;
                                            break;
									case "advanced":
											$dalle_max_image = isset($credit_limit['advanced']) && $credit_limit['advanced'] ? $credit_limit['advanced'] : 80;
											$claude_35_max_token_limit = 0;
											$claude_37_max_token_limit = $claude_37_token_limit  ? $claude_37_token_limit  : 50000;
                                            $grok_v3_max_token_limit = isset($grok_v3_token_limit['advanced']) && $grok_v3_token_limit['advanced'] ? $grok_v3_token_limit['advanced'] : 50000;
											break;
									case "promax":
											$dalle_max_image = null;
											$claude_35_max_token_limit = null;
											$deepseek_max_token_usage = null;
											$claude_37_max_token_limit = null;
                                            $grok_v3_max_token_limit = null;
											break;
									case "enterprise":
											if (strtolower(str_replace(' ', '', $subscription->plan_name)) != "enterprise") {

													// Enterprise Plan Owner (Team Max, Office Max, Enterprise Max)
													$ent_members = $usermodel->getEnterpriseMembers($user_id);
													$o1_limit = json_decode($prompt_limit[strtolower(str_replace(' ', '', $subscription->plan_name))], true);

													if($ent_members['success'] && $ent_members['res']) {
															foreach($ent_members['res'] as $value){
																	$members_o1_prompts = $UsageTracking->findUserByEmailStartDateInModel($value->email, $maxTokenRange['max_start'], $o1_limit['model']);
																	$members_flux_prompts = $FluxUsage->getFluxUsage($value->email, $maxTokenRange['max_start']);

																	foreach ($members_o1_prompts as $result) {
																			$members_total_o1_prompts += $result->total_token;
																	}

																	foreach ($members_flux_prompts as $result) {
																			$members_total_flux_prompts += $result->total_prompt;
																	}
															}
													}

													$o1_prompt_usage = $UsageTracking->getInModelTokenUsage($email, $maxTokenRange['max_start'], $o1_limit['model']);
													$o1_prompt = (isset($o1_prompt_usage[0]->total_prompt) ? $o1_prompt_usage[0]->total_prompt : 0);

													$flux_prompt_usage = $FluxUsage->getFluxUsage($email, $maxTokenRange['max_start']);
													$flux_prompt = (isset($flux_prompt_usage[0]->total_prompt) ? $flux_prompt_usage[0]->total_prompt : 0);

													$o1_total_prompt = $o1_limit['limit'];
													$flux_total_prompt = (int) $flux_limit[strtolower(str_replace(' ', '', $subscription->plan_name))];

													break;
											}
									default:
											$dalle_max_image = 0;
											$claude_35_max_token_limit = 0;
											$deepseek_max_token_usage = 0;
                                            $grok_v3_max_token_limit = 0;
							}

					}else{
						return $this->response->setJSON(['success' => 0, 'data' => '', 'total' => 0]);
					}
				}

				$usages = $UsageTracking->findUserByAppEmailStartDateModel($email, $maxTokenRange['max_start']);

				$total = 0;
				$tokenUsage = [];

				foreach ($usages as $value) {
						$model = $value->model;

                    if ($model != 'dall-e-3') {
                        $app = $value->app;
                        $totalToken = ($model == "gpt-4o-mini" || $model == "meta-llama/Llama-3.2-3B-Instruct-Turbo") ? $value->total_token / 10 : $value->total_token;

                        if (isset($tokenUsage[$app])) {
                            $tokenUsage[$app]->total_token += $totalToken;
                        } else {
                            $value->total_token = $totalToken;
                            $tokenUsage[$app] = $value;
                        }

								$total += $totalToken;
						}
				}

				$usages = array_values($tokenUsage); // re-index array

				$max_tokens = isset($account['res'][0]->max_tokens) ? $account['res'][0]->max_tokens : 0;
				if ($max_tokens <= $total){
					btflag_set("mucnxwlyxt","1");
				}else{
					btflag_set("mucnxwlyxt","");
				}

				$getGpt4oTokenUsage = $UsageTracking->getGpt4oTokenUsage($email,$maxTokenRange['max_start']);
				$gpt4o_total_token = @$getGpt4oTokenUsage[0]->total_token ?? 0;

				// Dall-E
				$getDallETokenUsage = $UsageTracking->getDallETokenUsage($email,$maxTokenRange['max_start']);
				$dalle_total_image = @$getDallETokenUsage[0]->total_image ?? 0;

				//Deepseek
				$getDeepseekTokenUsage = $UsageTracking->getDeepseekTokenUsage($email,$maxTokenRange['max_start']);
				$deepseek_total_token = @$getDeepseekTokenUsage[0]->total_token ?? 0;

                //xAI - grok-v3
                $getXAIGrokV3TokenUsage = $UsageTracking->getXAITokenUsage($email, $maxTokenRange['max_start']);
                $grok_v3_total_token = @$getXAIGrokV3TokenUsage[0]->total_token ?? 0;

				return $this->response->setJSON([
						'success' => 1,
						'data' =>  json_encode($usages),
						'total' => $total,
						'date' => $maxTokenRange['max_start'],
						'gpt4o_total_token' => $gpt4o_total_token,
						'gpt_4o_max_token_usage' => $gpt_4o_max_token_usage,
						'dalle_total_image' => $dalle_total_image,
						'dalle_max_image' => $dalle_max_image,
						'claude_35_total_token' => $claude_35_total_token,
						'claude_35_max_token_limit' => $claude_35_max_token_limit,
						'claude_37_total_token' => $claude_37_total_token,
						'claude_37_max_token_limit' => $claude_37_max_token_limit,
						'o1_prompt' => $o1_prompt,
						'o1_total_prompt' => $o1_total_prompt,
						'members_total_o1_prompts' => $members_total_o1_prompts,
						'flux_prompt' => $flux_prompt,
						'flux_total_prompt' => $flux_total_prompt,
						'members_total_flux_prompts' => $members_total_flux_prompts,
						'deepseek_max_token_usage' => $deepseek_max_token_usage,
						'deepseek_total_token' => $deepseek_total_token,
                        'grok_v3_max_token_limit' => $grok_v3_max_token_limit,
                        'grok_v3_total_token' => $grok_v3_total_token
				]);
			}
			return $this->response->setJSON([
					'success' => 1,
					'data' => '',
					'total' => 0,
					'gpt4o_total_token' => 0,
					'gpt_4o_max_token_usage' => $gpt_4o_max_token_usage,
					'dalle_total_image' => $dalle_total_image,
					'dalle_max_image' => $dalle_max_image,
					'claude_35_total_token' => $claude_35_total_token,
					'claude_35_max_token_limit' => $claude_35_max_token_limit,
					'claude_37_total_token' => $claude_37_total_token,
					'claude_37_max_token_limit' => $claude_37_max_token_limit,
					'o1_prompt' => $o1_prompt,
					'o1_total_prompt' => $o1_total_prompt,
					'members_total_o1_prompts' => $members_total_o1_prompts,
					'flux_prompt' => $flux_prompt,
					'flux_total_prompt' => $flux_total_prompt,
					'members_total_flux_prompts' => $members_total_flux_prompts,
					'deepseek_max_token_usage' => $deepseek_max_token_usage,
					'deepseek_total_token' => $deepseek_total_token,
                    'grok_v3_max_token_limit' => $grok_v3_max_token_limit,
                    'grok_v3_total_token' => $grok_v3_total_token
			]);
		}

		private function getClaudeTokenInfo($subscription, $UsageTracking, $email, $maxTokenRange) {
			$claude_35_token_limit = 0;
			$claude_37_token_limit = 0;
			$claude_35_total_token = 0;
			$claude_37_total_token = 0;
			$isPlan = strtolower($subscription->plan_type);

			$claude_credit_limit = $this->getCacheRedis("ClaudeCreditLimit", "default", true);

			$getClaudeTokenUsage = $UsageTracking->getClaudeTokenUsage($email, $maxTokenRange['max_start'], "claude-3-5-sonnet@20240620");
			$claude_35_total_token = @$getClaudeTokenUsage[0]->total_token ?? 0;
			$getClaudeTokenUsage = $UsageTracking->getClaudeTokenUsage($email, $maxTokenRange['max_start'], "claude-3-7-sonnet@20250219");
			$claude_37_total_token = @$getClaudeTokenUsage[0]->total_token ?? 0;

			if (isset($claude_credit_limit[$isPlan])) {
					$claude_credit_limit = json_decode($claude_credit_limit[$isPlan], true);
					$prompt_model_limit = isset($claude_credit_limit["limit"]) ? intval($claude_credit_limit["limit"]) : 0;
					$prompt_model_list = isset($claude_credit_limit["model"]) ? $claude_credit_limit["model"] : [];

					if (in_array("claude-3-5-sonnet@20240620", $prompt_model_list)) {
							$claude_35_token_limit = $prompt_model_limit;
							$getClaudeTokenUsage = $UsageTracking->getClaudeTokenUsage($email, $maxTokenRange['max_start'], "claude-3-5-sonnet@20240620");
							$claude_35_total_token = @$getClaudeTokenUsage[0]->total_token ?? 0;
					}
					if (in_array("claude-3-7-sonnet@20250219", $prompt_model_list)) {
							$claude_37_token_limit = $prompt_model_limit;
							$getClaudeTokenUsage = $UsageTracking->getClaudeTokenUsage($email, $maxTokenRange['max_start'], "claude-3-7-sonnet@20250219");
							$claude_37_total_token = @$getClaudeTokenUsage[0]->total_token ?? 0;
					}
			}

			return [
					"claude_35_token_limit" => $claude_35_token_limit,
					"claude_35_total_token" => $claude_35_total_token,
					"claude_37_token_limit" => $claude_37_token_limit,
					"claude_37_total_token" => $claude_37_total_token
			];
	}

    private function getUserPlan() {
        if(btsessionHas('PLAN')) {
            return strtolower(btsessionGet('PLAN')->plan_type);
        }
        return "";
    }

    private function getUserPlanName() {
        if(btsessionHas('PLAN')) {
            return strtolower(btsessionGet('PLAN')->plan_name);
        }
        return "";
    }

    private function getPPG()
    {
        $req = $this->req;
        $ppg = isset($req['ppg']) ? $req['ppg'] : '';
        $from_upgrade = isset($req['from_upgrade']) ? $req['from_upgrade'] : '';
        $from_downgrade = isset($req['from_downgrade']) ? $req['from_downgrade'] : '';

        if($ppg!=''){
            $planModel = new PlanModel();

            switch($ppg) {
                case '40':
                    $id = explode(",", PPG40);
                    $plan = $planModel->getPricingInPlanID($id);
                    break;
                case '48':
                    $id = explode(",", PPG48);
                    $plan = $planModel->getPricingInPlanID($id);
                    break;
                case '58':
                    $id = explode(",", PPG58);
                    $plan = $planModel->getPricingInPlanID($id, 'desc');
                    break;
                case '59':
                    $id = explode(",", PPG59);
                    $plan = $planModel->getPricingInPlanID($id);
                    break;
                default:
                    $plan = $planModel->getPricingByPPG($ppg);
            }
        } else if(isset($req['ppg_id'])){
            $id = json_decode($req['ppg_id'], TRUE);
            if(is_int($id)) {
                $plan = btdbFindBy('PlanModel', 'plan_id', $id);
            } else {
                $planModel = new PlanModel();
                $current_plan = btsessionGet("PLAN");
                $ppg = $current_plan->ppg;
                $ppgWithToggle = ['97', '101', '109', '110'];

                if (($from_upgrade=='1' || $from_downgrade=='1') && !in_array($ppg, $ppgWithToggle)) {
                    $plan = $planModel->getPricingInPlanIDNoAnnual($id);
                }else{
                    switch ($ppg) {
                        case '97':
                            $plan = $this->getPpg97Plan($planModel, $id, $from_upgrade, $from_downgrade);
                        break;
                        case '101':
                        case '109':
                        case '110':
                            $plan = $this->getPpgPlan($from_upgrade, $from_downgrade);
                        break;
                        default:
                            $plan = $planModel->getPricingInPlanID($id);
                        break;
                    }
                }
            }
        } else {
            return $this->response->setJSON($this->res_error);
        }
        // log_message('debug', implode($plan));
        $currency = 'usd';
        $aKey = isset($req['akey']) ? $req['akey'] : '';
				$app_type = btflag('app', '');
        $utpm = isset($req['utpm']) ? $req['utpm'] : '';
				$mx = isset($req['mx']) ? $req['mx'] : '';
        if( ($app_type === 'pro' || $app_type === 'promax') && $aKey != 'downgrade' && $mx=='') {
            $ppgHasPro = false;
            $tmp_plan = array();
						$filter_plan = array('pro', 'promax');
						if($app_type === 'promax') $filter_plan = array('promax');
            if($plan['res']) {
                for($i = 0; $i < sizeof($plan['res']); $i++) {
                    if( in_array(strtolower($plan['res'][$i]->plan_type), $filter_plan) || strtolower($plan['res'][$i]->plan_type) == 'enterprise') {
                        array_push($tmp_plan, $plan['res'][$i]);
                        $ppgHasPro = true;
                    }
                }
                $currency = $plan['res'][0]->currency;
            }
            if(!$ppgHasPro) {
							$plan = $this->getProPlan($currency, $aKey);
						} else {
							$plan['res'] = $tmp_plan;
						}
        } elseif ($utpm === 'utpm') {
					$tmp_plan = array();
					$filter_plan = array('promax');
					if (!empty($plan['res'])) {
							for ($i = 0; $i < sizeof($plan['res']); $i++) {
								if (in_array(strtolower($plan['res'][$i]->plan_type), $filter_plan)) {
									array_push($tmp_plan, $plan['res'][$i]);
								}
							}
					}

					$plan['res'] = $tmp_plan;
				}

        if($plan['success'] && $plan['res']) {
            return $this->response->setJSON(['success' => $plan['success'], 'data' => $plan['res']]);
        }
        return $this->response->setJSON($this->res_error);
    }


    private function getPpg97Plan($planModel, $id, $from_upgrade, $from_downgrade)
    {
        $plan = '';
        if ($from_upgrade == '2' && btsessionGet("PLAN")->ppg == '97') {
            if (strtolower(btsessionGET("PLAN")->plan_type) === "basic") {
                $key = array_search("16", $id); // replace the $23.85
                if ($key !== false) {
                    $id[$key] = "72"; // $20 ppg52
                }
                $plan = $planModel->getPricingInPlanID($id);
            }

            if (strtolower(btsessionGET("PLAN")->plan_type) === "pro" ) {
                $key = array_search("62", $id); // Find the index of the value "62"
                if ($key !== false) {
                    unset($id[$key]); // Remove the value "62"
                    $id = array_values($id); // Reindex the array
                }
                $plan = $planModel->getPricingInPlanID($id);
            }

            if (strtolower(btsessionGET("PLAN")->plan_type) === "promax" ) {

                $key = array_search("62", $id); // Find the index of the value "62"
                if ($key !== false) {
                    unset($id[$key]); // Remove the value "62"
                    $id = array_values($id); // Reindex the array
                }
                log_message('debug', 'xxxxxxxxxxxxxxxxxx' . json_encode($id));
                $plan = $planModel->getPricingInPlanID($id);
            }
        } else if ($from_downgrade == '1' && btsessionGet("PLAN")->ppg == '97') {
            if (strtolower(btsessionGET("PLAN")->plan_type) === "basic") {
                $key = array_search("16", $id); // replace the $23.85
                if ($key !== false) {
                    $id[$key] = "72"; // $20 ppg52
                }
                $plan = $planModel->getPricingInPlanID($id);
            }
            if (strtolower(btsessionGET("PLAN")->plan_type) === "pro") {
                $key = array_search("16", $id); // replace the $23.85
                if ($key !== false) {
                    $id[$key] = "72"; // $20 ppg52
                }
                $plan = $planModel->getPricingInPlanID($id);
            }if (strtolower(btsessionGET("PLAN")->plan_type) === "promax" ) {
                $ppg97Basic = explode(",", PPGBASIC);
                $id = array_merge($id, $ppg97Basic);
                $key = array_search("16", $id); // replace the $23.85
                if ($key !== false) {
                    $id[$key] = "72"; // $20 ppg52
                }
                $plan = $planModel->getPricingInPlanID($id);
            }
        }
        return $plan;
    }

    private function getPpgPlan($from_upgrade, $from_downgrade) {
        $plan_types = ['Basic', 'Pro', 'ProMax'];
        $planModel = new PlanModel();
        $current_plan = btsessionGet("PLAN");
        $ppg = $current_plan->ppg;
        $current_plan_type = $current_plan->plan_type;
        $current_payment_interval = $current_plan->payment_interval;
        $plan = $planModel->getPricingByPPG($ppg);

        if ($plan['success']) {
            $plans = array_filter($plan['res'], function($plan) use ($from_upgrade, $from_downgrade, $plan_types, $current_plan_type, $current_payment_interval) {
                $plan_type = $plan->plan_type;
                $payment_interval = $plan->payment_interval;
                $current_plan_type_index = array_search($current_plan_type, $plan_types);
                $plan_type_index = array_search($plan_type, $plan_types);

                if ($plan_type == $current_plan_type && $payment_interval != $current_payment_interval) {
                    return ($from_upgrade == 2 && $current_payment_interval == 'Monthly') || ($from_downgrade == 1 && $current_payment_interval == 'Yearly');
                }

                if ($from_upgrade == 2) {
                    return $plan_type_index > $current_plan_type_index;
                } else if ($from_downgrade == 1) {
                    return $plan_type_index < $current_plan_type_index;
                }

                return false;
            });

            $plan['res'] = array_values($plans);
        }

        return $plan;
    }

    private function getProPlan($currency, $aKey = '')
    {
        $planModel = new PlanModel();
        $id = [16,62];
        switch( strtolower($currency) ) {
            case 'gbp':
                $id = [30];
                if($aKey == 'upgrade') array_push($id, 32);
                break;
            case 'eur':
                $id = [34];
                if($aKey == 'upgrade') array_push($id, 36);
                break;
            case 'brl':
                $id = [38];
                if($aKey == 'upgrade') array_push($id, 40);
                break;
            case 'sar':
                $id = [42];
                if($aKey == 'upgrade') array_push($id, 44);
                break;
            case 'aed':
                $id = [46];
                if($aKey == 'upgrade') array_push($id, 48);
                break;
            default:
                if($aKey == 'upgrade') array_push($id, 20);
                break;
        }
        $plan = $planModel->getPricingInPlanID($id);
        return $plan;
    }

    private function getPlan()
    {
        $req = $this->req;
        $plan_id = $req['plan_id'];

        $planModel = new PlanModel();
        $plan = $planModel->getPricingByPlanID($plan_id);

        if($plan['success'] && $plan['res']) {
            return $this->response->setJSON([ 'success' => $plan['success'], 'data' => $plan['res'][0] ]);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function getPayment()
    {
        $req = $this->req;

        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
        } else return $this->response->setJSON($this->res_error);
        $paymentModel = new PaymentModel();
        $payment = $paymentModel->getUserPayment($user_id);

        if($payment['success'] && $payment['res']) {
            return $this->response->setJSON(['success' => $payment['success'], 'data' => $payment['res']]);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function getSubscription()
    {
        $req = $this->req;
        $user = null;

        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
        } else return $this->response->setJSON($this->res_error);
        $accountModel = new AccountModel();
        $account = $accountModel->getSubscription($user_id);

        if($account['success'] && $account['res']) {
            $acct_user = $account['res'];
            foreach($acct_user as $key => $acct){
                $plan_type = strtolower($acct_user[$key]->plan_type);
                $price_per_member = strtolower($acct_user[$key]->price_per_member);
                $trial_days = strtolower($acct_user[$key]->trial_days);
                $start_date = strtolower($acct_user[$key]->start_date);

                if ($plan_type=='enterprise'){
                    $members = $acct_user[$key]->members;
                    $base_price = $acct_user[$key]->price;
                    $payment_interval = strtolower($acct_user[$key]->payment_interval);
                    if ($members>9){
                        $price = $base_price+(($members-9)*$price_per_member);
                        $acct_user[$key]->price = $price;
                        if ($payment_interval=='monthly'){
                            $acct_user[$key]->price_label = '$'.number_format($price).'/MONTH';
                        }else{
                            $acct_user[$key]->price_label = '$'.number_format($price).'/YEAR';
                        }
                    }
                }

                $acct_user[$key]->is_trial_end = 'yes';
                if ($trial_days!==null&&$trial_days!==''&&$trial_days>0){
                    $trial_end = date('Y-m-d', strtotime($start_date. ' + '.$trial_days.' days'));
                    $acct_user[$key]->is_trial_end = 'no';
                    if ($trial_end < $this->date_now){
                        $acct_user[$key]->is_trial_end = 'yes';
                    }
                }
            }

            return $this->response->setJSON(['success' => $account['success'], 'data' => $acct_user, 'date_now' => $this->date_now]);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function forgotPassword()
    {
        $req = $this->req;
        $email = $req['email'];
        //
        $user = btdbFindBy('UserModel', 'email', $req['email']);
        if ($user['success'] && $user['res']) {
            $token = btutilGenerateAccessToken($email);
            $update_data = [
                'user_id' => $user['res'][0]->user_id,
                'access_token' => $token,
            ];

            $res_db = btdbUpdate('UserModel', 'User', $update_data, 'update');
            if ($res_db['success']) {
                $email_data = [
                    'send_to' => $email,
                    'token' => $token,
                ];
                btemailSendResetPassword($email_data);

                $user_data = obj_to_arr($user['res'][0]);
                $event_data =  array_merge($user_data, $email_data)  ;
                if (array_key_exists("access_token", $event_data)) {
                    $event_data["access_token"] = $token;
                }
                logUserActivity('forgot-password', $event_data);

                return $this->response->setJSON($this->res_ok);
            }

            return $this->response->setJSON(['success' => 0, 'data' => $res_db]);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function updateAccount()
    {
        $req = $this->req;

        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
            $user_pid = btsessionGet('USER')->user_pid;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $email = $user->email;
            $mode = $user->mode;
            $user_pid = $user->user_pid;
        } else return $this->response->setJSON($this->res_error);

        $newEmail = $req['newemail'];
        $oldPassword = $req['password'];
        $newPassword = $req['newpassword'];
        $confirmPassword = $req['confpassword'];
        $isEmailChange = false;
        $isPasswordChange = false;

        $user_data = [
            'user_id' => $user_id,
            'access_token' => ''
        ];

        if($newPassword !== '') {
            if($newPassword != $confirmPassword) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Password did not match" ]]);
            $user_data['password'] = $this->wp_hasher->HashPassword($newPassword);
            $isPasswordChange = true;
        }

        if($email !== '' && $newEmail !== '') {
            $userModel = new UserModel();
            $userExist = $userModel->checkIfEmailExist($newEmail);
            if($userExist) {
                return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Email already exists" ]]);
            }
            $userappModel = new UserAppModel();
            // If User App New Email is already exists, delete it. It will overwrite for current user app.
            $userappModel->deleteByEmail($newEmail);
            $userappModel->updateEmail($email, $newEmail);
            $user_data['email'] = $newEmail;
            $isEmailChange = true;
        }

        $user = btdbFindBy('UserModel', 'email', $email);

        if($user['success'] && $user['res']) {
            if ($oldPassword !== '' && !$this->wp_hasher->checkPassword($oldPassword, $user['res'][0]->password)) {
                $res = [
                    'msg' => 'Incorrect password.',
                ];

                return $this->response->setJSON(['success' => 0, 'data' => $res]);
            }

            if(isset($user_data['email']) && $user_data['email']) {
                $account = btdbFindBy('AccountModel', 'user_id', $user_id);
                if( $account['success'] && $account['res'] ) {
                    if(!$user['res'][0]->orig_email) {
                        $user_data['orig_email'] = $email;
                    }
                    switch(strtolower($account['res'][0]->merchant)) {
                        case 'recurly':
                            $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
                            $payment = new BTPayment($paymenttype);
                            $payment->updateAccount(
                                $account['res'][0]->merchant_customer_id,
                                ['email' => $user_data['email']]
                            );
                            break;
                        case 'stripe':
                            $paymenttype = new BTStripe($this->stripe_api_key, $mode);
                            $payment = new BTPayment($paymenttype);
                            $payment->updateAccount(
                                $account['res'][0]->merchant_customer_id,
                                ['email' => $user_data['email']]
                            );
                            break;
                        default:
                            // do nothing
                            break;
                    }
                }
            }
            $update = btdbUpdate('UserModel', 'User', $user_data, 'update');

            if($update['success']) {
                if($isEmailChange) logUserActivity('change-email');
                if($isPasswordChange) logUserActivity('change-password');
                $data = btdbFindBy('UserModel', 'email', $newEmail);
                if ($data['success'] && $data['res']) {
                    btsessionSetAccount($data['res'][0], 1);
                    $this->setAuthCookies($newEmail, $data);
                }

                // MAILERLITE
                $mailertype = new BTHubSpot($this->mailer_key);
                $mailertool = new BTMailer($mailertype);
                if(!$this->isEmailTest($newEmail) && $mailertool->checkExist($email)) {
                    $mailertool->updateSubscriber($email, ["email"=>$newEmail]);
                }

                $url = getenv('API_URL') . '/app/updateChatUser';
                $ch_data = [
                    'user_id' => $user_id,
                    'email' => $email,
                    'user_pid' => $user_pid
                ];
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($ch_data));
                $response = curl_exec($ch);
                curl_close($ch);

                $this->destroySession();
                return $this->response->setJSON($this->res_ok);
            }

            return $this->response->setJSON(['success' => 0, 'data' => $update]);
        }

        return $this->response->setJSON($this->res_error);
    }

    private function changePassword()
    {
        $req = $this->req;

        if(isset($req['access_token'])) {
            $password = $req['password'];
            $confirmPassword = $req['confpassword'];
            if($password != $confirmPassword) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Password did not match" ]]);

            $user = btdbFindBy('UserModel', 'access_token', $req['access_token']);
            if($user['success'] && $user['res']) {
                $user_data = [
                    'user_id' => $user['res'][0]->user_id,
                    'password' => $this->wp_hasher->HashPassword($password),
                    'access_token' => ''
                ];
                $update = btdbUpdate('UserModel', 'User', $user_data, 'update');

                if($update['success']) {
                  btdbDelete('UserIpModel', ['email'], [$user['res'][0]->email], 'new');
                  return $this->response->setJSON($this->res_ok);
                }

                return $this->response->setJSON(['success' => 0, 'data' => $update]);
            }
            return $this->response->setJSON($this->res_error);
        }
        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $email = $user->email;
        } else return $this->response->setJSON($this->res_error);

        $oldPassword = $req['password'];
        $newPassword = $req['newpassword'];
        $confirmPassword = $req['confpassword'];

        if($newPassword != $confirmPassword) return $this->response->setJSON(['success' => 0, 'data' => [ 'msg' => "Password did not match" ]]);

        $user = btdbFindBy('UserModel', 'email', $email);

        if($user['success'] && $user['res']) {
            if (!$this->wp_hasher->checkPassword($oldPassword, $user['res'][0]->password)) {
                $res = [
                    'msg' => 'Incorrect password.',
                ];

                return $this->response->setJSON(['success' => 0, 'data' => $res]);
            }
            $user_data = [
                'user_id' => $user['res'][0]->user_id,
                'password' => $this->wp_hasher->HashPassword($newPassword),
                'access_token' => ''
            ];
            $update = btdbUpdate('UserModel', 'User', $user_data, 'update');

            $event_data = obj_to_arr($user['res'][0]);
            if (array_key_exists("access_token", $event_data)) {
                $event_data["access_token"] = '';
            }
            logUserActivity('change-password', $event_data);

            if($update['success']) {
              btdbDelete('UserIpModel', ['email'], [$user['res'][0]->email], 'new');
              return $this->response->setJSON($this->res_ok);
            }

            return $this->response->setJSON(['success' => 0, 'data' => $update]);
        }

        return $this->response->setJSON($this->res_error);
    }

    private function deleteAccount()
    {
        $req = $this->req;
        if(btsessionIsUserLoggedIn()) {
            btSessionSetUserSubscription(btsessionGET("USER"), 1);

            if(btsessionHAS("ACCOUNT")) {
                $merchant = btsessionGET("ACCOUNT")->merchant;
                $mode = btsessionGET("USER")->mode;
                switch(strtolower($merchant)) {
                    case 'recurly':
                        $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
                        $payment = new BTPayment($paymenttype);
                        $result = $payment->cancelAllSubscription(btsessionGET("ACCOUNT")->merchant_customer_id);
                        break;
                    case 'stripe':
                        $paymenttype = new BTStripe($this->stripe_api_key, $mode);
                        $payment = new BTPayment($paymenttype);
                        $result = $payment->cancelAllSubscription(btsessionGET("USER")->email);
                        break;
                    case 'paddle':
                        $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
                        $payment = new BTPayment($paymenttype);
                        $result = $payment->cancelSubscription(btsessionGET("ACCOUNT")->merchant_subscription_id);
                        break;
                    case 'fastspring':
                        $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
                        $payment = new BTPayment($paymenttype);
                        $result = $payment->cancelSubscription(btsessionGET("ACCOUNT")->merchant_subscription_id);
                        break;
                    case 'paypal':
                        $paymenttype = new BTPayPal($this->paypal_api_key, $mode);
                        $payment = new BTPayment($paymenttype);
                        $result = $payment->cancelSubscription(btsessionGET("ACCOUNT")->merchant_subscription_id);
                        break;
                    case 'cardtransaction':
                        $result = $this->sctCancelAllSubscription(btsessionGET("ACCOUNT")->merchant_customer_id, $mode);
                        break;
                }
            }

            $userappModel = new UserAppModel();
            $userappModel->deleteByEmail(btsessionGet('USER')->email);
            // MAILERLITE
            $mailertype = new BTHubSpot($this->mailer_key);
            $mailertool = new BTMailer($mailertype);
            $mailertool->deleteSubscriber(btsessionGet('USER')->email);
            btemailSendDeleteAccountEmail([
                'send_to' => btsessionGet('USER')->email
            ]);

            if (btsessionGet("ACCOUNT") && btsessionGet("ACCOUNT")->account_id) {
                $account_data = [
                    'account_id' => btsessionGet("ACCOUNT")->account_id,
                    'status' => 'inactive',
                    'cancelled_at' => $this->date_now
                ];
                btdbUpdate('AccountModel', 'Account', $account_data, 'update');
            }

            $event_data = [
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'first_name' => btsessionGet('USER')->first_name,
                'last_name' => btsessionGet('USER')->last_name,
            ];
            logUserActivity('deleteAccount', $event_data);
            btdbDelete('UserModel', ['user_id'], [btsessionGet('USER')->user_id], 'new');

						/**Delete enterprise members if theres any**/
						$enterprise_members = btdbFindBy('UserModel', 'ent_parent_user_id', btsessionGET("USER")->user_id);
						foreach ($enterprise_members['res'] as $value) {
							$member_user_id = $value->user_id;
							btdbDelete('UserModel', ['user_id'], [$member_user_id], 'new');
						}

						$this->destroySession();

            return $this->response->setJSON($this->res_ok);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function checkAccessLink()
    {
        $req = $this->req;
        if( !isset($req['tk']) || !isset($req['email']) ) return $this->response->setJSON($this->res_error);
        $email = $req['email'];
        $tk = $req['tk'];

        $user = btdbFindBy('UserModel', ['email', 'access_token'], [$email, $tk]);
        if($user['success'] && $user['res']) return $this->response->setJSON($this->res_ok);
        return $this->response->setJSON($this->res_error);
    }

    private function createSubscriptionPayPal(){
      $req = $this->req;
      $user = null;
      if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
      if(btsessionIsUserLoggedIn()) {
          $user_id = btsessionGet('USER')->user_id;
          $user_pid = btsessionGet('USER')->user_pid;
          $email = btsessionGet('USER')->email;
          $mode = btsessionGet('USER')->mode;
      } else if($user && $user['success']) {
          $user = $user['data'];
          $user_id = $user->user_id;
          $user_pid = $user->user_pid;
          $email = $user->email;
          $mode = $user->mode;
      } else return $this->response->setJSON($this->res_error);
      $plan_id = $req['plan_id'];
      $upgrade = isset($req['upgrade'])? $req['upgrade'] : '';
      $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
      $uid = btutilGenerateHashId([$email, $mode]);
      $mode = $mode ? $mode : FLAG_MODE_LIVE;
      $account_pid = btutilGenerateHashId([$uid]);
      $acct = btdbFindBy('AccountModel', ['user_id', 'status'], [$user_id, 'active']);
      if($acct['success'] && $acct['res']) {
        if ($upgrade==''){
            return $this->response->setJSON(['success' => 0, 'data' => ['msg' => 'You already have a current subscription.', 'link' => ""]]);
        }
      }

      if($plan['success'] && $plan['res']) {
        $paymenttype = new BTPayPal($this->paypal_api_key, $mode);
        $payment = new BTPayment($paymenttype);
        $base_url = getenv("app.baseURL");

        $payment_interval = strtoupper($plan['res'][0]->payment_interval);
        $plan_type = strtoupper($plan['res'][0]->plan_type);
        $price = $plan['res'][0]->price;
        $trial_price = $plan['res'][0]->trial_price;
        $trial_days = $plan['res'][0]->trial_days;
        $subscription_desc = $plan['res'][0]->label;
        $currency = strtoupper($plan['res'][0]->currency);
        $plan_public_id = $plan['res'][0]->plan_pid;
        $plan_id = $plan['res'][0]->plan_id;

        if ($trial_days==''){
          $trial_days = '0';
        }
        if ($trial_price==''){
          $trial_days = '0';
        }

        $params = [
          'plan' => $payment_interval,
          'subscription_type' => $plan_type,
          'price' => $price,
          'currency' => $currency,
          'plan_public_id' => $plan_id,
          'trial_price' => $trial_price,
          'trial_days' => $trial_days,
          'subscription_name' => 'AI Pro Subscription',
          'subscription_desc' => $subscription_desc,
          'agreement_return_url' => $base_url.'/api/execute-paypal-agreement/success/'.$user_pid.'/'.$plan_id,
          'agreement_cancel_url' => $base_url.'/api/execute-paypal-agreement/fail/'.$user_pid.'/'.$plan_id
        ];

        $result = $payment->createSubscription($params);

        if ($result['status']=="1"){
            return $this->response->setJSON(['success' => 1, 'data' => ['msg' => '', 'link' => $result['data']]]);
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => ['msg' => $result['message'], 'link' => ""]]);
        }
      }
    }

    public function executePayPalAgreement($status,$user_pid,$plan_id){
      $status = strtolower($status);
      $token = isset($_GET['token']) ? trim($_GET['token']) : '';
      $mode = 'live';
      $user_id = '';
      $email = '';

			$aiwp_url = "https://ai-pro.org";
			if(strpos(base_url(), "staging")) {
				$aiwp_url = "https://staging.ai-pro.org";
			}

            if(strpos(base_url(), "uat")) {
				$aiwp_url = "https://uat.ai-pro.org";
			}

			switch(btflag('n_flow', '')) {
				case 'landing':
					$base_url = $aiwp_url . "/landing";
					break;
                case 'image':
					$base_url = $aiwp_url . "/landing-image";
					break;
				case 'imagegen':
					$base_url = $aiwp_url . "/landing-imagegen";
					break;
				case 'aiart':
					$base_url = $aiwp_url . "/landing-aiart";
					break;
				default:
					$base_url = getenv("app.baseURL");
					break;
			}

      if ($status!='success'){
        header("Location: ".$base_url."/pay?error=Payment Failed");
        exit;
      }

      if ($token==''){
        header("Location: ".$base_url."/pay?error=Payment Failed");
        exit;
      }

      if ($user_pid==''){
        header("Location: ".$base_url."/pay?error=Payment Failed");
        exit;
      }

      $user = btdbFindBy('UserModel', 'user_pid', $user_pid);
      if($user['success'] && $user['res']) {
        $user_id = $user['res'][0]->user_id;
        $mode = $user['res'][0]->mode;
        $email = $user['res'][0]->email;
      }else{
        header("Location: ".$base_url."/pay?error=User account not found");
        exit;
      }

      $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
      if(!$plan['success'] || !$plan['res']) {
        header("Location: ".$base_url."/pay?error=Unable to find plan");
        exit;
      }

      $paymenttype = new BTPayPal($this->paypal_api_key, $mode);
      $payment = new BTPayment($paymenttype);
      $params = [
        'token' => $token
      ];
      $result = $payment->executeAgreement($params);
      if ($result['status']!="1"){
        header("Location: ".$base_url."/pay?error=".$result['message']);
        exit;
      }

      $account = btdbFindBy('AccountModel', 'merchant_subscription_id', $result['data']['subscription_id']);
      if($account['success'] && $account['res']) {
        header("Location: ".$base_url."/thankyou");
        exit;
      }

      $isTrial = $plan['res'][0]->trial_days ? true : false;
      $uid = btutilGenerateHashId([$email, $mode]);
      $account_pid = btutilGenerateHashId([$uid]);
      $max_tokens = $plan['res'][0]->max_tokens ? $plan['res'][0]->max_tokens : '';

      $price = $isTrial ? ($plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : 0) : $plan['res'][0]->price;
      $account_data = [
          'account_pid' => $account_pid,
          'user_id' => $user_id,
          'plan_id' => $plan_id,
          'merchant' => 'PayPal',
          'merchant_customer_id' => $token,
          'merchant_subscription_id' => $result['data']['subscription_id'],
          'trial_end' => $result['data']['current_sub_end'],
          'start_date' => $result['data']['current_sub_start'],
          'end_date' => $result['data']['current_sub_end'],
          'cancelled_at' => 0,
					'max_tokens' => $max_tokens
      ];

      // for user log
      $accountModel = new AccountModel();
      $account = $accountModel->getActiveSubscription($user_id);
      // user log

      $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');

      if ($result['data']['invoice_number']!=''){
        $insert_data = [
            'acctpmt_pid' => btutilGenerateHashId([$uid, $acct['_']['insert_id']]),
            'account_id' => $acct['_']['insert_id'],
            'user_id' => $user_id,
            'amount' => $price,
            'currency' => $plan['res'][0]->currency,
            'charge_id' => $result['data']['invoice_number'],
            'merchant' => 'PayPal',
            'mode' => $mode
        ];
        if ($price!==''){
            btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
        }
      }

        // user log
        if (isset($insert_data)) {
            $event_data = array_merge($account_data, $insert_data);
        } else {
            $event_data = $account_data;
        }
        if ($account['success'] == 1  && count($account['res']) <= 0) {
            $event = 'create-subscription';
        } else if ($account['success'] == 1  && count($account['res']) > 0) {
            $event = 'update-subscription';
        }

        $plan_type = strtoupper($plan['res'][0]->plan_type);
        if (strtoupper($plan['res'][0]->plan_type)==='PROMAX'){
            if (strtoupper($plan['res'][0]->payment_interval)=='YEARLY'){
                $plan_type = 'PRO MAX ANNUAL';
            }
        }
        $event_data = array_merge($event_data, ['plan_type' => $plan_type]);
				$event_data['user_pid'] = $user_pid;

        $event = btflag_cookie('reactivateSubscription', $event);
        logUserActivity($event, $event_data);
        // user log

      $this->doAfterPayment([
          'user_id' => $user_id,
          'plan' => $plan['res'][0],
          'email' => $email
      ]);

			$plan_name = $plan['res'][0]->label;
			$plan_name = str_replace(" ","",$plan_name);

			//User should only have one subcription. Cancel the other subscriptions.
			$this->cancelOtherExistingSubscription($user_id,$acct['_']['insert_id']);
        if(btsessionHas('SurveyData')) {
            $user_data = [
                'user_id' => btsessionGet("USER")->user_id,
                'survey_data' => btsessionGet('SurveyData')->init_session_data
            ];

            btdbUpdate('UserModel', 'User', $user_data, 'update');
            btsessionGet('USER')->survey_data = btsessionGet('SurveyData')->init_session_data;

            header("Location: ".$base_url."/thankyou");
            exit;
        }else{
            header("Location: ".$base_url."/thankyou/?plan=".$plan_name);
            exit;
        }
    }

    public function executeStripeDirectLink($upid=''){
        $base_url = getenv("app.baseURL");
        $checkout = btsessionHas('checkout_sesson_id') ? btsessionGet('checkout_sesson_id') : '';
        $checkout_id = isset($checkout->id) ? $checkout->id : '';
        $arr_checkout = explode("|",$checkout_id);
        $checkout_sesson_id = isset($arr_checkout[0]) ? $arr_checkout[0] : '';
        $plan_id = isset($arr_checkout[1]) ? $arr_checkout[1] : '';
        $user_pid = isset($arr_checkout[2]) ? $arr_checkout[2] : '';

        if ($checkout_sesson_id=='' || $plan_id == '' || $user_pid == ''){
            echo 'Invalid Session.';
            exit;
        }

        $user = btdbFindBy('UserModel', 'user_pid', $user_pid);
        if($user['success'] && $user['res']) {
          $user_id = $user['res'][0]->user_id;
          $mode = $user['res'][0]->mode;
          $email = $user['res'][0]->email;
        }else{
            echo 'User account not found.';
            exit;
        }

        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if(!$plan['success'] || !$plan['res']) {
            echo 'Invalid Plan.';
            exit;
        }

        $paymenttype = new BTStripe($this->stripe_api_key, $mode);
        $payment = new BTPayment($paymenttype);

        $params = [
          'checkout_id' => $checkout_sesson_id
        ];
        $result = $payment->retrieveCheckoutSession($params);


        if ($result['status'] !='1'){
            echo 'Invalid Checkout Session.';
            exit;
        }

        $result = $result['data'];
        $customer = isset($result->customer) ? $result->customer : '';
        $subscription_id = isset($result->subscription) ? $result->subscription : '';
        $invoice_number = isset($result->invoice) ? $result->invoice : '';
        $expires_at = isset($result->expires_at) ? $result->expires_at : '';

        $params = [
            'subscription_id' => $subscription_id
        ];
        $subscription = $payment->getSubscriptionDetails($params);
        if ($subscription['status'] !='1'){
            echo 'Subscription Details Not Found.';
            exit;

        }
        $subscription = $subscription['data'];
        $period_start = isset($subscription['start_date']) ? $subscription['start_date'] : '';
        $period_end = isset($subscription['end_date']) ? $subscription['end_date'] : '';
        $payment_interval = isset($subscription['payment_interval']) ? $subscription['payment_interval'] : '';

        if ($payment_interval=='year'){
            $options = isset($plan['res'][0]->options) ? $plan['res'][0]->options : '';
            if ($options!==''){
                $options = json_decode($options);
                $upgrade_id = isset($options->upgrade_id) ? $options->upgrade_id : '';
                if ($upgrade_id!=''){
                    $plan_id = $upgrade_id;
                    $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
                }
            }
        }

        $params = [
            'invoice_number' => $invoice_number
        ];
        $invoice = $payment->getInvoice($params);
        $invoice = isset($invoice['data']) ? $invoice['data'] : [];
        $charge_id = isset($invoice->charge) ? $invoice->charge : '';

        $isTrial = $plan['res'][0]->trial_days ? true : false;
        $uid = btutilGenerateHashId([$email, $mode]);
        $account_pid = btutilGenerateHashId([$uid]);
        $max_tokens = $plan['res'][0]->max_tokens ? $plan['res'][0]->max_tokens : '';

        $price = $isTrial ? ($plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : 0) : $plan['res'][0]->price;
        $account_data = [
            'account_pid' => $account_pid,
            'user_id' => $user_id,
            'plan_id' => $plan_id,
            'merchant' => 'Stripe',
            'merchant_customer_id' => $customer,
            'merchant_subscription_id' => $subscription_id,
            'trial_end' => 0,
            'start_date' => $period_start,
            'end_date' => $period_end,
            'cancelled_at' => 0,
            'max_tokens' => $max_tokens
        ];

        if ($isTrial){
            $account_data['trial_end'] = $period_end;
        }
        $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');
        $acct_account_insert_id = $acct['_']['insert_id'];

        $insert_data = [
            'acctpmt_pid' => btutilGenerateHashId([$uid, $acct_account_insert_id]),
            'account_id' => $acct_account_insert_id,
            'user_id' => $user_id,
            'amount' => $price,
            'currency' => $plan['res'][0]->currency,
            'charge_id' => $charge_id,
            'merchant' => 'Stripe',
            'mode' => $mode
        ];
        btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');

        // user log
        if (isset($insert_data)) {
            $event_data = array_merge($account_data, $insert_data);
        } else {
            $event_data = $account_data;
        }
        $event = 'create-subscription';

        $plan_type = strtoupper($plan['res'][0]->plan_type);
        if (strtoupper($plan['res'][0]->plan_type)==='PROMAX'){
            if (strtoupper($plan['res'][0]->payment_interval)=='YEARLY'){
                $plan_type = 'PRO MAX ANNUAL';
            }
        }
        $event_data = array_merge($event_data, ['plan_type' => $plan_type]);

        $event = btflag_cookie('reactivateSubscription', $event);
        logUserActivity($event, $event_data);
        // user log

        $this->doAfterPayment([
            'user_id' => $user_id,
            'plan' => $plan['res'][0],
            'email' => $email
        ]);

        $plan_name = $plan['res'][0]->label;
        $plan_name = str_replace(" ","",$plan_name);

        //User should only have one subcription. Cancel the other subscriptions.
        $this->cancelOtherExistingSubscription($user_id,$acct_account_insert_id);
        header("Location: ".$base_url."/thankyou/?plan=".$plan_name);

        $prefix = '';

        if (strpos($base_url, 'staging') !== false) {
            $prefix = 'staging.';
        } else if (strpos($base_url, 'dev') !== false) {
            $prefix = 'dev.';
        } else if (strpos($base_url, 'uat') !== false) {
            $prefix = 'uat.';
        }

        $redirect = "https://{$prefix}ai-pro.org/thankyou/?plan=$plan_name";

        header("Location: $redirect");
        exit;
    }

    private function createSubscription($pmt = 'Recurly')
    {
        $req = $this->req;
        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
            $user_ip = btsessionGet('USER')->ip_address;
						$user_pid = btsessionGet('USER')->user_pid;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
						$user_pid = $user->user_pid;
            $email = $user->email;
            $mode = $user->mode;
            $user_ip = $user->ip_address;
        } else return $this->response->setJSON($this->res_error);
        $plan_id = $req['plan_id'];
        $planModel = new PlanModel();
        $plan = $planModel->getPricingByPlanID($plan_id);
        $uid = btutilGenerateHashId([$email, $mode]);
        $mode = $mode ? $mode : FLAG_MODE_LIVE;
        $account_pid = btutilGenerateHashId([$uid]);
        $acct = btdbFindBy('AccountModel', 'user_id', $user_id);
        if($acct['success'] && $acct['res']) {
            $account_data['account_id'] = $acct['res'][0]->account_id;
            $account_pid = $acct['res'][0]->account_pid;
        }

        if($plan['success'] && $plan['res']) {
            $cc = isset($req['cc']) ? $req['cc'] : '';
            $ccmonth = isset($req['ccmonth']) ? $req['ccmonth'] : '';
            $ccyr = isset($req['ccyr']) ? $req['ccyr'] : '';
            $cvv = isset($req['cvv']) ? $req['cvv'] : '';
            $postal = isset($req['postal']) ? $req['postal'] : '';
            $phone = isset($req['phone']) ? $req['phone'] : '';
            $gateway = isset($req['gateway']) ? $req['gateway'] : '';
            $isTrial = $plan['res'][0]->trial_days ? true : false;
            $max_members = $plan['res'][0]->max_members ? $plan['res'][0]->max_members : '';
            $account_code = 'app-' . $account_pid . '-' . $user_id;
            $payment_menthod_id = isset($req['payment_menthod_id']) ? $req['payment_menthod_id'] : '';
            $ppg = $plan['res'][0]->ppg;
            $adid = btflag('adid', '');
            $via = btflag('via', '');
            $client_reference_id = isset($req['client_reference_id']) ? $req['client_reference_id'] : '';
            $plan_type = $plan['res'][0]->plan_type ? strtolower($plan['res'][0]->plan_type) : '';
            $additional_member = 0;

            if ($ppg!=''){
                $ppg = 'PPG-'.$ppg;
            }

            $gateway_code = '';
            if ($gateway=='2'){
                if (strtolower($mode)=='test'){
                    $gateway_code = getenv('RECURLY_GATEWAYCODE_2_TEST');
                }else{
                    $gateway_code = getenv('RECURLY_GATEWAYCODE_2_LIVE');
                }
            }

            $cc_raw = str_replace("-","",$cc);
            $cc_number_encrypted = sha1($cc_raw);
            $first_six = substr($cc_raw, 0, 6);
            $last_four = substr($cc_raw, -4);

            $cc_num = '';
            if ($first_six!='' && $last_four!=''){
              $cc_num = $first_six.'****'.$last_four;
            }

						$cc_num64 = base64_encode($cc_num);

            if (strtolower($pmt) != 'fastspring'){
                if ($this->checkNegative($email,$cc_number_encrypted) === 1){
                    return $this->response->setJSON(['success' => 0, 'data' => ['msg' => 'Credit card details blocked.']]);
                }

                $isProxy = $this->proxyCheck($mode);
                $isHoneypot = btflag('ishoneypot');

                if ($isProxy['is_proxy'] == '1' || $isHoneypot == 'yes'){

                    if ($isHoneypot === 'yes') {

                        $isProxy['data']['is_honeypot'] = 'yes';
                    }
                    $data_proxy = json_encode($isProxy['data']);
                    $user_data = [
                        'user_id' => $user_id,
                        'is_fraud' => $data_proxy
                    ];
                    btdbUpdate('UserModel', 'User', $user_data, 'update');
                    btflag_set('isProxy','yes');

                    return $this->response->setJSON(['success' => 1, 'data' => ['msg' => '', 'redir' => 'ty']]);
                }
            }

			//Check fingerprint - start
			$pos_email = strpos($email, 'test_LHOFOjUBCs_');
			if ($pos_email===false){
				$fingerprint_data = btdbFindBy('UserLogModel', ['user_pid','event'], [$user_pid,'register-fingerprint']);
				if($fingerprint_data['success'] && $fingerprint_data['res']) {
					$fingerprint_data = isset($fingerprint_data['res'][0]->event_data) ? $fingerprint_data['res'][0]->event_data : '';

					if ($fingerprint_data!==''){
						$fingerprint_data = json_decode($fingerprint_data, true);
						$fBrowser = isset($fingerprint_data['browser']) ? $fingerprint_data['browser'] : '';
						$fCookie = isset($fingerprint_data['http-headers-cookie']) ? $fingerprint_data['http-headers-cookie'] : '';
						$isFingerFraud = false;

						if ($fBrowser == '' && $fCookie == ''){
							$isFingerFraud = true;
						}

						$pos = strpos(strtolower($fBrowser), 'unknown');
						if ($pos !== false) {
							$isFingerFraud = true;
						}

						if ($isFingerFraud){
							$user_data = [
								'user_id' => $user_id,
								'is_fraud' => '{"fraud_via_fingerprint":"yes"}'
							];
							btdbUpdate('UserModel', 'User', $user_data, 'update');
							btflag_set('isProxy','yes');

							return $this->response->setJSON(['success' => 1, 'data' => ['msg' => '', 'redir' => 'ty']]);
						}
					}
				}
			}
			//Check fingerprint - end

            $user_ip = (is_null($user_ip) || $user_ip == 'null') ? '' : $user_ip;

            $pos = strpos($user_ip, ':');
            if ($pos !== false) {
                $user_ip = '';
            }

            if(strtolower($pmt) == 'recurly') {
                $params = [
                'account_code' => $account_code,
                'email' => $email,
                'first_name' => isset($req['first_name']) ? $req['first_name'] : btsessionGet('USER')->first_name,
                'last_name' => isset($req['last_name']) ? $req['last_name'] : btsessionGet('USER')->last_name,
                'cc_number' => $cc,
                'cc_exp_month' => $ccmonth,
                'cc_exp_year' => $ccyr,
                'cc_cvv' => $cvv,
                'postal_code' => $postal,
                'phone' => $phone,
                'gateway_code' => $gateway_code,
                'product_id' => $plan['res'][0]->recurly_plan_id ? $plan['res'][0]->recurly_plan_id : '',
                'campaign' => $adid,
                'subchannel' => $ppg,
                'user_ip' => $user_ip
                ];
                $charge_id = 'invoice_number';

                $members = isset($req['members']) ? $req['members'] : '10';
                if ($plan_type=='enterprise' && $members > 10){
                    $additional_member = $members-10;
                    $params['add_ons'] = [
                        'code' => 'additional_member',
                        'quantity' => $additional_member,
                      ];
                }

                $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
                $payment = new BTPayment($paymenttype);
                $result = $payment->createSubscription($params);

                if ($plan_type == 'enterprise') {
                    btflag_set('pmt', 'rec');
                }
            } else if(strtolower($pmt) == 'stripe-apple-google') {
                $stripe_plan_id = $plan['res'][0]->stripe_plan_id ? $plan['res'][0]->stripe_plan_id : '';
                $stripe_plan_id = json_decode($stripe_plan_id, true);
                $cus_name = isset($req['cus_name']) ? $req['cus_name'] : '';

                if ($mode=='test'){
                  $stripe_plan =  $stripe_plan_id["test"];
                }else{
                  $stripe_plan =  $stripe_plan_id["live"];
                }

                $payment_interval = $plan['res'][0]->payment_interval ? $plan['res'][0]->payment_interval : '';
                $payment_interval = strtolower($payment_interval);
                $plan_type = 'M';

                if ($payment_interval=='yearly'){
                  $plan_type = 'Y';
                }

                $params = [
                'email' => $email,
                'payment_menthod_id' => $payment_menthod_id,
                'cus_name' => $cus_name,
                'phone' => '',
                'cc_number' => '',
                'cc_exp_month' => '',
                'cc_exp_year' => '',
                'cc_cvv' => '',
                'plan_trial_days' => $plan['res'][0]->trial_days ? $plan['res'][0]->trial_days : '',
                'plan_trial_price' => $plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : '',
                'plan_currency' => $plan['res'][0]->currency ? $plan['res'][0]->currency : 'USD',
                'plan_type' => $plan_type,
                'plan_interval' => '1',
                'plan_price' => $plan['res'][0]->price ? $plan['res'][0]->price : '',
                'stripe_product_id' => $stripe_plan,
                'meta_data'=>['user_id'=>$user_id,'source'=>'app-ai', 'referral'=>$client_reference_id, 'rewardful_via'=>$via]
                ];
                $charge_id = 'payment_intent_id';

                $paymenttype = new BTStripe($this->stripe_api_key, $mode);
                $payment = new BTPayment($paymenttype);
                $result = $payment->createSubscription($params);

                if($result['status'] === "1") {
                    $account_code = $result['data']['customer_id'];
                }
            } else if(strtolower($pmt) == 'fastspring') {

                $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
                $payment = new BTPayment($paymenttype);
                $result = $payment->getSubscriptionDetails(isset($req['sub_id']) ? $req['sub_id'] : '');


                if ($result['status']=='1'){
                    $end_date = isset($result['data']->nextDisplayISO8601) ? $result['data']->nextDisplayISO8601 : '';
                    $end_date = $end_date.' 00:00:00';

                    $account_code = isset($req['account']) ? $req['account'] : '';
                    $cc_raw = '';
                    $cc_num = '';
                    $last_four = '';
                    $charge_id = 'fastspring';

                    $result = [ 'status'=>'1',
                                'data'=>
                                [
                                    'fastspring' => $req['reference'],
                                    'subscription_id' => isset($req['sub_id']) ? $req['sub_id'] : '',
                                    'current_sub_start' => $this->date_now,
                                    'current_sub_end' => $end_date
                                ]
                            ];
                }

            } else {
                $stripe_plan_id = $plan['res'][0]->stripe_plan_id ? $plan['res'][0]->stripe_plan_id : '';
                $stripe_plan_id = json_decode($stripe_plan_id, true);

                if ($mode=='test'){
                  $stripe_plan =  $stripe_plan_id["test"];
                }else{
                  $stripe_plan =  $stripe_plan_id["live"];
                }

                $payment_interval = $plan['res'][0]->payment_interval ? $plan['res'][0]->payment_interval : '';
                $payment_interval = strtolower($payment_interval);
                $plan_type = 'M';

                if ($payment_interval=='yearly'){
                  $plan_type = 'Y';
                }

                $first_name = isset($req['first_name']) ? $req['first_name'] : btsessionGet('USER')->first_name;
                $last_name = isset($req['last_name']) ? $req['last_name'] : btsessionGet('USER')->last_name;

				$is3ds = 'no';
				if (btflag('pmt') == 'pay3d'){
					$is3ds = 'yes';
				}

                $params = [
                'email' => $email,
                'cus_name' => $first_name.' '.$last_name,
                'phone' => isset($req['phone']) ? $req['phone'] : '',
                'cc_number' => $cc,
                'cc_exp_month' => $ccmonth,
                'cc_exp_year' => $ccyr,
                'cc_cvv' => $cvv,
                'plan_trial_days' => $plan['res'][0]->trial_days ? $plan['res'][0]->trial_days : '',
                'plan_trial_price' => $plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : '',
                'plan_currency' => $plan['res'][0]->currency ? $plan['res'][0]->currency : 'USD',
                'plan_type' => $plan_type,
                'plan_interval' => '1',
                'plan_price' => $plan['res'][0]->price ? $plan['res'][0]->price : '',
                'stripe_product_id' => $stripe_plan,
                'meta_data'=>['user_id'=>$user_id,'source'=>'app-ai', 'referral'=>$client_reference_id, 'rewardful_via'=>$via],
				'return_url' => base_url().'api/execute-threed-secure-new/?plan='.$plan['res'][0]->label.'&plan_id='.$plan['res'][0]->plan_id.'&c1='.$cc_num64.'&c2='.$cc_number_encrypted,
                '3d' => $is3ds
                ];
                $charge_id = 'payment_intent_id';

                $paymenttype = new BTStripe($this->stripe_api_key, $mode);
                $payment = new BTPayment($paymenttype);
                $result = $payment->createSubscription($params);

				if($result['status'] === "1") {
					if (isset($result['redirect']) && $result['redirect']!==''){
						return $this->response->setJSON(['success' => 1, 'data' => '', 'redirect'=>$result['redirect']]);
					}
                    $account_code = $result['data']['customer_id'];
                }
            }

            if ($pmt=='stripe-apple-google'){
                $pmt = 'stripe';
            }

            if($result['status'] === "1") {
                $price = $isTrial ? $plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : 0 : $plan['res'][0]->price;
                $price_per_member = $plan['res'][0]->price_per_member ? $plan['res'][0]->price_per_member : 0;

                $account_data = [
                    'account_pid' => $account_pid,
                    'user_id' => $user_id,
                    'plan_id' => $plan_id,
                    'cc_number' => $cc_num,
                    'cc_number_encrypted' => sha1($cc_raw),
                    'merchant' => $pmt,
                    'merchant_customer_id' => $account_code,
                    'merchant_subscription_id' => $result['data']['subscription_id'],
                    'trial_end' => $result['data']['current_sub_end'],
                    'start_date' => $result['data']['current_sub_start'],
                    'end_date' => $result['data']['current_sub_end'],
                    'cancelled_at' => 0,
                    'members' => (int)$max_members+$additional_member,
                    'max_tokens' => $plan['res'][0]->max_tokens
                ];
                $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');

                if ($additional_member>0){
                    $price = $price + ($additional_member*$price_per_member);
                }
                $insert_data = [
                    'acctpmt_pid' => btutilGenerateHashId([$uid, $acct['_']['insert_id']]),
                    'account_id' => $acct['_']['insert_id'],
                    'user_id' => $user_id,
                    'amount' => $price,
                    'currency' => $plan['res'][0]->currency,
                    'charge_id' => isset($result['data'][$charge_id]) ? $result['data'][$charge_id] : '0',
                    'merchant' => $pmt,
                    'cc_number' => $last_four,
                    'mode' => $mode
                ];

                if ($price!==''){
                    btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
                }

                $plan_type = strtoupper($plan['res'][0]->plan_type);
                if (strtoupper($plan['res'][0]->plan_type)==='PROMAX'){
                    if (strtoupper($plan['res'][0]->payment_interval)=='YEARLY'){
                        $plan_type = 'PRO MAX ANNUAL';
                    }
                }

                $event = btflag_cookie('reactivateSubscription', 'create-subscription');
                $event_data = array_merge($account_data , $insert_data);
                $event_data = array_merge($event_data , ['plan_type' => $plan_type]);
								$event_data['user_pid'] = $user_pid;

                if ($additional_member>0 && isset($event_data['members'])){
                    $event_data['total_members'] = (int)$max_members+$additional_member+1;
                    unset($event_data['members']);
                }else{
                    $event_data['total_members'] = (int)$max_members+1;
                    unset($event_data['members']);
                }

                logUserActivity($event, $event_data);

                $this->doAfterPayment([
                    'user_id' => $user_id,
                    'email' => $email,
                    'plan' => $plan['res'][0],
                    'firstname' => isset($req['first_name']) ? $req['first_name'] : '',
                    'lastname' => isset($req['last_name']) ? $req['last_name'] : ''
                ]);

                //User should only have one subcription. Cancel the other subscriptions in merchant.
                $this->cancelOtherExistingSubscription($user_id,$acct['_']['insert_id']);

                return $this->response->setJSON($this->res_ok);
            }

            $acct = btdbFindBy('AccountModel', ['user_id', 'plan_id'], [$user_id, $plan_id]);
            if($acct['success'] && $acct['res']) {
                $acct = $acct['res'][0];
                if( strtotime($acct->trial_end) > strtotime($this->date_now) ) {
                    return $this->response->setJSON(['success' => 0, 'data' =>
                        ['msg' => "You have cancelled this plan and you will be able to resubscribe after " . date('M d, Y', strtotime($acct->trial_end))]
                    ]);
                }
            }
            return $this->response->setJSON(['success' => 0, 'data' => ['msg' => $result['message'] ? $result['message'] : ""]]);
        }

        return $this->response->setJSON($this->res_error);
    }

    private function checkNegative($email, $cc_number_encrypted){
        $NegativeModel = new NegativeModel();
        $negative = $NegativeModel->getNegative($email, $cc_number_encrypted);

        $isExist = 0;

        if ($negative['success']==1 && isset($negative['res'][0])){
            if (count($negative['res'])>0){
                $isExist = 1;
            }
        }

        return $isExist;
    }

    private function cancelOtherExistingSubscription($user_id, $new_subscribe_account_id){
      $accountModel = new AccountModel();
      $account = $accountModel->getActiveSubscription($user_id);

      if ($account['success']!=1){
        return;
      }
      if ($new_subscribe_account_id==''){
        return;
      }

      foreach ($account['res'] as $value) {
        $account_id = $value->account_id;
        $merchant = strtolower($value->merchant);
        $mode = strtolower($value->mode);
        $subscription_id = $value->merchant_subscription_id;

        if ($subscription_id==''){
          continue;
        }

        if ($account_id==$new_subscribe_account_id){
          continue;
        }

        if ($merchant=='recurly'){
          $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
          $payment = new BTPayment($paymenttype);
          $result = $payment->cancelSubscription($subscription_id);
          try{ $payment->cancelPastDueInvoice($value->merchant_customer_id); }
          catch(Exception $e) {}
          $update_data = [
              'account_id' => $account_id,
              'status' => 'inactive',
              'end_date' => $this->date_now,
              'cancelled_at' => $this->date_now
          ];
          btdbUpdate('AccountModel', 'Account', $update_data, 'update');
        }else if ($merchant=='stripe'){
          $paymenttype = new BTStripe($this->stripe_api_key, $mode);
          $payment = new BTPayment($paymenttype);
          $result = $payment->cancelSubscription($subscription_id);
          $update_data = [
              'account_id' => $account_id,
              'status' => 'inactive',
              'end_date' => $this->date_now,
              'cancelled_at' => $this->date_now
          ];
          btdbUpdate('AccountModel', 'Account', $update_data, 'update');
        }else if ($merchant=='paddle'){
          $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
          $payment = new BTPayment($paymenttype);
          $result = $payment->cancelSubscription($subscription_id);
          $update_data = [
              'account_id' => $account_id,
              'status' => 'inactive',
              'end_date' => $this->date_now,
              'cancelled_at' => $this->date_now
          ];
          btdbUpdate('AccountModel', 'Account', $update_data, 'update');
        }else if ($merchant=='paypal'){
          $paymenttype = new BTPayPal($this->paypal_api_key, $mode);
          $payment = new BTPayment($paymenttype);
          $result = $payment->cancelSubscription($subscription_id);
          $update_data = [
              'account_id' => $account_id,
              'status' => 'inactive',
              'end_date' => $this->date_now,
              'cancelled_at' => $this->date_now
          ];
          btdbUpdate('AccountModel', 'Account', $update_data, 'update');
        }else if ($merchant=='fastspring'){
            $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->cancelSubscription($subscription_id);
            $update_data = [
                'account_id' => $account_id,
                'status' => 'inactive',
                'end_date' => $this->date_now,
                'cancelled_at' => $this->date_now
            ];
            btdbUpdate('AccountModel', 'Account', $update_data, 'update');
        }
      }
    }

    private function getPaddlePayment() {
        $req = $this->req;
        $mode = 'live';
        if(btsessionIsUserLoggedIn()) {
            $mode = btsessionGet('USER')->mode;
        }
        $checkout_id = $req['checkout_id'];
        $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
        $payment = new BTPayment($paymenttype);
        $result = $payment->getSubscriptionDetails(['checkout_id' => $checkout_id]);

        if($result['data']->state == "processed") {
            return $this->response->setJSON($this->res_ok);
        }
        return $this->response->setJSON($this->res_error);
    }

    private function subscribePaddle()
    {
        $req = $this->req;
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
        } else {
            return $this->response->setJSON($this->res_error);
        }
        $pmt = 'Paddle';
        $uid = btutilGenerateHashId([$email, $mode]);
        $account_pid = btutilGenerateHashId([$uid]);

        $acct = btdbFindBy('AccountModel', 'user_id', $user_id);
        if($acct['success'] && $acct['res']) {
            $account_data['account_id'] = $acct['res'][0]->account_id;
            $account_pid = $acct['res'][0]->account_pid;
        }
        $plan_id = $req['plan'];
        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if($plan['success'] && $plan['res']) {
            $plan = $plan['res'][0];
            $checkout_id = $req['checkout_id'];
            $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->getSubscriptionDetails(['checkout_id' => $checkout_id]);
            //
            $isTrial = $plan->trial_days ? true : false;
            if($isTrial) {
                $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + '. $plan->trial_days .' days'));
            } else {
                if(strtolower($plan->payment_interval) == 'yearly') {
                    $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + 1 year'));
                } else {
                    $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + 1 month'));
                }
            }
            $subscription_id = "";
            $charge_id = "";
            if($result['data']->state === "processed") {
                $subscription_id = $result['data']->order->subscription_id;
                $charge_id = $result['data']->order->subscription_order_id;
            }
            $price = $isTrial ? $plan->trial_price ? $plan->trial_price : 0 : $plan->price;
            $max_tokens = isset($plan->max_tokens) ? $plan->max_tokens : '';
            $account_data = [
                'account_pid' => $account_pid,
                'user_id' => $user_id,
                'plan_id' => $plan_id,
                'merchant' => $pmt,
                'merchant_customer_id' => $checkout_id,
                'merchant_subscription_id' => $subscription_id,
                'trial_end' => $end_date,
                'start_date' => $this->date_now,
                'end_date' => $end_date,
                'cancelled_at' => 0,
								'max_tokens' => $max_tokens
            ];

            // for user log
            $event = 'create-subscription';
            $accountModel = new AccountModel();
            $account = $accountModel->getActiveSubscription($user_id);
            if ($account['success'] == 1  && count($account['res']) <= 0) {
                $event = 'create-subscription';
            } else if ($account['success'] == 1  && count($account['res']) > 0) {
                $event = 'update-subscription';
            }
            $event = btflag_cookie('reactivateSubscription', $event);
            // user log

            $oldacct = $acct;
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');

            $insert_data = [
                'acctpmt_pid' => btutilGenerateHashId([$uid, $acct['_']['insert_id']]),
                'account_id' => $acct['_']['insert_id'],
                'user_id' => $user_id,
                'amount' => $price,
                'charge_id' => $charge_id,
                'currency' => $plan->currency,
                'merchant' => $pmt,
                'mode' => $mode
            ];
            if ($price!==''){
                btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
            }

            $response = $this->res_ok;
            if($oldacct['success'] && $oldacct['res']) {
                $result = $payment->cancelSubscription($oldacct['res'][0]->merchant_subscription_id);
                if($result['status'] == "1") {
                    $account_data = [
                        'account_id' => $oldacct['res'][0]->account_id,
                        'status' => 'inactive',
                        'end_date' => $this->date_now,
                        'cancelled_at' => $this->date_now
                    ];
                    btdbUpdate('AccountModel', 'Account', $account_data, 'update');

                    $oldPlanModel = btdbFindBy('PlanModel', 'plan_id', $oldacct['res'][0]->plan_id);
                    $oldplan = null;
                    if($oldPlanModel['success'] && $oldPlanModel['res']) {
                        $oldPlanModel = $oldPlanModel['res'][0];
                        $oldplan = array(
                            'plan_name' => $oldPlanModel->plan_name,
                            'plan_type' => $oldPlanModel->plan_type,
                            'label' => $oldPlanModel->label,
                            'price' => $oldPlanModel->price,
                            'payment_interval' => $oldPlanModel->payment_interval
                        );
                    }
                    $response['data'] = array(
                        'currency' => $plan->currency,
                        'old_plan' => $oldplan,
                        'new_plan' => array(
                            'plan_name' => $plan->plan_name,
                            'plan_type' => $plan->plan_type,
                            'label' => $plan->label,
                            'price' => $plan->price,
                            'payment_interval' => $plan->payment_interval
                        )
                    );
                }
            }

            $plan_type = strtoupper($plan->plan_type);
            if (strtoupper($plan->plan_type)==='PROMAX'){
                if (strtoupper($plan->payment_interval)=='YEARLY'){
                    $plan_type = 'PRO MAX ANNUAL';
                }
            }

            $event_data = array_merge($account_data, $insert_data);
            $event_data = array_merge($event_data, ['plan_type' => $plan_type]);
            logUserActivity($event, $event_data);

            $this->doAfterPayment([
                'user_id' => $user_id,
                'email' => $email,
                'plan' => $plan
            ]);

            if(btsessionHas('SurveyData')) {
                $user_data = [
                    'user_id' => btsessionGet("USER")->user_id,
                    'survey_data' => btsessionGet('SurveyData')->init_session_data
                ];

                btdbUpdate('UserModel', 'User', $user_data, 'update');
                btsessionGet('USER')->survey_data = btsessionGet('SurveyData')->init_session_data;

                return $this->response->setJSON($response);
            }else{
                return $this->response->setJSON($response);
            }
        }
    }

    private function subscribeFastSpring()
    {
        $req = $this->req;
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
        } else {
            return $this->response->setJSON($this->res_error);
        }
        $pmt = 'Paddle';
        $uid = btutilGenerateHashId([$email, $mode]);
        $account_pid = btutilGenerateHashId([$uid]);

        $acct = btdbFindBy('AccountModel', 'user_id', $user_id);
        if($acct['success'] && $acct['res']) {
            $account_data['account_id'] = $acct['res'][0]->account_id;
            $account_pid = $acct['res'][0]->account_pid;
        }
        $plan_id = $req['plan'];
        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if($plan['success'] && $plan['res']) {
            $plan = $plan['res'][0];
            $checkout_id = $req['checkout_id'];
            $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
            $payment = new BTPayment($paymenttype);
            $result = $payment->getSubscriptionDetails(['checkout_id' => $checkout_id]);
            //
            $isTrial = $plan->trial_days ? true : false;
            if($isTrial) {
                $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + '. $plan->trial_days .' days'));
            } else {
                if(strtolower($plan->payment_interval) == 'yearly') {
                    $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + 1 year'));
                } else {
                    $end_date = date('Y-m-d H:i:s', strtotime($this->date_now. ' + 1 month'));
                }
            }
            $subscription_id = "";
            $charge_id = "";
            if($result['data']->state === "processed") {
                $subscription_id = $result['data']->order->subscription_id;
                $charge_id = $result['data']->order->subscription_order_id;
            }
            $price = $isTrial ? $plan->trial_price ? $plan->trial_price : 0 : $plan->price;
            $max_tokens = isset($plan->max_tokens) ? $plan->max_tokens : '';
            $account_data = [
                'account_pid' => $account_pid,
                'user_id' => $user_id,
                'plan_id' => $plan_id,
                'merchant' => $pmt,
                'merchant_customer_id' => $checkout_id,
                'merchant_subscription_id' => $subscription_id,
                'trial_end' => $end_date,
                'start_date' => $this->date_now,
                'end_date' => $end_date,
                'cancelled_at' => 0,
								'max_tokens' => $max_tokens
            ];

            // for user log
            $event = 'create-subscription';
            $accountModel = new AccountModel();
            $account = $accountModel->getActiveSubscription($user_id);
            if ($account['success'] == 1  && count($account['res']) <= 0) {
                $event = 'create-subscription';
            } else if ($account['success'] == 1  && count($account['res']) > 0) {
                $event = 'update-subscription';
            }
            $event = btflag_cookie('reactivateSubscription', $event);
            // user log

            $oldacct = $acct;
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');

            $insert_data = [
                'acctpmt_pid' => btutilGenerateHashId([$uid, $acct['_']['insert_id']]),
                'account_id' => $acct['_']['insert_id'],
                'user_id' => $user_id,
                'amount' => $price,
                'charge_id' => $charge_id,
                'currency' => $plan->currency,
                'merchant' => $pmt,
                'mode' => $mode
            ];
            btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');

            $response = $this->res_ok;
            if($oldacct['success'] && $oldacct['res']) {
                $result = $payment->cancelSubscription($oldacct['res'][0]->merchant_subscription_id);
                if($result['status'] == "1") {
                    $account_data = [
                        'account_id' => $oldacct['res'][0]->account_id,
                        'status' => 'inactive',
                        'end_date' => $this->date_now,
                        'cancelled_at' => $this->date_now
                    ];
                    btdbUpdate('AccountModel', 'Account', $account_data, 'update');

                    $oldPlanModel = btdbFindBy('PlanModel', 'plan_id', $oldacct['res'][0]->plan_id);
                    $oldplan = null;
                    if($oldPlanModel['success'] && $oldPlanModel['res']) {
                        $oldPlanModel = $oldPlanModel['res'][0];
                        $oldplan = array(
                            'plan_name' => $oldPlanModel->plan_name,
                            'plan_type' => $oldPlanModel->plan_type,
                            'label' => $oldPlanModel->label,
                            'price' => $oldPlanModel->price,
                            'payment_interval' => $oldPlanModel->payment_interval
                        );
                    }
                    $response['data'] = array(
                        'currency' => $plan->currency,
                        'old_plan' => $oldplan,
                        'new_plan' => array(
                            'plan_name' => $plan->plan_name,
                            'plan_type' => $plan->plan_type,
                            'label' => $plan->label,
                            'price' => $plan->price,
                            'payment_interval' => $plan->payment_interval
                        )
                    );
                }
            }

            $plan_type = strtoupper($plan->plan_type);
            if (strtoupper($plan->plan_type)==='PROMAX'){
                if (strtoupper($plan->payment_interval)=='YEARLY'){
                    $plan_type = 'PRO MAX ANNUAL';
                }
            }

            $event_data = array_merge($account_data, $insert_data);
            $event_data = array_merge($event_data, ['plan_type' => $plan_type]);
            logUserActivity($event, $event_data);

            $this->doAfterPayment([
                'user_id' => $user_id,
                'email' => $email,
                'plan' => $plan
            ]);

            if(btsessionHas('SurveyData')) {
                $user_data = [
                    'user_id' => btsessionGet("USER")->user_id,
                    'survey_data' => btsessionGet('SurveyData')->init_session_data
                ];

                btdbUpdate('UserModel', 'User', $user_data, 'update');
                btsessionGet('USER')->survey_data = btsessionGet('SurveyData')->init_session_data;

                return $this->response->setJSON($response);
            }else{
                return $this->response->setJSON($response);
            }
        }
    }

    private function updateSubscriptionENT(){
        $req = $this->req;
        $user = null;
        $additional_member = isset($req['members']) ? $req['members'] : 0;

        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $email = $user->email;
        } else return $this->response->setJSON($this->res_error);

        $accountModel = new AccountModel();
        $account = $accountModel->getActiveSubscription($user_id);
        if($account['success'] && $account['res']) {
            $account_id = $account['res'][0]->account_id;
            $account_code = $account['res'][0]->merchant_customer_id;
            $account_subscription_id = $account['res'][0]->merchant_subscription_id;
            $account_plan_type = strtolower($account['res'][0]->plan_type);
            $current_members = $account['res'][0]->members;
            $cc_number = $account['res'][0]->cc_number;
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => ['message'=>'No active plan']]);
        }

        if ($account_plan_type!=='enterprise'){
            return $this->response->setJSON(['success' => 0, 'data' => ['message'=>'Current plan is not Enterprise']]);
        }

        $data_user = btdbFindBy('UserModel', ['user_id'], [$user_id]);
        if ($data_user['success'] && !$data_user['res']) {
            return $this->response->setJSON(['success' => 0, 'data' => ['message'=>'User not found']]);
        }
        $mode = isset($data_user['res'][0]->mode) ? $data_user['res'][0]->mode : 'live';

        $current_members_excluding_free = 0;
        if ($current_members>9){
            $current_members_excluding_free = $current_members-9;
        }

        $total_members = $additional_member+$current_members_excluding_free;

        $params = [
            'subscription_id' => $account_subscription_id,
            'add_on_code' => 'additional_member',
            'qty' => $total_members,
          ];

        $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
        $payment = new BTPayment($paymenttype);
        $result = $payment->addAddOn($params);

        if($result['status'] === "1") {
            $account_data = [
                'account_id' => $account_id,
                'members' => $total_members+9 //add again the initial free
            ];
            btdbUpdate('AccountModel', 'Account', $account_data, 'update');

            $params = [
                'account_code' => $account_code
            ];

            $last_four = '';
            if ($cc_number!=''){
                $arr_cc_number = explode("****",$cc_number);
                $last_four = isset($arr_cc_number[1]) ? $arr_cc_number[1] : '';
            }

            $result = $payment->listInvoice($params);

            foreach($result['data'] as $invoice) {
                $invoice = $payment->getInvoice($invoice->getNumber());
                $invoice_id = $invoice['data']->getNumber();
                $type = $invoice['data']->getType();
                if($type == 'charge') {
                    $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                    if($charge['success'] && !$charge['res']) {
                        $insert_data = [
                            'acctpmt_pid' => btutilGenerateHashId([$invoice_id, $account_id]),
                            'account_id' => $account_id,
                            'user_id' => $user_id,
                            'currency' => $invoice['data']->getCurrency(),
                            'amount' => $invoice['data']->getTotal(),
                            'charge_id' => $invoice_id,
                            'merchant' => 'Recurly',
                            'cc_number' => $last_four,
                            'mode' => $mode
                        ];
                        $invoice_list[$invoice_id] = $insert_data;
                    }
                }
            }

            if($invoice_list) {
                usort($invoice_list, function ($a, $b) {
                    return floatval($a['charge_id']) <=> floatval($b['charge_id']);
                });
                foreach($invoice_list as $invoice) {
                    btdbUpdate('PaymentModel', 'Payment', $invoice, 'new');
                }
            }

            $addlogs = [
                'additional_members' => $additional_member,
                'total_members' => $total_members+10 //add again the initial free + parent account
            ];
            logUserActivity('subcription-add-member', $addlogs);

            return $this->response->setJSON(['success' => 1, 'data' => ['message'=>'']]);
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => ['message'=>'Error in adding member']]);

        }
    }

	public function executeThreeDSecureChangeCard(){
		$payment_method_id = isset($_GET['pm']) ? trim($_GET['pm']) : '';
		$setup_intent = isset($_GET['setup_intent']) ? trim($_GET['setup_intent']) : '';
		$account_pid = isset($_GET['apid']) ? trim($_GET['apid']) : '';
		$cc_num = isset($_GET['c1']) ? trim($_GET['c1']) : '';
		$cc_encrypted = isset($_GET['c2']) ? trim($_GET['c2']) : '';

		if ($cc_num!=''){
			$cc_num = base64_decode($cc_num);
		}

        if(!btsessionIsUserLoggedIn()) {
			die(header('Location: ' . getenv('app.baseURL').'/login'));
		}

		$mode = btsessionGet('USER')->mode;
		$email = btsessionGet('USER')->email;
		$user_id = btsessionGet('USER')->user_id;
        $uid = btutilGenerateHashId([$email, $mode]);

        $acct = btdbFindBy('AccountModel', ['user_id', 'account_pid'], [$user_id, $account_pid]);
        if(!$acct['success'] || !$acct['res']){
			echo 'Account not found.';
		}
        $pmt = $acct['res'][0]->merchant;
        $account_id = $acct['res'][0]->account_id;
        $cc_number_encrypted_previous = $acct['res'][0]->cc_number_encrypted;

		$params = [
			'setup_intent_id' => $setup_intent
		];
		$paymenttype = new BTStripe($this->stripe_api_key, $mode);
		$payment = new BTPayment($paymenttype);
		$result = $payment->retrieveSetupIntent($params);

		$result_status = isset($result['data']->status) ? $result['data']->status : '';
		if ($result_status=='succeeded' || $result_status=='complete' || $result_status=='completed'){
			$customer_id = $result['data']->customer;

			$params = [
				'customer_id' => $customer_id,
				'payment_menthod_id' => $payment_method_id
			];
			$result = $payment->updatePaymentMethod($params);

			if ($result['status']!='1'){
				$error = $result['message'];
				btflag_set('threed_error', $error);
			}

            $acct['res'][0]->cc_number_encrypted = $cc_number_encrypted_previous;

            $event_data = obj_to_arr($acct['res'][0]);
            logUserActivity('change-card', $event_data);

            $account_data = [
                'account_id' => $account_id,
                'cc_number' => $cc_num,
                'cc_number_encrypted' => $cc_encrypted
            ];
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

			die(header('Location: ' . getenv('app.baseURL').'/manage-account/'));
		}else{
			$error = 'Authentication Failed';
            btflag_set('threed_error', $error);

			die(header('Location: ' . getenv('app.baseURL').'/manage-account/'));
		}


		// $stripe->setupIntents->retrieve('seti_1Mm8s8LkdIwHu7ix0OXBfTRG', []);
	}

	public function executeThreeDSecureNew(){
		$payment_intent = isset($_GET['payment_intent']) ? trim($_GET['payment_intent']) : '';
		$subscription_id = isset($_GET['sid']) ? trim($_GET['sid']) : '';
		$plan_id = isset($_GET['plan_id']) ? trim($_GET['plan_id']) : '';
		$cc_num = isset($_GET['c1']) ? trim($_GET['c1']) : '';
		$cc_encrypted = isset($_GET['c2']) ? trim($_GET['c2']) : '';

        if(!btsessionIsUserLoggedIn()) {
			die(header('Location: ' . getenv('app.baseURL').'/login'));
		}

		$mode = btsessionGet('USER')->mode;
		$email = btsessionGet('USER')->email;
		$user_id = btsessionGet('USER')->user_id;
        $uid = btutilGenerateHashId([$email, $mode]);

        $log_param = [
            'user_id' => $user_id,
            'email' => $email,
            'mode' => $mode,
            'payment_intent' => $payment_intent,
            'subscription_id' => $subscription_id,
            'plan_id' => $plan_id,
            'cc_num' => base64_decode($cc_num),
            'cc_num_encrypt' => $cc_encrypted,
            'site' => 'start',
            'processed' => '0'
        ];
        $res_logs = btdbUpdate('ThreeDSecureLogModel', 'ThreeDSecureLog', $log_param, 'new');
		$log_id = $res_logs['_']['insert_id'];

		$params = [
			'payment_intent_id' => $payment_intent
		];
		$paymenttype = new BTStripe($this->stripe_api_key, $mode);
		$payment = new BTPayment($paymenttype);
		$result = $payment->retrievePaymentIntent($params);

		if ($result['status']!=='1'){
			echo 'Invalid Payment URL';
			exit;
		}
		$result = $result['data'];
		$customer_id = $result->customer;

		if ($result->status=='succeeded' || $result->status=='complete' || $result->status=='completed'){
			//to do: extra process
		}else{
			$error = 'Authentication Failed';
            btflag_set('threed_error', $error);

			die(header('Location: ' . getenv('app.baseURL').'/pay/'));
		}

		$params = [
			'subscription_id' => $subscription_id
		];
		$result_subscription = $payment->getSubscriptionDetails($params);

		if ($result_subscription['status']!=='1'){
			echo 'Invalid Subscription';
			exit;
		}

		$curr_sub_start = $result_subscription['data']['start_date'];
		$curr_sub_end = $result_subscription['data']['end_date'];

        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if(!isset($plan['res'][0])) {
			echo 'Invalid Link..';
			exit;
		}

		if ($cc_num!=''){
			$cc_num = base64_decode($cc_num);
		}

		$account_pid = btutilGenerateHashId([$uid]);
		$account_data = [
			'account_pid' => $account_pid,
			'user_id' => $user_id,
			'plan_id' => $plan_id,
			'cc_number' => $cc_num,
			'cc_number_encrypted' => $cc_encrypted,
			'merchant' => 'Stripe',
			'merchant_customer_id' => $customer_id,
			'merchant_subscription_id' => $subscription_id,
			'trial_end' => $curr_sub_end,
			'start_date' => $curr_sub_start,
			'end_date' => $curr_sub_end,
			'cancelled_at' => 0,
			'max_tokens' => $plan['res'][0]->max_tokens
		];
		$acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');
		$account_id = $acct['_']['insert_id'];
		//Insert Invoice
		$params = [
			'customer_id' => $customer_id
		];
		$result_invoices = $payment->listInvoice($params);
		$invoices = $result_invoices['data'];

		foreach ($invoices as $value) {
			$invoice_number = $value->id;
			$amount = $value->amount;
			if ($amount>0){
			  $amount = $amount/100;
			}

			$charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_number, $user_id]);
			if($charge['success'] && !$charge['res']) {
				$insert_data = [
				'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
				'account_id' => $account_id,
				'user_id' => $user_id,
				'currency' => $plan['res'][0]->currency,
				'amount' => $amount,
				'charge_id' => $invoice_number,
				'merchant' => 'Stripe',
				'cc_number' => '',
				'mode' => $mode
				];
				btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
			}
		}

		$this->doAfterPayment([
			'user_id' => $user_id,
			'email' => $email,
			'plan' => $plan['res'][0],
			'firstname' => isset($req['first_name']) ? $req['first_name'] : '',
			'lastname' => isset($req['last_name']) ? $req['last_name'] : ''
		]);

		//User should only have one subcription. Cancel the other subscriptions in merchant.
		$this->cancelOtherExistingSubscription($user_id,$acct['_']['insert_id']);

		$error = '';
		btflag_set('threed_error', $error);

		$log_param = [
			'log_id' => $log_id,
			'processed' => '1'
		];
		$three_d_secure = btdbUpdate('ThreeDSecureLogModel', 'ThreeDSecureLog', $log_param, 'update');

		die(header("Location: ".base_url()."thankyou/?plan=".$plan['res'][0]->label));
	}

	public function executeThreeDSecureUpdate(){
		$accountModel = new accountModel();

		$plan_label = isset($_GET['plan']) ? trim($_GET['plan']) : '';
		$plan_id = isset($_GET['plan_id']) ? trim($_GET['plan_id']) : '';
		$old_plan_id = isset($_GET['oaccount']) ? trim($_GET['oaccount']) : '';
		$payment_intent = isset($_GET['payment_intent']) ? trim($_GET['payment_intent']) : '';
		$subscription_id = isset($_GET['sid']) ? trim($_GET['sid']) : '';

        if(!btsessionIsUserLoggedIn()) {
			die(header('Location: ' . getenv('app.baseURL').'/login'));
		}

		$mode = btsessionGet('USER')->mode;
		$email = btsessionGet('USER')->email;
		$user_id = btsessionGet('USER')->user_id;

		$params = [
			'payment_intent_id' => $payment_intent
		];
		$paymenttype = new BTStripe($this->stripe_api_key, $mode);
		$payment = new BTPayment($paymenttype);
		$result = $payment->retrievePaymentIntent($params);

		if ($result['status']!=='1'){
			echo 'Invalid Payment URL';
			exit;
		}
		$result = $result['data'];

		if ($result->status=='succeeded' || $result->status=='complete' || $result->status=='completed'){
			//to do: extra process
		}else{
			$error = 'Authentication Failed';
            btflag_set('threed_error', $error);

			die(header('Location: ' . getenv('app.baseURL').'/pay/'.$plan_id));
		}

		$customer_id = $result->customer;

		$params = [
			'subscription_id' => $subscription_id
		];
		$result_subscription = $payment->getSubscriptionDetails($params);

		if ($result_subscription['status']!=='1'){
			echo 'Invalid Subscription';
			exit;
		}

		$curr_sub_start = $result_subscription['data']['start_date'];
		$curr_sub_end = $result_subscription['data']['end_date'];

        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        if(!isset($plan['res'][0])) {
			echo 'Invalid Link..';
			exit;
		}

		$account = $accountModel->getSubscriptionForUpgrade($user_id);
        if(!isset($account['res'][0])) {
			echo 'Invalid Link.';
			exit;
		}
        if($account['success'] && $account['res']) {
            $old_account_id = $account['res'][0]->account_id;
		}

		// UPDATE USER
		$user_data = [
			'user_id' => $user_id,
			'status' => 'active'
		];
		$usr = btdbUpdate('UserModel', 'User', $user_data, 'update');

		// UPDATE ACCOUNT
		$account_data = [
			'account_id' => $old_account_id,
			'end_date' => $this->date_now,
			'trial_end' => $this->date_now,
			'status' => 'inactive'
		];
		$acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

		// INSERT NEW ACCOUNT
		$uid = btutilGenerateHashId([$email, $mode]);
		$account_data = [
			'account_pid' => $uid,
			'user_id' => $user_id,
			'plan_id' => $plan_id,
			'merchant' => 'Stripe',
			'merchant_customer_id' => $customer_id,
			'merchant_subscription_id' => $subscription_id,
			'trial_end' => $curr_sub_end,
			'start_date' => $curr_sub_start,
			'end_date' => $curr_sub_end,
			'cancelled_at' => 0,
			'cc_number' => '',
			'cc_number_encrypted' => '',
			'max_tokens' => isset($plan['res'][0]->max_tokens) ? $plan['res'][0]->max_tokens : '0'
		];
		$acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');
		$account_id = $acct['_']['insert_id'];
		//
		btSessionSetUserSubscription((object)['user_id' => $user_id], 1);

		//Insert Invoice
		$params = [
			'customer_id' => $customer_id
		];
		$result_invoices = $payment->listInvoice($params);
		$invoices = $result_invoices['data'];

		foreach ($invoices as $value) {
			$invoice_number = $value->id;
			$amount = $value->amount;
			if ($amount>0){
			  $amount = $amount/100;
			}

			$charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_number, $user_id]);
			if($charge['success'] && !$charge['res']) {
				$insert_data = [
				'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
				'account_id' => $account_id,
				'user_id' => $user_id,
				'currency' => $plan['res'][0]->currency,
				'amount' => $amount,
				'charge_id' => $invoice_number,
				'merchant' => 'Stripe',
				'cc_number' => '',
				'mode' => $mode
				];
				btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
			}
		}

		$params = [
			'email' => $email,
			'plan_trial_days' => $plan['res'][0]->trial_days ? $plan['res'][0]->trial_days : '',
			'plan_trial_price' => $plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : '',
			'plan_currency' => $plan['res'][0]->currency ? $plan['res'][0]->currency : 'USD',
			'plan_type' => strtolower($plan['res'][0]->payment_interval) === 'yearly' ? 'Y' : 'M',
			'plan_interval' => '1',
			'plan_price' => $plan['res'][0]->price ? $plan['res'][0]->price : '',
			'plan_label' => $plan_label,
			'merchant_subscription_id' => $subscription_id,
		];
		logUserActivity('update-subscription', $params);

		btflag_remove('pricing');

		// MAILERLITE
		$mailertype = new BTHubSpot($this->mailer_key);
		$mailertool = new BTMailer($mailertype);

		$myDate = new \DateTime($this->date_now);
		$hubSpotDate = $myDate->format('Y-m-d');

		if($this->isEmailTest($email)) {
			// Do nothing
		} else if(!$mailertool->checkExist($email)) {
			$subscriber = [
				'email' => $email,
				'date_paid' => $this->date_now,
				'date_paid2' => $hubSpotDate,
				'plan_id' => $plan_id,
				'plan' => $plan['res'][0]->plan_type,
				'ispaid' => "1",
				'isstart' => "1"
				];
			$result = $mailertool->addSubscriber(getenv("MAILER_GROUP"), $subscriber);
		} else {
			$subscriber = [
				"date_paid" => $this->date_now,
				'date_paid2' => $hubSpotDate,
				'plan_id' => $plan_id,
				'plan' => $plan['res'][0]->plan_type,
				'ispaid' => "1",
				'isstart' => "1"
			];
			$result = $mailertool->updateSubscriber($email, $subscriber);
		}
		// END MAILERLITE

		$error = '';
		btflag_set('threed_error', $error);

		die(header("Location: ".base_url()."thankyou/?plan=".$plan_label));
	}

    private function updateSubscription()
    {
        $req = $this->req;
        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $email = $user->email;
        } else return $this->response->setJSON($this->res_error);
        $plan_id = $req['plan_id'];
        $plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
        $mode = btsessionGet('USER')->mode;
        $account_id = null;

        $accountModel = new AccountModel();
        $account = $accountModel->getSubscriptionForUpgrade($user_id);
        if($account['success'] && $account['res']) {
            $account_id = $account['res'][0]->account_id;
            $account_code = $account['res'][0]->merchant_customer_id;
            $account_status = strtolower($account['res'][0]->status);
            $pmt = $account['res'][0]->merchant;
            $cc_number = $account['res'][0]->cc_number;
            $cc_number_encrypted = $account['res'][0]->cc_number_encrypted;
            $cc_raw = str_replace("-","",$cc_number);
            $first_six = substr($cc_raw, 0, 6);
            $last_four = substr($cc_raw, -4);
            $cc_num = '';
            if ($first_six!='' && $last_four!=''){
              $cc_num = $first_six.'****'.$last_four;
            }
        }

				if($plan['success'] && $plan['res'] && $account_id) {
            if(strtolower($pmt) == 'recurly') {
                if(!$account_id) return $this->createSubscription($pmt);
                $account_pid = $account['res'][0]->account_pid;
                $params = [
                'account_code' => $account_code,
                'current_subsription_id' => $account['res'][0]->merchant_subscription_id,
                'new_plan_code' => $plan['res'][0]->recurly_plan_id ? $plan['res'][0]->recurly_plan_id : '',
                ];
                $charge_id = 'invoice_number';
                $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
            } else if(strtolower($pmt) == 'paddle') {
                if(!$plan['res'][0]->paddle_plan_id) return $this->response->setJSON(['success' => 0, 'data' => ['message' => 'No Plan ID Found.']]);
                $paddle_plan_id = json_decode($plan['res'][0]->paddle_plan_id, true);
                $user_paddle_subscription_id = $account['res'][0]->merchant_subscription_id;
                $params = [
                    'subscription_id' => $user_paddle_subscription_id,
                    'new_plan_id' => $paddle_plan_id[$mode],
                    'pro_rate' => 'true'
                ];
                $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
            } else if(strtolower($pmt) == 'fastspring') {
                if(!$plan['res'][0]->paddle_plan_id) return $this->response->setJSON(['success' => 0, 'data' => ['message' => 'No Plan ID Found.']]);
                $current_subscription_id = $account['res'][0]->merchant_subscription_id;
                $params = [
                    'current_subsription_id' => $current_subscription_id,
                    'new_plan_code' => $plan['res'][0]->fs_plan_id ? $plan['res'][0]->fs_plan_id : ''
                ];
                $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
            } else if(strtolower($pmt) == 'cardtransaction') {
                if(!$plan['res'][0]->sct_plan_id) return $this->response->setJSON(['success' => 0, 'data' => ['message' => 'No Plan ID Found.']]);
                $current_merchant_customer_id = $account['res'][0]->merchant_customer_id;
                $current_subscription_id = $account['res'][0]->merchant_subscription_id;
                $params = [
                    'customer_code' => $current_merchant_customer_id,
                    'subsription_code' => $current_subscription_id,
                    'new_plan_code' => $plan['res'][0]->sct_plan_id ? $plan['res'][0]->sct_plan_id : ''
                ];
            } else if(strtolower($pmt) == 'stripe' && $plan['res'][0]->stripe_plan_id) {
                $stripe_plan_id = json_decode($plan['res'][0]->stripe_plan_id, true);
                $plan_label = $plan['res'][0]->plan_type ? strtoupper($plan['res'][0]->plan_type) : '';
                if(strpos($plan_label, 'PROMAX') !== false) {
                  if (strtolower($plan['res'][0]->payment_interval) === 'yearly'){
                    $plan_label = 'PROMAX ANNUAL';
                  } else if (strtolower($plan['res'][0]->payment_interval) === 'monthly'){
                    $plan_label = 'PROMAX';
                  }
                } else {
                  $pos = strpos($plan_label, 'PRO');
                  if ($pos !== false) {
                    if (strtolower($plan['res'][0]->payment_interval) === 'yearly'){
                      $plan_label = 'PRO ANNUAL';
                    }
                    if (strtolower($plan['res'][0]->payment_interval) === 'monthly'){
                      $plan_label = 'PRO';
                    }
                  }
                  $pos = strpos($plan_label, 'BASIC');
                  if ($pos !== false) {
                    if (strtolower($plan['res'][0]->payment_interval) === 'yearly'){
                      $plan_label = 'BASIC ANNUAL';
                    }
                    if (strtolower($plan['res'][0]->payment_interval) === 'monthly'){
                      $plan_label = 'BASIC';
                    }
                  }
                }

                $merchant_subscription_id = $account['res'][0]->merchant_subscription_id;

                $params = [
                    'email' => $email,
                    'plan_trial_days' => $plan['res'][0]->trial_days ? $plan['res'][0]->trial_days : '',
                    'plan_trial_price' => $plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : '',
                    'plan_currency' => $plan['res'][0]->currency ? $plan['res'][0]->currency : 'USD',
                    'plan_type' => strtolower($plan['res'][0]->payment_interval) === 'yearly' ? 'Y' : 'M',
                    'plan_interval' => '1',
                    'plan_price' => $plan['res'][0]->price ? $plan['res'][0]->price : '',
                    'plan_label' => $plan_label,
                    'stripe_product_id' => $stripe_plan_id[$mode],
                    'merchant_subscription_id' => $merchant_subscription_id,
					'return_url' => base_url().'api/execute-threed-secure-update/?plan='.$plan_label.'&plan_id='.$plan_id.'&oaccount='.$account['res'][0]->plan_id
                ];

                $charge_id = 'invoice_number';
                $paymenttype = new BTStripe($this->stripe_api_key, $mode);
            } else return $this->response->setJSON($this->res_error);


            if(strtolower($pmt) == 'stripe' && $plan['res'][0]->stripe_plan_id && $account_status=='paused') {
                $resume_params = [
                    'subscription_id' => $merchant_subscription_id
                ];
                $payment = new BTPayment($paymenttype);
                $result = $payment->resumeSubscription($resume_params);
            }

            if(strtolower($pmt) == 'cardtransaction') {
                $result = $this->sctUpdateSubscription($params,$mode);
            }else{
                $payment = new BTPayment($paymenttype);
                $result = $payment->updateSubscription($params);
            }

			if(strtolower($pmt) == 'stripe' && $result['status']=='1') {
				$redirect = isset($result['redirect']) ? $result['redirect'] : '';
				if ($redirect!=''){
					return $this->response->setJSON(['success' => 1, 'data' => '', 'redirect'=>$redirect]);
				}
			}

            logUserActivity('update-subscription', $params);
			if($result['status'] === "1") {
                if(strtolower($pmt) == 'paddle'){
                    $subscription_id = $user_paddle_subscription_id;
                    $curr_sub_start = $account['res'][0]->start_date;
                    $curr_sub_end = date('Y-m-d H:i:s', strtotime($result['data']->next_payment->date));
                } else {
                    $subscription_id = $result['data']['subscription_id'];
                    $curr_sub_end = $result['data']['current_sub_end'];
                    $curr_sub_start = $result['data']['current_sub_start'];
                }

                // UPDATE USER
                $user_data = [
                    'user_id' => $user_id,
                    'status' => 'active'
                ];
                $usr = btdbUpdate('UserModel', 'User', $user_data, 'update');

                // UPDATE ACCOUNT
                $account_data = [
                    'account_id' => $account_id,
                    'end_date' => $this->date_now,
                    'trial_end' => $this->date_now,
                    'status' => 'inactive'
                ];
                $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');
                // INSERT NEW ACCOUNT
                $uid = btutilGenerateHashId([$email, $mode]);
                $account_data = [
                    'account_pid' => $uid,
                    'user_id' => $user_id,
                    'plan_id' => $plan_id,
                    'merchant' => $pmt,
                    'merchant_customer_id' => $account_code,
                    'merchant_subscription_id' => $subscription_id,
                    'trial_end' => $curr_sub_end,
                    'start_date' => $curr_sub_start,
                    'end_date' => $curr_sub_end,
                    'cancelled_at' => 0,
                    'cc_number' => $cc_num,
                    'cc_number_encrypted' => $cc_number_encrypted,
                    'max_tokens' => isset($plan['res'][0]->max_tokens) ? $plan['res'][0]->max_tokens : '0'
                ];
                $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');
                $account_id = $acct['_']['insert_id'];
                //
                btSessionSetUserSubscription((object)['user_id' => $user_id], 1);

                $invoice_list = [];
                if(strtolower($pmt) == 'recurly') {
                    $params = [
                        'account_code' => $account_code
                    ];
                    $result = $payment->listInvoice($params);

                    foreach($result['data'] as $invoice) {
                        $invoice = $payment->getInvoice($invoice->getNumber());
                        $invoice_id = $invoice['data']->getNumber();
                        $type = $invoice['data']->getType();
                        if($type == 'charge') {
                            $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                            if($charge['success'] && !$charge['res']) {
                                $insert_data = [
                                    'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
                                    'account_id' => $account_id,
                                    'user_id' => $user_id,
                                    'currency' => $invoice['data']->getCurrency(),
                                    'amount' => $invoice['data']->getTotal(),
                                    'charge_id' => $invoice_id,
                                    'merchant' => $pmt,
                                    'cc_number' => $last_four,
                                    'mode' => $mode
                                ];
                                $invoice_list[$invoice_id] = $insert_data;
                            }
                        }
                    }
                } else if(strtolower($pmt) == 'paddle') {
                    $params = [
                        'subscription_id' => $subscription_id
                    ];
                    $result = $payment->listInvoice($params);
                    foreach($result['data'] as $invoice) {
                        $invoice_id = $invoice->order_id;
                        $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                        if($charge['success'] && !$charge['res']) {
                            if(floatval($invoice->amount) > 0) {
                                $insert_data = [
                                    'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
                                    'account_id' => $account_id,
                                    'user_id' => $user_id,
                                    'currency' => $invoice->currency,
                                    'amount' => $invoice->amount,
                                    'charge_id' => $invoice_id,
                                    'merchant' => $pmt,
                                    'cc_number' => '',
                                    'mode' => $mode
                                ];
                                $invoice_list[$invoice_id] = $insert_data;
                            }
                        }
                    }
                } else if(strtolower($pmt) == 'stripe') {
                    $invoice_id = $result['data']['invoice_number'];
                    $price = isset($result['data']['price']) ? $result['data']['price'] : '';

                    if ($price==''){
                        $price = $plan['res'][0]->price;
                    }

                    $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                    if($charge['success'] && !$charge['res']) {
                        $insert_data = [
                            'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
                            'account_id' => $account_id,
                            'user_id' => $user_id,
                            'currency' => $plan['res'][0]->currency,
                            'amount' => $price,
                            'charge_id' => $invoice_id,
                            'merchant' => $pmt,
                            'cc_number' => $last_four,
                            'mode' => $mode
                        ];
                        btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
                    }
                } else if(strtolower($pmt) == 'fastspring') {
                    $invoice_id = $result['data']['invoice_number'];
                    $price = isset($result['data']['price']) ? $result['data']['price'] : '';

                    if ($price==''){
                        $price = $plan['res'][0]->price;
                    }

                    $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                    if($charge['success'] && !$charge['res']) {
                        $insert_data = [
                            'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
                            'account_id' => $account_id,
                            'user_id' => $user_id,
                            'currency' => $plan['res'][0]->currency,
                            'amount' => $price,
                            'charge_id' => $invoice_id,
                            'merchant' => $pmt,
                            'cc_number' => $last_four,
                            'mode' => $mode
                        ];
                        btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
                    }
                } else if(strtolower($pmt) == 'cardtransaction') {
                    $invoice_id = $result['data']['invoice_number'];
                    $price = isset($result['data']['price']) ? $result['data']['price'] : '';

                    if ($price==''){
                        $price = $plan['res'][0]->price;
                    }

                    $charge = btdbFindBy('PaymentModel', ['charge_id', 'user_id'], [$invoice_id, $user_id]);
                    if($charge['success'] && !$charge['res']) {
                        $insert_data = [
                            'acctpmt_pid' => btutilGenerateHashId([$uid, $account_id]),
                            'account_id' => $account_id,
                            'user_id' => $user_id,
                            'currency' => $plan['res'][0]->currency,
                            'amount' => $price,
                            'charge_id' => $invoice_id,
                            'merchant' => 'cardtransaction',
                            'cc_number' => $last_four,
                            'mode' => $mode
                        ];
                        btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');
                    }
                }

                if($invoice_list) {
                    usort($invoice_list, function ($a, $b) {
                        return floatval($a['charge_id']) <=> floatval($b['charge_id']);
                    });
                    foreach($invoice_list as $invoice) {
                        btdbUpdate('PaymentModel', 'Payment', $invoice, 'new');
                    }
                }

                // MAILERLITE
                $mailertype = new BTHubSpot($this->mailer_key);
                $mailertool = new BTMailer($mailertype);

								$myDate = new \DateTime($this->date_now);
								$hubSpotDate = $myDate->format('Y-m-d');

                if($this->isEmailTest($email)) {
                    // Do nothing
                } else if(!$mailertool->checkExist($email)) {
                    $subscriber = [
                        'email' => $email,
                        'date_paid' => $this->date_now,
                        'date_paid2' => $hubSpotDate,
                        'plan_id' => $plan_id,
                        'plan' => $plan['res'][0]->plan_type,
                        'ispaid' => "1",
                        'isstart' => "1"
		                ];
                    $result = $mailertool->addSubscriber(getenv("MAILER_GROUP"), $subscriber);
                } else {
                    $subscriber = [
                        "date_paid" => $this->date_now,
                        'date_paid2' => $hubSpotDate,
                        'plan_id' => $plan_id,
                        'plan' => $plan['res'][0]->plan_type,
                        'ispaid' => "1",
                        'isstart' => "1"
                    ];
                    $result = $mailertool->updateSubscriber($email, $subscriber);
                }
                // END MAILERLITE
                btflag_remove('pricing');
                // Response data
                $oldPlanModel = btdbFindBy('PlanModel', 'plan_id', $account['res'][0]->plan_id);
                $oldplan = null;
                if($oldPlanModel['success'] && $oldPlanModel['res']) {
                    $oldPlanModel = $oldPlanModel['res'][0];
                    $oldplan = array(
                        'plan_name' => $oldPlanModel->plan_name,
                        'plan_type' => $oldPlanModel->plan_type,
                        'label' => $oldPlanModel->label,
                        'price' => $oldPlanModel->price,
                        'payment_interval' => $oldPlanModel->payment_interval
                    );
                }
                $response = $this->res_ok;
                $response['data'] = array(
                    'currency' => $plan['res'][0]->currency,
                    'old_plan' => $oldplan,
                    'new_plan' => array(
                        'plan_name' => $plan['res'][0]->plan_name,
                        'plan_type' => $plan['res'][0]->plan_type,
                        'label' => $plan['res'][0]->label,
                        'price' => $plan['res'][0]->price,
                        'payment_interval' => $plan['res'][0]->payment_interval
                    )
                );

                return $this->response->setJSON($response);
            }
            return $this->response->setJSON(['success' => 0, 'data' => $result]);
        }

        return $this->response->setJSON($this->res_error);
    }

    private function cancelSubscription()
    {
        $req = $this->req;
        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $email = btsessionGet('USER')->email;
            $mode = btsessionGet('USER')->mode;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $email = $user->email;
            $mode = $user->mode;
        } else return $this->response->setJSON($this->res_error);

        if(isset($req['p_id'])) {
            $pid = $req['p_id'];
        } else {
            $pid = btsessionGet("ACCOUNT")->account_pid;
        }
        $account = btdbFindBy('AccountModel', ['account_pid', 'user_id'], [$pid, $user_id]);
        if($account['success'] && $account['res']) {
            $account = $account['res'][0];
            $account_id = $account->account_id;
            $pmt = $account->merchant ? $account->merchant : 'recurly';

            if(strtolower($pmt) == 'recurly') {
                $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
            } else if(strtolower($pmt) == 'stripe') {
                $paymenttype = new BTStripe($this->stripe_api_key, $mode);
            } else if(strtolower($pmt) == 'paddle') {
                $paymenttype = new BTPaddle($this->paddle_api_key, $mode);
            } else if(strtolower($pmt) == 'paypal') {
                $paymenttype = new BTPayPal($this->paypal_api_key, $mode);
            } else if(strtolower($pmt) == 'fastspring') {
                $paymenttype = new BTFastSpring($this->fastspring_api_key, $mode);
            } else if(strtolower($pmt) == 'cardtransaction') {
            } else {
              return $this->response->setJSON($this->res_error);
            }

            if(strtolower($pmt) == 'recurly') {
              $payment = new BTPayment($paymenttype);
              $result = $payment->cancelAllSubscription($account->merchant_customer_id);
            } elseif(strtolower($pmt) == 'cardtransaction') {
              $result = $this->sctCancelSubscription(btsessionGET("ACCOUNT")->merchant_customer_id, btsessionGET("ACCOUNT")->merchant_subscription_id, $mode);
            } else {
              $payment = new BTPayment($paymenttype);
              $result = $payment->cancelSubscription($account->merchant_subscription_id);
            }

            $event_data = obj_to_arr($account);
            logUserActivity('cancel-subscription', $event_data);

            if($result['status'] == "1") {
                $account_data = [
                    'account_id' => $account_id,
                    'status' => 'inactive',
                    'cancelled_at' => $this->date_now
                ];
                $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

                $activeAccountsPerUser = btdbFindBy('AccountModel', ['user_id','status'], [$user_id,'active']);
                $activeAccountsCount = sizeof($activeAccountsPerUser['res']);
                if ($activeAccountsCount<=0) {
                    $user_data = [
                        'user_id' => $user_id,
                        'status' => 'inactive'
                    ];
                    btsessionGet('USER')->status = 'inactive';
                    $usr = btdbUpdate('UserModel', 'User', $user_data, 'update');
                    btsessionGet('USER')->status = 'inactive';
                    btSessionSetUserSubscription((object)['user_id' => $user_id], 1);
                }

                // MAILERLITE
                $mailertype = new BTHubSpot($this->mailer_key);
                $mailertool = new BTMailer($mailertype);
                $email = strtolower($email);
                if($this->isEmailTest($email)) {
                    // Do nothing
                } else if(!$mailertool->checkExist($email)) {
                    $subscriber = [
                        'email' => $email,
												'isstart' => "1"
											];
                    $result = $mailertool->addSubscriber($subscriber);
                } else {
                    $subscriber = [
											'user_status' => "cancelled"
                    ];
                    $result = $mailertool->updateSubscriber($email, $subscriber);
                }
                // END MAILERLITE
                return $this->response->setJSON($this->res_ok);
            }
        }
        return $this->response->setJSON($this->res_error);
    }

    private function changeCard()
    {
        $req = $this->req;
        $user = null;
        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
            $mode = btsessionGet('USER')->mode;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
            $mode = $user->mode;
        } else return $this->response->setJSON($this->res_error);

        $account_pid = isset($req['pid']) ? $req['pid'] : '';
        $acct = btdbFindBy('AccountModel', ['user_id', 'account_pid'], [$user_id, $account_pid]);
        if(!$acct['success'] || !$acct['res']) return $this->response->setJSON($this->res_error);

        $pmt = $acct['res'][0]->merchant;
        $account_id = $acct['res'][0]->account_id;
        $cc_number_encrypted_previous = $acct['res'][0]->cc_number_encrypted;

		$cc = $req['cc'];
		$cc_raw = str_replace("-","",$cc);
		$first_six = substr($cc_raw, 0, 6);
		$last_four = substr($cc_raw, -4);

		$cc_num = '';
		if ($first_six!='' && $last_four!=''){
			$cc_num = $first_six.'****'.$last_four;
		}

        if(strtolower($pmt) == 'recurly') {
            $cc = $req['cc'];
            $ccmonth = $req['ccmonth'];
            $ccyr = $req['ccyr'];
            $cvv = $req['cvv'];
            $postal = isset($req['postal']) ? $req['postal'] : '';
            $account_code = $acct['res'][0]->merchant_customer_id;
            $phone = isset($req['phone']) ? $req['phone'] : '';

            $params = [
                'account_code' => $account_code,
                'first_name' => isset($req['first_name']) ? $req['first_name'] : btsessionGet('USER')->first_name,
                'last_name' => isset($req['last_name']) ? $req['last_name'] : btsessionGet('USER')->last_name,
                'cc_number' => $cc,
                'cc_exp_month' => $ccmonth,
                'cc_exp_year' => $ccyr,
                'cc_cvv' => $cvv,
                'postal_code' => $postal,
                'phone' => $phone
            ];

            $paymenttype = new BTRecurly($this->recurly_api_key, $mode);
        } else if(strtolower($pmt) == 'stripe') {
            $cc = $req['cc'];
            $ccmonth = $req['ccmonth'];
            $ccyr = $req['ccyr'];
            $cvv = $req['cvv'];
            $postal = isset($req['postal']) ? $req['postal'] : '';
            $merchant_customer_id = $acct['res'][0]->merchant_customer_id;

            $first_name = isset($req['first_name']) ? $req['first_name'] : btsessionGet('USER')->first_name;
            $last_name = isset($req['last_name']) ? $req['last_name'] : btsessionGet('USER')->last_name;

            $cus_name = $first_name.' '.$last_name;
            $params = [
                'customer_id' => $merchant_customer_id,
                'first_name' => isset($req['first_name']) ? $req['first_name'] : btsessionGet('USER')->first_name,
                'last_name' => isset($req['last_name']) ? $req['last_name'] : btsessionGet('USER')->last_name,
                'phone' => isset($req['phone']) ? $req['phone'] : '',
                'cus_name' => $cus_name,
                'cc_number' => $cc,
                'cc_exp_month' => $ccmonth,
                'cc_exp_year' => $ccyr,
                'cc_cvv' => $cvv,
                'postal_code' => $postal,
				'return_url' => base_url().'api/execute-threed-secure-changecard/?apid='.$account_pid.'&c1='.base64_encode($cc_num).'&c2='.sha1($cc_raw)
            ];

            $paymenttype = new BTStripe($this->stripe_api_key, $mode);
        } else {
            // return $this->response->setJSON($this->res_error);
        }
        $payment = new BTPayment($paymenttype);
        $result = $payment->changeCreditCard($params);

        if($result['status'] === "1") {
			if (isset($result['redirect']) && $result['redirect']!==''){
				return $this->response->setJSON([ 'success' => 1, 'data' => '', 'redirect' => $result['redirect']]);
			}

            $acct['res'][0]->cc_number_encrypted = $cc_number_encrypted_previous;

            $event_data = obj_to_arr($acct['res'][0]);
            logUserActivity('change-card', $event_data);

            $account_data = [
                'account_id' => $account_id,
                'cc_number' => $cc_num,
                'cc_number_encrypted' => sha1($cc_raw)
            ];
            $acct = btdbUpdate('AccountModel', 'Account', $account_data, 'update');

            return $this->response->setJSON($this->res_ok);
        }

        return $this->response->setJSON([ 'success' => 0, 'data' => [ 'msg' => $result['message'] ] ]);
    }

    private function saveSurveyData()
    {
        if(!btsessionIsUserLoggedIn()) {
            return $this->response->setJSON($this->res_error);
        }

        $req = $this->req;

        if(isset($req['save_session']) && $req['save_session'] === '1'){
            $session_data = ['init_session_data' => $req['data']];
            btsessionSet('SurveyData',$session_data);
            return $this->response->setJSON($this->res_ok);
        }

        $data =  btsessionHas("SurveyData") ? btsessionGet('SurveyData')->init_session_data : $req['data'];

        $user_data = [
            'user_id' => btsessionGet("USER")->user_id,
            'survey_data' => $data
        ];

        $update = btdbUpdate('UserModel', 'User', $user_data, 'update');
        btsessionGet('USER')->survey_data = $data;

        if($update['success']) return $this->response->setJSON($this->res_ok);

        return $this->response->setJSON(['success' => 0, 'data' => $update]);

    }

    private function sendEnterprisePaymentInfo(){
        $req = $this->req;
        $members = $req['members'];
        $total_amount = $req['total_amount'];

        $email_data = [
            'send_to' => btsessionGet('USER')->email,
            'members' => $members,
            'total_amount' => $total_amount,
            'user_id' => btsessionGet("USER")->user_id,
        ];
        btemailSendEnterprisePaymentInfo($email_data);

        return $this->response->setJSON(['success' => 1, 'data' => btsessionGet('USER')->email]);
    }

    private function sendEnterprisePaymentConfirmation() {
        /* $req = $this->req;
        $reference_number = $req['reference_number'];
        $amount = $req['amount'];
        $owner_email = btsessionGet("USER")->email;
        $mode = btsessionGet('USER')->mode;

        if ($mode=='test') {
            $recipient = getenv("ENTERPRISE_ADMIN_EMAIL_TEST");
        } else {
            $recipient = getenv("ENTERPRISE_ADMIN_EMAIL");
        }

        $email_data = [
            'send_to' => $recipient,
            'reference_number' => $reference_number,
            'amount' => $amount,
            'email' => $owner_email
        ];

        $has_file = isset($_FILES['file']);
        $file = null;
        $fileName = null;
        $target = null;

        if ($has_file) {
            $file = $_FILES['file'];

            if (isset($file['name'])) {
                $fileName = basename($file['name']);
                $fileTempName = $file['tmp_name'];
                $target = "uploads/$fileName";

                move_uploaded_file($fileTempName, $target);

                $email_data['attach'] = $target;
            }
        }

        btemailSendEnterprisePaymentConfirmation($email_data);

        $event_data = [];
        $event_data['amount'] = $amount;
        $event_data['reference_number'] = $reference_number;

        logUserActivity('sendEnterprisePaymentConfirmation', $event_data);

        if (!is_null($fileName)) {
            if (file_exists($target)) {
                unlink($target);
            }
        } */

        return $this->response->setJSON(['success' => 1, 'data' => '']);
    }

    private function validateEnterpriseEmail(){
        $req = $this->req;
        $user_id = btsessionGet("USER")->user_id;
        $email = $req['email'];
        $error_message = "";

        $data = btdbFindBy('UserModel', ['email', 'ent_parent_user_id'], [$email, $user_id]);

        if ($data['success'] && $data['res']) {
            $error_message="Email already exist.";
        }

        $data = btdbFindBy('UserModel', 'email', $email);
        if ($data['success'] && $data['res']) {
            $error_message="Email is an existing ai-pro user.";
        }

        if ($error_message==""){
            return $this->response->setJSON(['success' => 1, 'data' => ""]);
        }else{
            return $this->response->setJSON(['success' => 0, 'data' => $error_message]);
        }
    }

    private function addEnterpriseMember(){
        $req = $this->req;
        $members = json_decode($req['members']);
        $user_id = btsessionGet("USER")->user_id;
        $user_mode = btsessionGet("USER")->mode;
        $owner_email = btsessionGet("USER")->email;
				$max_members = btsessionGet("ACCOUNT")->members;
				$members_database_count = 0;

        if (count($members)==0){
            return $this->response->setJSON(['success' => 1, 'data' => '']);
        }

				$userModel = new UserModel();
				$data = $userModel->getEnterpriseMembers($user_id);
				if($data['success'] && $data['res']) {
					$members_database_count = count($data['res']);
				}

				$total_members = count($members) + $members_database_count;

				if ($total_members>$max_members){
					return $this->response->setJSON(['success' => 0, 'data' => 'Members Exceeded.']);
				}

				//validate first
        $error_message = [];
				$i = 0;
				foreach ($members as $value){
					$email = trim(strtolower($value->email));

					$data = btdbFindBy('UserModel', ['email', 'ent_parent_user_id'], [$email, $user_id]);
					if ($data['success'] && $data['res']) {
						array_push($error_message,["index"=>$i,"email"=>$email,"error"=>"Email already exists."]);
						continue;
					}

					$data = btdbFindBy('UserModel', 'email', $email);
					if ($data['success'] && $data['res']) {
						array_push($error_message,["index"=>$i,"email"=>$email,"error"=>"Email is an existing AI-Pro user."]);
						continue;
					}
					$i += 1;
				}

				if (count($error_message)>0){
					return $this->response->setJSON(['success' => 1, 'data' => $error_message]);
				}

        $error_message = [];
        foreach ($members as $value){
            $email = trim(strtolower($value->email));
            $name = trim($value->name);
            $arr_name = explode(" ", $name);
            $first_name = isset($arr_name[0]) ? $arr_name[0] : '';
            $last_name = isset($arr_name[1]) ? $arr_name[1] : '';

						$login_token = btutilGeneratePassword($email . time());
            $temp_password = $this->generateRandom(10);
            $uid = btutilGenerateHashId([$email, $user_mode]);

						if ($email=='' || $name==''){
							continue;
						}

            $user_data = [
                'user_pid' => $uid,
                'email' => $email,
                'mode' => $user_mode,
                'password' => $this->wp_hasher->HashPassword( $temp_password ),
                'first_name' => $first_name,
                'last_name' => $last_name,
                'status' => 'active',
                'login_token' => $login_token,
                'ent_parent_user_id' => $user_id
            ];

            $res = btdbUpdate('UserModel', 'User', $user_data, 'new');
            if(!$res['success']) {
                array_push($error_message,["email"=>$email,"error"=>"Error adding email ".$email]);
                continue;
            }else{
							$event_data = [
								'full_name' => $name,
								'member_email' => $email
							];
							logUserActivity('addEnterpriseMember', $event_data);

							$email_data = [
								'send_to' => $email,
								'first_name' => $first_name,
								'email' => $email,
								'owner_email' => $owner_email,
								'temp_password' => $temp_password
							];
							btemailSendEnterpriseWelcomeMembers($email_data);
            }
        }

        return $this->response->setJSON(['success' => 1, 'data' => $error_message]);
    }

    private function getEnterpriseMembers(){
        $req = $this->req;
        $user_id = '';

        if(btsessionIsUserLoggedIn()) {
					$user_id = btsessionGet('USER')->user_id;
					$email = btsessionGet('USER')->email;
				} else return $this->response->setJSON($this->res_error);

        $userModel = new UserModel();
        $data = $userModel->getEnterpriseMembers($user_id);
				$members = [];
        if($data['success'] && $data['res']) {
					foreach($data['res'] as $value){
							$members = array_merge($members,[$value->email]);
					}

					$accountModel = new AccountModel();
					$account = $accountModel->getActiveSubscription($user_id);

					if ($account['success']==1 && $account['res']){
						$maxTokenRange = $this->computeTokenDateRange($account);
                        $UsageTracking = new UsageTrackingModel();
                        $usages = $UsageTracking->findUserByEmailsAndStartDateAndModelGroup($members,$maxTokenRange['max_start']);

                        if (strtolower($account['res'][0]->plan_name) != 'enterprise') {

                            $prompt_limit = $this->getCacheRedis("PromptCreditLimit", "default", true);
                            $o1_limit = json_decode($prompt_limit[strtolower(str_replace(' ', '', $account['res'][0]->plan_name))], true);
                            $FluxUsage = new FluxUsageTrackingModel();
                            $o1_prompt = $UsageTracking->findUserByEmailsAndStartDateAndModel($members,$maxTokenRange['max_start'], $o1_limit['model']);
                            $flux_prompt = $FluxUsage->findUserByEmailsAndStartDate($members,$maxTokenRange['max_start']);

                            foreach($data['res'] as $value){
                                $member_o1_usage = array_search(strtolower($value->email),array_column($o1_prompt,'user_email'));
                                $member_flux_usage = array_search(strtolower($value->email),array_column($flux_prompt,'user_email'));


                                if ($member_o1_usage!==false){
                                    $value->total_o1_prompt = $o1_prompt[$member_o1_usage]->total_prompt;
                                } else {
                                    $value->total_o1_prompt = '0';
                                }

                                if ($member_flux_usage!==false){
                                    $value->total_flux_prompt = $flux_prompt[$member_flux_usage]->total_prompt;
                                } else {
                                    $value->total_flux_prompt = '0';
                                }
                            }
                        }
                        foreach($data['res'] as $value){

                            $total = 0;
                            $member_usage = array_search(strtolower($value->email),array_column($usages,'user_email'));
                            foreach ($usages as $usage) {
                                if (strtolower($usage->user_email) == strtolower($value->email)) {
                                    $model = $usage->model;

                                    if ($model != 'dall-e-3') {
                                        $totalToken = ($model == "gpt-4o-mini") ? floor($usage->total_token / 10) : (int) $usage->total_token;
                                        $total += $totalToken;
                                    }
                                }
                            }

                            if ($member_usage!==false){
                                $value->total_token = $total;
                            }else{
                                $value->total_token = '0';
                            }
                        }
					}

					return $this->response->setJSON(['success' => $data['success'], 'data' => $data['res']]);
        }

        return $this->response->setJSON($this->res_error);
    }

    private function deleteEnterpriseMember(){
			$req = $this->req;
			$member_user_id = $req['member_user_id'];
			$member_email = $req['member_email'];

			if ($member_user_id=='' || $member_email==''){
				return $this->response->setJSON($this->res_error);
			}

			$event_data = [
				'member_email' => $member_email
			];
			logUserActivity('deleteEnterpriseMember', $event_data);
			btdbDelete('UserModel', ['user_id'], [$member_user_id], 'new');
			btemailSendDeleteAccountEmail([
				'send_to' => $member_email
			]);

			return $this->response->setJSON($this->res_ok);

    }

    private function editEnterpriseMember(){
			$req = $this->req;
			$user_id = btsessionGet("USER")->user_id;
			$member_user_id = $req['member_user_id'];
			$member_email = trim($req['member_email']);
			$member_fullname = $req['member_fullname'];
			$member_old_fullname = $req['member_old_fullname'];
			$member_old_email = trim($req['member_old_email']);

			$arr_name = explode(" ", $member_fullname);
			$first_name = isset($arr_name[0]) ? $arr_name[0] : '';
			$last_name = isset($arr_name[1]) ? $arr_name[1] : '';

			if ($member_user_id=='' || $member_email=='' || $member_fullname == ''){
				return $this->response->setJSON($this->res_error);
			}

			//validate first
			$error_message = [];
			if ($member_old_email!==$member_email){
				$data = btdbFindBy('UserModel', ['email', 'ent_parent_user_id'], [$member_email, $user_id]);
				if ($data['success'] && $data['res']) {
					array_push($error_message,["email"=>$member_email,"error"=>"Email already exists."]);
				}
				if (count($error_message)>0){
					return $this->response->setJSON(['success' => 1, 'data' => $error_message]);
				}

				$data = btdbFindBy('UserModel', 'email', $member_email);
				if ($data['success'] && $data['res']) {
					array_push($error_message,["email"=>$member_email,"error"=>"Email is an existing AI-Pro user."]);
				}
				if (count($error_message)>0){
					return $this->response->setJSON(['success' => 1, 'data' => $error_message]);
				}
			}


			$event_data = [];

			if ($member_old_email!==$member_email){
				if ($member_old_email!=$member_email){
					$event_data['old_email'] = $member_old_email;
					$event_data['new_email'] = $member_email;
					$user_data = [
						'user_id' => $member_user_id,
						'email' => $member_email,
					];
					$update = btdbUpdate('UserModel', 'User', $user_data, 'update');
				}
			}

			if ($member_old_fullname!=$member_fullname){
				$event_data['old_fullname'] = $member_old_fullname;
				$event_data['new_fullname'] = $member_fullname;
				$user_data = [
					'user_id' => $member_user_id,
					'first_name' => $first_name,
					'last_name' => $last_name,
				];
				$update = btdbUpdate('UserModel', 'User', $user_data, 'update');
			}

			if (isset($event_data['old_fullname']) || isset($event_data['old_email'])){
				logUserActivity('updateEnterpriseMember', $event_data);
			}

			return $this->response->setJSON($this->res_ok);
    }

    private function resendPassEnterpriseMember(){
        $req = $this->req;
        $member_user_id = $req['member_user_id'];
        $member_email = $req['member_email'];
        $temp_password = $this->generateRandom(10);

        if ($member_user_id==''){
            return;
        }

        $user_data = [
            'user_id' => $member_user_id,
            'password' => $this->wp_hasher->HashPassword( $temp_password ),
        ];
        $update = btdbUpdate('UserModel', 'User', $user_data, 'update');

        $email_data = [
            'send_to' => $member_email,
            'email' => $member_email,
            'temp_password' => $temp_password
        ];
        btemailResendPasswordEnterpriseMembers($email_data);

        $event_data = [];
        $event_data['member_email'] = $member_email;
        logUserActivity('resetPasswordEnterpriseMember', $event_data);

        return $this->response->setJSON(['success' => 1, 'data' => '']);
    }

    private function generateRandom($n) {
			$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
			$randomString = '';

			for ($i = 0; $i < $n; $i++) {
					$index = rand(0, strlen($characters) - 1);
					$randomString .= $characters[$index];
			}

			return $randomString;
    }

    private function clickAppUpgrade()
    {
        $req = $this->req;
        $event_data = [
            'url' => isset($req['url']) ? $req['url'] : ''
        ];

				logUserActivity('click-app-upgrade', $event_data);
        return $this->response->setJSON($this->res_ok);
    }

    private function getBanner() {
        $bannerModel = new MemberAreaModel();
        $respond = $bannerModel->getBanner();

        return $this->response->setJSON(['success' => $respond['success'], 'data' => $respond['res']]);
    }


    public function geotest(){
        $ip = getClientIP();
        $location = getGeoIP($ip);
        $location = json_decode($location);
        $country = '';

        foreach($location as $key => $value) {
            if ($country==''){
              $country =  isset($value->countryName) ? $value->countryName : '';
            }
        }

        $country2 = isset($location->countryName) ? $location->countryName : '';

        echo "IP:".$ip;
        echo '----------------';
        var_dump($country);
        echo '-----------------';
        echo $country2;
        echo '----------------';
        var_dump($location);
    }

    // Apple Login
    //---------------------------------------------------------------------------------------------
    public function apple_login() {
        $state = bin2hex(random_bytes(16)); // Use stronger randomness
				$base_url = '';
        $redirectURI = getenv('app.baseURL').'/apple/handle-apple-login';
        // $redirectURI = 'https://staging.start.ai-pro.org/apple/handle-apple-login';
        $authorize_url = 'https://appleid.apple.com/auth/authorize' . '?' . http_build_query([
            'response_type' => 'code',
            'response_mode' => 'form_post',
            'client_id' => 'org.aipro.start.login',
            'redirect_uri' => $redirectURI,
            'state' => $state,
            'scope' => 'name email',
        ]);
        header("Location: $authorize_url");
        exit();
    }
    public function handle_apple_login()
    {
        // Retrieve state and code from POST data
        $session_state = $_POST['state'];
        $session_code = $_POST['code'];
        // Exchange authorization code for access token
        $token_url = 'https://appleid.apple.com/auth/token'; // Correct endpoint for token exchange
        $token_params = [
            'grant_type' => 'authorization_code',
            'code' => $session_code,
            'client_id' => getenv('APPLE_CLIENT_ID'), // Check if this is correct - use the ID from your Apple Developer Portal
            'client_secret' => getenv('APPLE_CLIENT_SECRET'),
            'redirect_uri' => getenv('app.baseURL').'/apple/handle-apple-login', // Ensure exact match with registered URI
        ];
        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $token_response = curl_exec($ch);
        curl_close($ch);
        // Parse token response to get access token
        $token_data = json_decode($token_response, true);
        $base_url = getenv('app.baseURL');
        // Check for errors in token response
        if (array_key_exists('error', $token_data)) {
            // Something went wrong
            header('Location: ' . $base_url.'/login/?e=aiafttlaob');
            exit;
        }
        $access_token = $token_data['access_token'];
        // Verify nonce in id_token (if present)
        if (isset($token_data['id_token'])) {
            // Extract and decode the id_token
            $id_token_parts = explode('.', $token_data['id_token']);
            $payload = json_decode(base64_decode($id_token_parts[1]), true);
            if (isset($payload['email']) && $payload['email_verified']) {
                // Email is verified, proceed with user authentication
                $email = $payload['email'];

                // Check if the user exists in the database
                $data = btdbFindBy('UserModel', 'email', $email);
                if (!$data['success'] || !isset($data['res'][0])) {
                    // Invalid login
                    header('Location: ' . $base_url.'/login/?e=pzmzwxjbnp');
                    exit;
                }
                $res = $data['res'][0];
                $login_token = btutilGenerateAccessToken($email);
                if ($data['res'][0]->login_token) {
                    $login_token = $data['res'][0]->login_token;
                } else {
                    btdbUpdate('UserModel', 'User', [ 'user_id' => $data['res'][0]->user_id, 'login_token' => $login_token ], 'update');
                }
                btsessionSetAccount($res, 1);

                $this->setAuthCookies($email, $data, $login_token);
                log_message('debug', 'login-------> ' . $email);
                logUserActivity('login');

                // Log user login for chat usage
                $url = getenv('API_URL') . '/e/add-login-log';
                // $url = getenv('API_URL') . '/pricing';
                $ch_data = [
                    'email' => $email
                ];
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($ch_data));
                $response = curl_exec($ch);
                curl_close($ch);
                if($this->getLandingUrl()) {
                    header('Location: ' . $this->getFluxUrl());
                    exit;
                }
                if (btflag('kt8typtb') === 'druig') {
                    header('Location: ' . getenv('CHATBOTPRO_URL'));
                    exit;
                }

                // header('Location: ' . getenv('app.baseURL').'/my-account');
                header('Location: ' . '/my-account');
                exit;
            } else {
                // Email is not verified
                echo "Email is not verified.";
            }
        }
    }

    public function apple_register($fromWP = '') {
        $state = bin2hex(random_bytes(16)); // Use stronger randomness

        if($fromWP=='1'){
            $redirect_url = 'https://start.ai-pro.org/apple/handle-apple-register/1';
        }else{
            $redirect_url = getenv('app.baseURL').'/apple/handle-apple-register/0';
        }

        // $redirect_url = 'https://1cbb0d650f08.ngrok.app'.'/apple/handle-apple-register/0';
				
        $authorize_url = 'https://appleid.apple.com/auth/authorize' . '?' . http_build_query([
            'response_type' => 'code',
            'response_mode' => 'form_post',
            'client_id' => 'org.aipro.start.login',
            'redirect_uri' => $redirect_url,
            'state' => $state,
            'scope' => 'name email',
        ]);
        header("Location: $authorize_url");
        exit();
    }
    public function handle_apple_register($fromWP = '') {
        // $session_state = $_POST['state'];
		
        $session_code = $_POST['code'];

        $firstName = '';
        $lastName = '';
        $email = '';

        if($fromWP=='1'){
            $redirect_url = getenv('app.baseURL').'/apple/handle-apple-register/1';
            $app_url = 'https://ai-pro.org';
            // $redirect_url = 'https://1cbb0d650f08.ngrok.app'.'/apple/handle-apple-register/1';
            // $app_url = 'https://1cbb0d650f08.ngrok.app';
        }else{
            $redirect_url = getenv('app.baseURL').'/apple/handle-apple-register/0';
            $app_url = getenv('app.baseURL');
            // $redirect_url = 'https://1cbb0d650f08.ngrok.app'.'/apple/handle-apple-register/0';
            // $app_url = 'https://1cbb0d650f08.ngrok.app';
        }
				$app_url = $this->getLandingUrl() ? $this->getLandingUrl() : $app_url;

        if(isset($_POST['user'])) {
            // If the 'user' key is set in $_POST data, retrieve and process the user information
            $userInfo = $_POST['user'];
            $data = json_decode($userInfo, true);

            $firstName = $data['name']['firstName'];
            $lastName = $data['name']['lastName'];
            $email = $data['email'];
        }

        // Exchange authorization code for access token
        $token_url = 'https://appleid.apple.com/auth/token'; // Correct endpoint for token exchange
        $token_params = [
            'grant_type' => 'authorization_code',
            'code' => $session_code,
            'client_id' => getenv('APPLE_CLIENT_ID'), // Check if this is correct - use the ID from your Apple Developer Portal
            'client_secret' => getenv('APPLE_CLIENT_SECRET'),
            'redirect_uri' => $redirect_url, // Ensure exact match with registered URI
        ];

        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $token_response = curl_exec($ch);
        curl_close($ch);

        // Parse token response to get access token
        $token_data = json_decode($token_response, true);

        // Check for errors in token response
        if (array_key_exists('error', $token_data)) {
            // Something went wrong
            header('Location: ' . $app_url .'/login/?e=aiafttlaob');
            exit;
        }

        $access_token = $token_data['access_token'];
        // Verify nonce in id_token (if present)
        if (isset($token_data['id_token'])) {
            // Extract and decode the id_token
            $id_token_parts = explode('.', $token_data['id_token']);
            $payload = json_decode(base64_decode($id_token_parts[1]), true);

            if (isset($payload['email']) && $payload['email_verified']) {
                // Email is verified, proceed with user registration
                $email = $payload['email'];
                $first_name = $firstName; // You may need to adjust this based on your Apple login integration
                $last_name = $lastName; // You may need to adjust this based on your Apple login integration

                $user_mode = btflag('mode', FLAG_MODE_LIVE);

                $uid = btutilGenerateHashId([$email, $user_mode]);
                $login_token = btutilGeneratePassword($email . time());

                // Check if the user exists in the database
                $userModel = new UserModel();
                $userExist = $userModel->checkIfEmailExist($email);
                if($userExist) {
									//User already exist
									if(btflag('kt8typtb') === 'echo'){
										setcookie('e', 'dbivartanw', time() + (86400 * 30), '/');
										setcookie('e', 'dbivartanw', time() + (86400 * 30), '/','.ai-pro.org');
										echo "<script type='text/javascript'>window.close();</script>";
										exit;
									}else{
										header('Location: ' . $app_url.'/register/?e=dbivartanw&eid='.$email);
										exit;
									}
                }

                $data = btdbFindBy('UserModel', 'email', $email);

                // Check if email is blocked
                if ($this->isEmailBlock($email) || $this->checkNegative($email,'') == 1) {
                    header('Location: ' . $app_url .'/register/?e=fbprmyulcd');
                    exit;
                }

                if (!$data['success'] || !isset($data['res'][0])) {
                    // User does not exist, proceed with registration
                    $user_mode = btflag('mode', FLAG_MODE_LIVE);

                    $uid = btutilGenerateHashId([$email, $user_mode]);
                    $login_token = btutilGeneratePassword($email . time());

                    // Gather additional user data (if needed)
                    $ip = getClientIP();
                    $ip_address = '';
                    if ($ip != '') {
                        $arr_ip = explode(',', $ip);
                        $ip_address = isset($arr_ip[0]) ? $arr_ip[0] : '';
                    }

                    // Construct user data array
                    $user_data = [
                        'user_pid' => $uid,
                        'email' => $email,
                        'mode' => $user_mode,
                        'password' => $this->wp_hasher->HashPassword($uid), // Assuming you have a WordPress-style hasher
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'login_token' => $login_token,
                        'ip_address' => $ip_address,
                        'flags' => json_encode($this->flagsData),
                        'social_login' => 2,
                    ];

                    // Insert the new user into the database
                    $res = btdbUpdate('UserModel', 'User', $user_data, 'new');
                    
										if (!$res['success']) {
                        // Handle registration errors
                        if (isset($res['msg']['email'])) {
                            header('Location: ' . $app_url .'/register/?e='.$res['msg']['email'].'&eid='.$email);
                            exit;
                        }
                        foreach ($res['msg'] as $key => $value) {
                            header('Location: ' . $app_url .'/register/?e='.$res['msg'][$key].'&eid='.$email);
                            exit;
                        }
                    }

                    // Fetch the user data after registration
                    $data = btdbFindBy('UserModel', 'email', $email);
                    if (!$data['success']) {
                        return $this->response->setJSON($this->res_error);
                    }
                }

                // User registration or retrieval successful, proceed with further actions
                $user_data = $data['res'][0];

                // Set up user session
                btsessionSetAccount($user_data, 1);

                // Log the user in
                $login_token = btutilGeneratePassword($email . time());

                if ($data['res'][0]->login_token) {
                    $login_token = $data['res'][0]->login_token;
                } else {
                    btdbUpdate('UserModel', 'User', [ 'user_id' => $data['res'][0]->user_id, 'login_token' => $login_token ], 'update');
                }
                btsessionSetAccount($user_data, 1);

                // Set authentication cookies
                $this->setAuthCookies($email, $data, $login_token);

                // Log user activity
                $event_data = obj_to_arr($user_data);
                logUserActivity('register', $event_data);

				// HUBSPOT
				$mailertype = new BTHubSpot($this->mailer_key);
				$mailertool = new BTMailer($mailertype);

				if($this->isEmailTest($email)) {
					// Do nothing
				} else if(!$mailertool->checkExist($email)) {
					$ip = getClientIP();
					$location = getGeoIP($ip);
					$location = json_decode($location);
					$country = '';

					foreach($location as $key => $value) {
                        if ($country==''){
                            $country =  isset($value->countryName) ? $value->countryName : '';
                        }
					}

					if (trim($country)==''){
						$country = isset($location->countryName) ? $location->countryName : '';
					}

					$adid = btflag("adid","");
					$landingpage = btflag("landingpage","");
					$splashdata = btflag_cookie('howdoiplantouse', '');

					$subscriber = [
                        'email' => $email,
                        'ispaid' => "0",
                        'isstart' => "1",
                        'country' => $country,
                        'adid' => $adid,
                        'landingpage' => $landingpage,
                        'howdoiplantouse' => $splashdata,
                    ];
					$result = $mailertool->addSubscriber($subscriber);
				}
				// END HUBSPOT

                // Redirect the user after successful registration
                if ($fromWP=='1'){
                    header('Location: https://ai-pro.org/apple_register_process.php?email='.$email.'&uid='.$uid);
                } else if(btflag('n_flow', '') == 'imagegen') {
                    header('Location: ' . $this->getFluxUrl());
                    exit;
                } else{
                    header('Location: ' . $app_url .'/pricing-redirect/?v='.$this->generateRandom(10).'&flowgx='.btflag('flow').'&ppgx='.btflag('ppg'));
                }
                exit;
            } else {
                header('Location: ' . $app_url .'/register/?e=aiafttlaob');
                exit;
            }
        }
    }

    //---------------------------------------------------------------------------------------------
    // Google Login
    //---------------------------------------------------------------------------------------------
    public function google_login(){
			$gClient = new Google_Client();
			$gClient->setApplicationName('Login to AI-Pro.org');
			$gClient->setClientId($this->google_client_id);
			$gClient->setClientSecret($this->google_client_secret);
			$gClient->setRedirectUri($this->google_redirect_url.'login');
			$this->google_oauthV2 = new Google_Oauth2Service($gClient);

			if(isset($_GET['code'])){
				$gClient->authenticate($_GET['code']);
				if (btsessionHas('google_token')){
					btsessionClean('google_token');
					btsessionSet('google_token',$gClient->getAccessToken());
				}else{
					btsessionSet('google_token',$gClient->getAccessToken());
				}
				header('Location: ' . getenv('GOOGLE_REDIRECT_URL').'login_process');
				exit;
			}else{
				$authUrl = $gClient->createAuthUrl();
				header('Location: ' . $authUrl);
				exit;
			}
		}

		public function google_register(){
			$gClient = new Google_Client();
			$gClient->setApplicationName('Register to AI-Pro.org');
			$gClient->setClientId($this->google_client_id);
			$gClient->setClientSecret($this->google_client_secret);
			$gClient->setRedirectUri($this->google_redirect_url.'register');
			$this->google_oauthV2 = new Google_Oauth2Service($gClient);

			if(isset($_GET['code'])){
				$gClient->authenticate($_GET['code']);
				if (btsessionHas('google_token')){
					btsessionClean('google_token');
					btsessionSet('google_token',$gClient->getAccessToken());
				}else{
					btsessionSet('google_token',$gClient->getAccessToken());
				}
				header('Location: ' . getenv('GOOGLE_REDIRECT_URL').'register_process');
				exit;
			}else{
				$authUrl = $gClient->createAuthUrl();
				header('Location: ' . $authUrl);
				exit;
			}
		}

		public function google_login_process(){
			$gClient = new Google_Client();
			$gClient->setApplicationName('Login to AI-Pro.org');
			$gClient->setClientId($this->google_client_id);
			$gClient->setClientSecret($this->google_client_secret);
            //
            $base_url = $this->getLandingUrl();
            $base_url = $base_url ? $base_url : getenv('app.baseURL');

			if(btsessionHas('google_token')){
				$gClient->setAccessToken(btsessionGet('google_token')->scalar);
			}else{
				header('Location: ' . $base_url.'/ ');
				exit;
			}

			if($gClient->getAccessToken()){
				$google_oauthV2 = new Google_Oauth2Service($gClient);

				// Get user profile data from google
				$gpUserProfile = $google_oauthV2->userinfo->get();

				// Getting user profile info
				$email = !empty($gpUserProfile['email'])?$gpUserProfile['email']:'';

				if ($email==''){
					header('Location: ' . $base_url.'/login');
					exit;
				}

				$data = btdbFindBy('UserModel', 'email', $email);

				if (!$data['success'] || !isset($data['res'][0])) {
                    //Invalid login
					header('Location: ' . $base_url.'/login/?e=pzmzwxjbnp');
					exit;
				}

				$res = $data['res'][0];
				$login_token = btutilGenerateAccessToken($email);
				if($data['res'][0]->login_token) {
						$login_token = $data['res'][0]->login_token;
				} else {
						btdbUpdate('UserModel', 'User', [ 'user_id' => $data['res'][0]->user_id, 'login_token' => $login_token ], 'update');
				}
				btsessionSetAccount($res, 1);

				$this->setAuthCookies($email, $data, $login_token);
				log_message('debug', 'login-------> ' . $email);
				logUserActivity('login');

				// LOG user login for chat usage
				$url = getenv('API_URL') . '/e/add-login-log';
				$ch_data = [
						'email' => $email
				];
				$ch = curl_init($url);
				curl_setopt($ch, CURLOPT_POST, true);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($ch_data));
				$response = curl_exec($ch);
				curl_close($ch);

                if($this->getLandingUrl()) {
                    header('Location: ' . $this->getFluxUrl());
                    exit;
                }
                if (btflag('kt8typtb') === 'druig') {
                    header('Location: ' . getenv('CHATBOTPRO_URL'));
                    exit;
                }

				header('Location: ' . getenv('app.baseURL').'/my-account');
				exit;
			}else{
                //Something went wrong
				header('Location: ' . $base_url.'/login/?e=aiafttlaob');
				exit;
			}
		}

		public function google_register_process(){
			$gClient = new Google_Client();
			$gClient->setApplicationName('Register to AI-Pro.org');
			$gClient->setClientId($this->google_client_id);
			$gClient->setClientSecret($this->google_client_secret);
            //
            $base_url = $this->getLandingUrl();
            $base_url = $base_url ? $base_url : getenv('app.baseURL');

			if(btsessionHas('google_token')){
				$gClient->setAccessToken(btsessionGet('google_token')->scalar);
			}else{
				header('Location: ' . $base_url.'/register');
				exit;
			}

			if($gClient->getAccessToken()){
				$google_oauthV2 = new Google_Oauth2Service($gClient);

				// Get user profile data from google
				$gpUserProfile = $google_oauthV2->userinfo->get();

				// Getting user profile info
				$email = !empty($gpUserProfile['email'])?$gpUserProfile['email']:'';
				$first_name = !empty($gpUserProfile['given_name'])?$gpUserProfile['given_name']:'';
				$last_name  = !empty($gpUserProfile['family_name'])?$gpUserProfile['family_name']:'';

				if ($email==''){
					header('Location: ' . $base_url.'/register');
					exit;
				}

				$user_mode = btflag('mode', FLAG_MODE_LIVE);
				$uid = btutilGenerateHashId([$email, $user_mode]);
				$login_token = btutilGeneratePassword($email . time());

                $userModel = new UserModel();
                $userExist = $userModel->checkIfEmailExist($email);
				if($userExist) {
					//User already exist
					if(btflag('kt8typtb') === 'echo'){
						setcookie('e', 'dbivartanw', time() + (86400 * 30), '/');
						setcookie('e', 'dbivartanw', time() + (86400 * 30), '/','.ai-pro.org');
						echo "<script type='text/javascript'>window.close();</script>";
						exit;
					}else{
						header('Location: ' . $base_url.	'/register/?e=dbivartanw');
						exit;
					}
				}

				if ($this->isEmailBlock($email)){
					//Email address blocked
					header('Location: ' . $base_url.'/register/?e=fbprmyulcd');
					exit;
				}

				if ($this->checkNegative($email,'') == 1){
					//Email address blocked
					header('Location: ' . $base_url.'/register/?e=fbprmyulcd');
					exit;
				}

				// User -------------------------------------------------------
				$ip = getClientIP();
				$ip_address = '';
				if ($ip!=''){
						$arr_ip = explode(',',$ip);
						$ip_address = isset($arr_ip[0]) ?  $arr_ip[0] : '';
				}

				$user_data = [
						'user_pid' => $uid,
						'email' => $email,
						'mode' => $user_mode,
						'password' => $this->wp_hasher->HashPassword( $uid ),
						'first_name' => $first_name,
						'last_name' => $last_name,
						'login_token' => $login_token,
						'ip_address' => $ip_address,
						'flags' => json_encode($this->flagsData),
						'social_login' => 1,
				];
				$res = btdbUpdate('UserModel', 'User', $user_data, 'new');
        if(!$res['success']) {
					if( isset($res['msg']['email']) ) {
						header('Location: ' . $base_url.'/register/?e='.$res['msg']['email']);
						exit;
					}
					foreach($res['msg'] as $key => $value) {
						header('Location: ' . $base_url.'/register/?e='.$res['msg'][$key]);
						exit;
					}
				}


        $data = btdbFindBy('UserModel', 'email', $email);

        if (!$data['success']) {
            return $this->response->setJSON($this->res_error);
        }
        $user_data = $data['res'][0];

				btsessionSetAccount($user_data, 1);
				//btemailSendWelcomeEmail(['send_to' => $email]);

				// HUBSPOT
				$mailertype = new BTHubSpot($this->mailer_key);
				$mailertool = new BTMailer($mailertype);

				if($this->isEmailTest($email)) {
					// Do nothing
				} else if(!$mailertool->checkExist($email)) {
					$ip = getClientIP();
					$location = getGeoIP($ip);
					$location = json_decode($location);
					$country = '';

					foreach($location as $key => $value) {
							if ($country==''){
								$country =  isset($value->countryName) ? $value->countryName : '';
							}
					}

					if (trim($country)==''){
						$country = isset($location->countryName) ? $location->countryName : '';
					}

					$adid = btflag("adid","");
					$landingpage = btflag("landingpage","");
					$splashdata = btflag_cookie('howdoiplantouse', '');

					$subscriber = [
							'email' => $email,
							'ispaid' => "0",
							'isstart' => "1",
							'country' => $country,
							'adid' => $adid,
							'landingpage' => $landingpage,
							'howdoiplantouse' => $splashdata,
						];

					$result = $mailertool->addSubscriber($subscriber);
				}
				// END HUBSPOT

				$this->setAuthCookies($email, $data, $login_token);
				$event_data = obj_to_arr($user_data);
				logUserActivity('register', $event_data);

				if(btflag('n_flow', '') === 'imagegen') {
						header('Location: ' . $this->getFluxUrl());
						exit;
				}
				header('Location: ' . $base_url .'/pricing-redirect/?v='.$this->generateRandom(10).'&flowgx='.btflag('flow_g','').'&ppgx='.btflag('ppg',''));
				exit;
			}else{
				header('Location: ' . $base_url.'/register/?e=aiafttlaob');
				exit;
			}
		}

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------
    protected function doAfterPayment($data)
    {
        if(isset($data['user_id'])) {
            $user_id = $data['user_id'];
            $user_data = [
                'user_id' => $user_id,
                'orig_email' => $data['email'],
                'is_fraud' => '',
                'status' => 'active'
            ];
            btflag_set('isProxy','no');

            btdbUpdate('UserModel', 'User', $user_data, 'update');
            btSessionSetUserSubscription((object)['user_id' => $user_id], 1);
        }
        if(isset($data['plan'])) {
            $plan = $data['plan'];
            btflag_set('user_plan', $plan->plan_name);

            btflag_set('user_plan_display', $plan->plan_type);
            if ($plan->plan_type=='promax' && $plan->payment_interval=='yearly'){
                btflag_set('user_plan_display', $plan->plan_type.' ANNUAL');
            }
            if(btsessionIsUserLoggedIn()) {
                $email = btsessionGet('USER')->email;
                btsessionGet('USER')->status = 'active';
                // btemailSendThankyouEmail(['send_to' => $email, 'plan' => $plan->label, 'plan_details' => $plan->display_txt1]);

                $price = '';
                if ($plan->trial_price!=''){
                    $price = $plan->trial_price;
                }else{
                    $price = $plan->price;
                }

                btflag_set('currency',$plan->currency);
                btflag_set('amount',$price);
                btflag_set('user_plan',$plan->plan_name);

                // MAILERLITE
                $mailertype = new BTHubSpot($this->mailer_key);
                $mailertool = new BTMailer($mailertype);

								$myDate = new \DateTime($this->date_now);
								$hubSpotDate = $myDate->format('Y-m-d');

                if($this->isEmailTest($email)) {
                    // Do nothing
                } else if(!$mailertool->checkExist($email)) {
                    $subscriber = [
                        'email' => $email,
                        'date_paid' => $this->date_now,
                        'date_paid2' => $hubSpotDate,
                        'plan_id' => $plan->plan_id,
                        'plan' => $plan->plan_type,
                        'ispaid' => "1",
                        'isstart' => "1",
                        'user_status' => "",
                        'firstname' => isset($data['firstname']) ? $data['firstname'] : '',
                        'lastname' => isset($data['lastname']) ? $data['lastname'] : ''
                    ];
                    $result = $mailertool->addSubscriber($subscriber);
                } else {
                    $subscriber = [
                        "date_paid" => $this->date_now,
                        'date_paid2' => $hubSpotDate,
                        'plan_id' => $plan->plan_id,
                        'plan' => $plan->plan_type,
                        'ispaid' => "1",
                        'isstart' => "1",
                        'user_status' => "",
                        'firstname' => isset($data['firstname']) ? $data['firstname'] : '',
                        'lastname' => isset($data['lastname']) ? $data['lastname'] : ''
                    ];
                    $result = $mailertool->updateSubscriber($email, $subscriber);
                }
                // END MAILERLITE
            }
        }

        btflag_remove('pricing');
        //SET REDIS
        $this->setRedis();

        btflag_set('daily', '');
        btflag_remove('daily');

        btflag_remove('reactivateSubscription', '');
        btflag_remove('reactivateSubscription', '', ['domain' => 'ai-pro.org']);
        btflag_remove('reactivateSubscription', '', ['domain' => '.ai-pro.org']);
        btflag_remove('reactivateSubscription', '', ['domain' => 'localhost']);
    }

    protected function isEmailTest($email)
    {
        if( strpos($email, "test") || strpos($email, "dispostable") || strpos($email, "mailinator")
        ) {
            return true;
        }
        return false;
    }

    protected function isEmailBlock($email)
    {
        if( strpos($email, "example")
        ) {
            return true;
        }
        return false;
    }

    protected function setRedis(){
        return;  // comment daw muna sabi ni sir jeff
        $email = btsessionGet('USER')->email;
        $plan_type = btsessionHas('ACCOUNT') ? strtolower(btsessionGet('ACCOUNT')->plan_type) : '';

        $created_at = btsessionGet('USER')->created_at;
        $basic_pro_logic_startdate = getenv('BASICPRO_LOGIC_STARTDATE');

        $apps_can_use  = '["all apps"]';
        if ($created_at > $basic_pro_logic_startdate){
            $apps_can_use = getenv('BASIC_APPS_LIST');
        }

        $client = new Predis\Client($this->redis_api_key);

        if ($plan_type=='pro' || $plan_type=='promax' || $plan_type=='enterprise'){
            $redis_data = [
                'plan' => $plan_type,
                'apps_available' => '["all apps"]'
            ];
        }elseif($plan_type=='basic'){
            $redis_data = [
                'plan' => $plan_type,
                'apps_available' => $apps_can_use
            ];
        }else{
            $redis_data = [
                'plan' => '',
                'apps_available' => ''
            ];
        }
        $client->set($email, json_encode($redis_data));

    }
    protected function proxyCheck($mode='live'){
        $mode_test_no_pcheck = btflag('mode_test_no_pcheck', '');

        if ($mode_test_no_pcheck=='1'){
            return ['is_proxy' => 0, 'data' => ['ip' => '', 'proxy' => '', 'vpn' => '']];
        }

        if ($mode=='test'){
            $api_key = $this->proxy_check_key['test_key'];

        }else{
            $api_key = $this->proxy_check_key['live_key'];
        }

        $proxy = '';
        $vpn = '';

        $user_ip = getClientIP();
        $user_ip = explode(",", $user_ip);
        $user_ip = trim($user_ip[0]);
        $ip_address = '';
        if ($user_ip!=''){
            $arr_ip = explode(',',$user_ip);
            $ip_address = isset($arr_ip[0]) ?  $arr_ip[0] : '';
        }

        if ($ip_address==''){
            return ['is_proxy' => 0, 'data' => ['ip' => '', 'proxy' => '', 'vpn' => '']];
        }

        try {
          $url = 'http://proxycheck.io/v2/'.$ip_address.'?key='.$api_key.'&vpn=3';
          $ch = curl_init();
          curl_setopt($ch, CURLOPT_URL, $url);
          curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; .NET CLR 1.1.4322)');
          curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
          curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
          curl_setopt($ch, CURLOPT_TIMEOUT, 5);
          $data = curl_exec($ch);
          $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
          curl_close($ch);

          if ($httpcode>=200 && $httpcode<300){
            $data = json_decode($data);
            $status = $data->status;
            if ($status=='ok'){
              $proxy = isset($data->$ip_address->proxy) ? strtolower($data->$ip_address->proxy) : '';
              $vpn = isset($data->$ip_address->vpn) ? strtolower($data->$ip_address->vpn) : '';
            }
          }
        }catch(Exception $e) {
          $proxy = '';
          $vpn = '';
        }

        if ($proxy=='yes' || $vpn=='yes'){
            return ['is_proxy' => 1, 'data' => ['ip' => $ip_address, 'proxy' => $proxy, 'vpn' => $vpn]];
        }else{
            return ['is_proxy' => 0, 'data' => ['ip' => $ip_address, 'proxy' => $proxy, 'vpn' => $vpn]];
        }
    }

    private function setMoodRating() {
        $req = $this->request->getJSON();

        if (!isset($req->user_email)) {
            return $this->response->setJSON(['success' => 0, 'res' => ['user_email' => "email is required"]]);
        }

        if (!isset($req->rating)) {
            return $this->response->setJSON(['success' => 0, 'res' => ['rating' => "rating is required"]]);
        }

        $email = $req->user_email;
        $rating = $req->rating;
        $trusPilotLogic = new TrusPilotLogic();
        $res = $trusPilotLogic->setMoodRating($email, $rating);
        $event_data = obj_to_arr($res);
        logUserActivity('set-mood-rating', $event_data);

        // Check if the rating is 3 or lower
        if ($rating <= 3) {
            $this->sendFreshDesk($email, $rating);
        }

        return $this->response->setJSON($res);
    }

    private function contactUs() {
        try {
            $concerns = [
                'Account Settings',
                'Plan and Subscription',
                'Technical Issues',
                'Others',
            ];

            $data = $this->request->getPost();

            if (!isset($data['concern']) || empty($data['concern'])) {
                $data['concern'] = null;
            }

            if (!isset($data['message']) || empty($data['message'])) {
                $data['message'] = null;
            } else {
                $data['message'] = nl2br(str_replace(['\\r', '\\n'], ["\r", "\n"], $data['message']));
            }

            $email = $data['email'] ?? null;
            $concern = $data['concern'];

            $rules = [
                'name' => 'required',
                'email' => 'required|valid_email',
            ];

            $validation = service('validation');
            $validation->setRules($rules);

            if ($concern) {
                $rules['concern'] = 'in_list['. implode(',', $concerns) .']';
            }

            if (!$validation->run($data)) {
                $errors = $validation->getErrors();

                return $this->response->setJSON([
                    'success' => 0,
                    'data' => [
                        'errors' => $errors,
                    ],
                ]);
            }

            $message = view('emails/ContactUsTemplate', $data);

            $email_data = [
                'sender_email' => BTEMAIL_SUPPORT,
                'reply_to' => $email,
                'subject' => 'AI-Pro.org Inquiry via Contact Us Form',
                'message' => $message,
            ];

            if ($concern) {
                $email_data['subject'] .= ": $concern";
            }

            $contactUsModel = new ContactUsModel();
            $contactUsModel->insert($data);

            btemailSendToSupport($email_data);

            return $this->response->setJSON([
                'success' => 1,
                'data' => [
                    'msg' => 'Successfully submited!'
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => 0,
                'data' => [
                    'message' => $e->getMessage(),
                ],
            ]);
        }

    }

		private function apple_registerv1() {
			$state = bin2hex(random_bytes(16)); // Use stronger randomness
			$req = $this->req;
			$reg_num = isset($req['reg_num']) ? $req['reg_num'] : '';

			if($reg_num=='1'){
					$redirect_url = 'https://start.ai-pro.org/apple/handle-apple-register/1';
			}else{
					$redirect_url = getenv('app.baseURL').'/apple/handle-apple-register/0';
			}

			// $redirect_url = 'https://1cbb0d650f08.ngrok.app'.'/apple/handle-apple-register/0';

			$authorize_url = 'https://appleid.apple.com/auth/authorize' . '?' . http_build_query([
					'response_type' => 'code',
					'response_mode' => 'form_post',
					'client_id' => 'org.aipro.start.login',
					'redirect_uri' => $redirect_url,
					'state' => $state,
					'scope' => 'name email',
			]);
			if(btflag('kt8typtb') === 'echo' ){
				return $this->response->setJSON(['success' => 1, 'url' => $authorize_url, 'params' => $reg_num]);
			}
	}

	private function google_registerv1(){
		$gClient = new Google_Client();
		$gClient->setApplicationName('Register to AI-Pro.org');
		$gClient->setClientId($this->google_client_id);
		$gClient->setClientSecret($this->google_client_secret);
		$gClient->setRedirectUri($this->google_redirect_url.'register');
		$this->google_oauthV2 = new Google_Oauth2Service($gClient);

		if(isset($_GET['code'])){
			$gClient->authenticate($_GET['code']);
			if (btsessionHas('google_token')){
				btsessionClean('google_token');
				btsessionSet('google_token',$gClient->getAccessToken());
			}else{
				btsessionSet('google_token',$gClient->getAccessToken());
			}
			return $this->response->setJSON(['success' => 1, 'url' => getenv('GOOGLE_REDIRECT_URL').'register_process']);
		}else{
			$authUrl = $gClient->createAuthUrl();
			return $this->response->setJSON(['success' => 1, 'url' => $authUrl]);
		}
	}

    protected function sendFreshDesk($email, $rating) {
        $api_key = "KIg2CyBRbwbEfIzLP0a";
        $password = "x";
        $yourdomain = "baytechph";

        $ticket_data = json_encode(array(
            "email" => $email,
            "subject" => 'Trustpilot Mood Survey',
            "description" => 'Mood Survey: ' . $rating,
            "priority" => 1,
            "status" => 2,
            "group_id" => 36000414027,
            "type" => "Inquiry",
            "product_id" => 36000042338,
            "tags" => ['mood-survey'],
            'responder_id' => 36060237326
        ));

        $url = "https://$yourdomain.freshdesk.com/api/v2/tickets";

        $ch = curl_init($url);

        $header[] = "Content-type: application/json";
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "$api_key:$password");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $ticket_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $server_output = curl_exec($ch);
        $info = curl_getinfo($ch);
        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $headers = substr($server_output, 0, $header_size);
        $response = substr($server_output, $header_size);

        if($info['http_code'] == 201) {
            //   echo "Ticket created successfully, the response is given below \n";
            //   echo "Response Headers are \n";
            //   echo $headers."\n";
            //   echo "Response Body \n";
            //   echo "$response \n";
            return true;
        } else {
          if($info['http_code'] == 404) {
                // echo "Error, Please check the end point \n";
                return false;
            } else {
                // echo "Error, HTTP Status Code : " . $info['http_code'] . "\n";
                // echo "Headers are ".$headers;4525
                // echo "Response are ".$response;
                return false;
            }
        }

        curl_close($ch);
    }

    public function executeSctDirectLink(){
			$request_code = isset($_REQUEST['t']) ?  trim($_REQUEST['t']) : '';

			if ($request_code==''){
					echo 'Invalid Session.';
					exit;
			}

			$redis = new RedisClient();
			$result_redis = $redis->hgetall($request_code);
			$meta_data =  isset($result_redis['meta_data']) ? $result_redis['meta_data'] : '';
			$subscription_data =  isset($result_redis['subscription_data']) ? $result_redis['subscription_data'] : '';
			$payment_data =  isset($result_redis['payment_data']) ? $result_redis['payment_data'] : '';

            $plan_id = '';
			$user_pid = '';
            $source = '';
			if ($meta_data!=''){
					$meta_data =  json_decode($meta_data);
					$plan_id = $meta_data->plan_id;
					$user_pid = $meta_data->user_pid;
					$source = isset($meta_data->source) ? strtolower($meta_data->source) : '';
			}

			$customer = '';
			$subscription_id = '';
			$current_sub_start = '';
			$current_sub_end = '';
			$trial_end = '';
			$payment_interval = '';
			if ($subscription_data!=''){
					$subscription_data =  json_decode($subscription_data);
					$customer = $subscription_data->customer_code;
					$subscription_id = $subscription_data->subscription_code;
					$period_start = $subscription_data->current_sub_start;
					$period_end = $subscription_data->current_sub_end;
					$trial_end = $subscription_data->trial_end;
					$payment_interval = $subscription_data->payment_interval;
			}

			$invoice_number = '';
			$amount = '';
			$currency = '';
			if ($payment_data!=''){
					$payment_data =  json_decode($payment_data);
					$invoice_number = $payment_data->invoice_number;
					$charge_id = $payment_data->invoice_number;
					$amount = $payment_data->amount;
					$currency = $payment_data->currency;
			}

			$base_url = getenv("app.baseURL");

            if ($plan_id == '' || $user_pid == ''){
					echo 'Invalid Session data.';
					exit;
			}

			//--------------
			$user = btdbFindBy('UserModel', 'user_pid', $user_pid);
			if($user['success'] && $user['res']) {
				$user_id = $user['res'][0]->user_id;
				$mode = $user['res'][0]->mode;
				$email = $user['res'][0]->email;
			}else{
					echo 'User account not found.';
					exit;
			}

			$plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
			if(!$plan['success'] || !$plan['res']) {
					echo 'Invalid Plan.';
					exit;
			}

			if ($payment_interval=='year' || $payment_interval=='yearly'){
					$options = isset($plan['res'][0]->options) ? $plan['res'][0]->options : '';
					if ($options!==''){
							$options = json_decode($options);
							$upgrade_id = isset($options->upgrade_id) ? $options->upgrade_id : '';
							if ($upgrade_id!=''){
									$plan_id = $upgrade_id;
									$plan = btdbFindBy('PlanModel', 'plan_id', $plan_id);
							}
					}
			}

			$isTrial = $plan['res'][0]->trial_days ? true : false;
			$uid = btutilGenerateHashId([$email, $mode]);
			$account_pid = btutilGenerateHashId([$uid]);
			$max_tokens = $plan['res'][0]->max_tokens ? $plan['res'][0]->max_tokens : '';

			$price = $isTrial ? ($plan['res'][0]->trial_price ? $plan['res'][0]->trial_price : 0) : $plan['res'][0]->price;
			$account_data = [
					'account_pid' => $account_pid,
					'user_id' => $user_id,
					'plan_id' => $plan_id,
					'merchant' => 'CardTransaction',
					'merchant_customer_id' => $customer,
					'merchant_subscription_id' => $subscription_id,
					'trial_end' => 0,
					'start_date' => $period_start,
					'end_date' => $period_end,
					'cancelled_at' => 0,
					'max_tokens' => $max_tokens
			];

			if ($isTrial){
					$account_data['trial_end'] = $period_end;
			}
			$acct = btdbUpdate('AccountModel', 'Account', $account_data, 'new');
			$acct_account_insert_id = $acct['_']['insert_id'];

			$insert_data = [
					'acctpmt_pid' => btutilGenerateHashId([$uid, $acct_account_insert_id]),
					'account_id' => $acct_account_insert_id,
					'user_id' => $user_id,
					'amount' => $price,
					'currency' => $plan['res'][0]->currency,
					'charge_id' => $charge_id,
					'merchant' => 'CardTransaction',
					'mode' => $mode
			];
			btdbUpdate('PaymentModel', 'Payment', $insert_data, 'new');

			// user log
			if (isset($insert_data)) {
					$event_data = array_merge($account_data, $insert_data);
			} else {
					$event_data = $account_data;
			}
			$event = 'create-subscription';

			$plan_type = strtoupper($plan['res'][0]->plan_type);
			if (strtoupper($plan['res'][0]->plan_type)==='PROMAX'){
					if (strtoupper($plan['res'][0]->payment_interval)=='YEARLY'){
							$plan_type = 'PRO MAX ANNUAL';
					}
			}
			$event_data = array_merge($event_data, ['plan_type' => $plan_type]);

			$event = btflag_cookie('reactivateSubscription', $event);
			logUserActivity($event, $event_data);
			// user log

			$this->doAfterPayment([
					'user_id' => $user_id,
					'plan' => $plan['res'][0],
					'email' => $email
			]);

			$plan_name = $plan['res'][0]->label;
			$plan_name = str_replace(" ","",$plan_name);

            if ($source=='wordpress'){
					header("Location: https://ai-pro.org/thank-you-page/?plan=".$plan_name);
					exit;
			}
            header("Location: ".$base_url."/thankyou/?plan=".$plan_name);
            exit;
    }

    private function sctPausePlan($params, $mode){
			$project_code = getenv('SCT_PROJECT_CODE');
			$end_point = getenv('SCT_API_ENDPOINT');
			$error_msg = '';

			$subscription_code = isset($params['subscription_id']) ? $params['subscription_id'] : '';
			$billing_cycle = isset($params['billing_cycle']) ? $params['billing_cycle'] : '';

			if ($mode=='test'){
					$mode = getenv('SCT_TEST_KEY');
			}else{
					$mode = '';
			}

			$post_field = array("subscription_code"=>$subscription_code,"project_code"=>$project_code,"billing_cycle"=>$billing_cycle,"mode"=>$mode);
			$post_field = http_build_query($post_field);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $end_point.'/api/pause-subscription/'.$project_code);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . getenv('SCT_API_KEY'),
                'Content-Type: application/x-www-form-urlencoded'
            ]);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
			$response = curl_exec($ch);

			if (curl_errno($ch)) {
				$error_msg = curl_error($ch);
			}
			curl_close($ch);

			if ($error_msg!==''){
				$response = ['status' => '0', 'message' => $error_msg, 'data' => '1'];
				return $response;
			}

			$response = json_decode($response);
			$success = isset($response->success) ? $response->success : '';
			$resume_date = isset($response->data->subscription_resume_date) ? $response->data->subscription_resume_date : '';

			if ($success=='1'){
				$resume_date = ['subscription_resume_date'=>$resume_date];
				$response = ['status' => '1', 'message' => '', 'data' => $resume_date];
			}else{
				$response = ['status' => '0', 'message' => $response->message, 'data' => ''];
			}

			return $response;
    }

    private function sctResumePlan($params, $mode){
			$project_code = getenv('SCT_PROJECT_CODE');
			$end_point = getenv('SCT_API_ENDPOINT');
			$error_msg = '';

			$subscription_code = isset($params['subscription_id']) ? $params['subscription_id'] : '';

			if ($mode=='test'){
					$mode = getenv('SCT_TEST_KEY');
			}else{
					$mode = '';
			}

			$post_field = array("subscription_code"=>$subscription_code,"project_code"=>$project_code,"mode"=>$mode);
			$post_field = http_build_query($post_field);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $end_point.'/api/unpause-subscription/'.$project_code);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . getenv('SCT_API_KEY'),
                'Content-Type: application/x-www-form-urlencoded'
            ]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
			$response = curl_exec($ch);

			if (curl_errno($ch)) {
					$error_msg = curl_error($ch);
			}
			curl_close($ch);

			if ($error_msg!==''){
			$response = ['status' => '0', 'message' => $error_msg, 'data' => '1'];
			return $response;
			}

			$response = json_decode($response);
			$success = isset($response->success) ? $response->success : '';
			$resume_date = isset($response->data->subscription_resume_date) ? $response->data->subscription_resume_date : '';

			if ($success=='1'){
			$resume_date = ['subscription_resume_date'=>$resume_date];
			$response = ['status' => '1', 'message' => '', 'data' => $resume_date];
			}else{
			$response = ['status' => '0', 'message' => $response->message, 'data' => ''];
			}
			return $response;
    }

    private function sctUpdateSubscription($params, $mode){
			$project_code = getenv('SCT_PROJECT_CODE');
			$end_point = getenv('SCT_API_ENDPOINT');
			$error_msg = '';

			$customer_code = isset($params['customer_code']) ? $params['customer_code'] : '';
			$subscription_code = isset($params['subsription_code']) ? $params['subsription_code'] : '';
			$new_plan_code = isset($params['new_plan_code']) ? $params['new_plan_code'] : '';

			if ($mode=='test'){
					$mode = getenv('SCT_TEST_KEY');
			}else{
					$mode = '';
			}

			$post_field = array(
					"customer_code"=>$customer_code,
					"subscription_code"=>$subscription_code,
					"new_plan_code"=>$new_plan_code,
					"project_code"=>$project_code,
					"mode"=>$mode
			);
			$post_field = http_build_query($post_field);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $end_point.'/api/update-subscription/'.$project_code);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . getenv('SCT_API_KEY'),
                'Content-Type: application/x-www-form-urlencoded'
          ]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
			$response = curl_exec($ch);

			if (curl_errno($ch)) {
					$error_msg = curl_error($ch);
			}
			curl_close($ch);

			if ($error_msg!==''){
			$response = ['status' => '0', 'message' => $error_msg, 'data' => '1'];
			return $response;
			}

			$response = json_decode($response);
			$success = isset($response->success) ? $response->success : '';

			if ($success=='1'){
					$subscription_code = isset($response->data->subscription_code) ? $response->data->subscription_code : '';
					$invoice_code = isset($response->data->invoice_code) ? $response->data->invoice_code : '';
					$start_date = isset($response->data->start_date) ? $response->data->start_date : '';
					$end_date = isset($response->data->end_date) ? $response->data->end_date : '';
					$amount = isset($response->data->amount) ? $response->data->amount : '';
					$subs_data = ['subscription_id'=>$subscription_code,'invoice_number'=>$invoice_code,'current_sub_start'=>$start_date,'current_sub_end'=>$end_date,'price'=>$amount];
					$response = ['status' => '1', 'message' => '', 'data' => $subs_data];
			}else{
			$response = ['status' => '0', 'message' => $response->message, 'data' => $response->data];
			}
			return $response;
    }

    private function sctCancelSubscription($customer_code, $subscription_code, $mode){
			$project_code = getenv('SCT_PROJECT_CODE');
			$end_point = getenv('SCT_API_ENDPOINT');
			$error_msg = '';

			$cancel_immediately = 0;

			if ($mode=='test'){
					$mode = getenv('SCT_TEST_KEY');
			}else{
					$mode = '';
			}

			$post_field = array(
                "customer_code"=>$customer_code,
                "project_code"=>$project_code,
                "subscription_code"=>$subscription_code,
                "cancel_immediately"=>$cancel_immediately,
                "mode"=>$mode
			);

            $post_field = http_build_query($post_field);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $end_point.'/api/cancel-subscription/'.$project_code);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . getenv('SCT_API_KEY'),
                'Content-Type: application/x-www-form-urlencoded'
            ]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
			$response = curl_exec($ch);

			if (curl_errno($ch)) {
					$error_msg = curl_error($ch);
			}
			curl_close($ch);

			if ($error_msg!==''){
			$response = ['status' => '0', 'message' => $error_msg, 'data' => '1'];
			return $response;
			}

			$response = json_decode($response);
			$success = isset($response->success) ? $response->success : '';

			if ($success=='1'){
					$response = ['status' => '1', 'message' => '', 'data' => ''];
			}else{
					$response = ['status' => '0', 'message' => '', 'data' => $response->data];
			}
			return $response;
    }

    private function sctCancelAllSubscription($customer_code, $mode){
			$project_code = getenv('SCT_PROJECT_CODE');
			$end_point = getenv('SCT_API_ENDPOINT');
			$error_msg = '';

			$cancel_immediately = 0;

			if ($mode=='test'){
					$mode = getenv('SCT_TEST_KEY');
			}else{
					$mode = '';
			}

			$post_field = array(
					"customer_code"=>$customer_code,
					"project_code"=>$project_code,
					"mode"=>$mode
			);
			$post_field = http_build_query($post_field);

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $end_point.'/api/cancel-all-subscription/'.$project_code);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . getenv('SCT_API_KEY'),
                'Content-Type: application/x-www-form-urlencoded'
            ]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
			$response = curl_exec($ch);

			if (curl_errno($ch)) {
					$error_msg = curl_error($ch);
			}
			curl_close($ch);

			if ($error_msg!==''){
			$response = ['status' => '0', 'message' => $error_msg, 'data' => '1'];
			return $response;
			}

			$response = json_decode($response);
			$success = isset($response->success) ? $response->success : '';

			if ($success=='1'){
					$response = ['status' => '1', 'message' => '', 'data' => ''];
			}else{
					$response = ['status' => '0', 'message' => '', 'data' => $response->data];
			}
			return $response;
    }

    private function getCacheRedis($key, $aiwp_app_id = "", $getRedis = false)
    {
        $redis = new RedisAemoClient();
        $client = $redis->getClient();
        if($aiwp_app_id) {
            $aiwp_app_id = strtolower($aiwp_app_id);
            $res = $redis->hget("redisAllowApp", $aiwp_app_id);
            if($res) $getRedis = true;
        }
        if(!$getRedis) return false;
        // Get the value of the key
        $res = $redis->hgetall($key);
        return $res;
    }

    private function getLandingUrl()
    {
        $url = "";
        $aiwp_url = "https://ai-pro.org";
        if(strpos(base_url(), "staging")) {
            $aiwp_url = "https://staging.ai-pro.org";
        }

        if(strpos(base_url(), "uat")) {
            $aiwp_url = "https://uat.ai-pro.org";
        }
        switch(btflag('n_flow', '')) {
            case 'landing':
                $url = $aiwp_url . "/landing";
                break;
            case 'image':
                $url = $aiwp_url . "/landing-image";
                break;
            case 'imagegen':
                $url = $aiwp_url . "/landing-imagegen";
                break;
            case 'aiart':
                $url = $aiwp_url . "/landing-aiart";
                break;
            case 'get-started':
                $url = $aiwp_url . "/get-started";
                break;
            default:
                break;
        }

        return $url;
    }

    private function getFluxUrl()
    {
        if(strpos(base_url(), "staging")) {
            return "https://staging.flux.ai-pro.org";
        }

        if(strpos(base_url(), "staging")) {
            return "https://uat.flux.ai-pro.org";
        }
        return "https://flux.ai-pro.org";
    }
}
