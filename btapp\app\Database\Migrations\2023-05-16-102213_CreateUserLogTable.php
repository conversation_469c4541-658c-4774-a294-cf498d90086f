<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateUserLogTable extends Migration
{
    private $table = "user_log";
    public function up()
    {
		$this->db->disableForeignKeyChecks();

		$this->forge->addField([
			'userlog_id' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => false,
				'auto_increment' => true,
			],
			'userlog_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'user_pid' => [
				'type' => 'CHAR',
                'constraint' => 36,
				'null' => true,
            ],
			'event' => [
				'type' => 'VARCHAR',
				'constraint' => 50,
				'null' => true,
			],
			'event_data' => [
				'type' => 'VARCHAR',
				'constraint' => 500,
				'null' => true,
			],
			'created_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'updated_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
			'deleted_at' => [
				'type' => 'DATETIME',
				'null' => false,
			],
		]);

		$this->forge->addKey('userlog_id', true);
		$this->forge->addKey(['user_pid'], false, false, 'start_userlog_user_pid_IDX');

		$this->forge->createTable($this->table);
		$this->db->enableForeignKeyChecks();
    }

    public function down()
    {
        // comment out this on SERVER DEPLOYMENT
        //$this->forge->dropTable($this->table);
    }
}
