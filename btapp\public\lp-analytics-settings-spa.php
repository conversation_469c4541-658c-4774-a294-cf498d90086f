<!---Mixpanel Start----->
<?php
		$actual_link = htmlspecialchars("$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]", ENT_QUOTES, 'UTF-8');
		$arr_link = explode('/', $actual_link);
		$slug1 = $arr_link[0] ?? '';
		$slug2 = $arr_link[1] ?? '';
		
		// Get the actual slug from URL
		$mixpanel_name = 'home';
		if ($slug2 && strpos($slug2, '?') !== false) {
			$mixpanel_name = explode('?', $slug2)[0];
		} elseif ($slug2) {
			$mixpanel_name = $slug2;
		}
	?>
	<script>
		let mixpanelLoaded = false;	
		if (!window.mixpanelLoaded) {
			(function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");
			for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?
			MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\/\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);
			mixpanel.init('510eae1e2d2a79bceee18c49bece1c6a', { debug: "false" });
			window.mixpanelLoaded = true;
		}
		var mxname = "<?php echo $mixpanel_name; ?>";
		var mxuserEmail = "<?php echo get_flag('user_email') ? get_flag('user_email') : ""; ?>";
		var mxuserPlan = "<?php echo get_flag('user_plan') ?? get_flag('user_plan') ?? ''; ?>";
		var mxrewardfulVia = "<?php echo get_flag('via') ? get_flag('via') : '';?>";
		var mxcurrency = "<?php echo get_flag('currency') ? get_flag('currency') : "";?>";
		var mxamount = "<?php echo get_flag('amount') ? get_flag('amount') : "";?>";
		var mxkeyword = "<?php echo get_flag('keyword') ? get_flag('keyword') : "";?>";
		var mxemailid = "<?php echo get_flag('emailid') ? get_flag('emailid') : "";?>";
		var mxadid = "<?php echo get_flag('adid') ? get_flag('adid') : "";?>";
		var mxppg = "<?php echo get_flag('ppg') ? get_flag('ppg') : "";?>";
		var mxpmt = "<?php echo get_flag('pmt') ? get_flag('pmt') : "";?>";
		var mxhowdoiplantouse = "<?php echo get_flag('howdoiplantouse') ? get_flag('howdoiplantouse') : "";?>";
		var mxremakemedloption = "<?php echo get_flag('remakemedloption') ? get_flag('remakemedloption') : "";?>";
		var mxtheme = <?php echo json_encode(get_flag('kt8typtb') ?: ""); ?>;
		var mxlocales = "<?php echo get_flag('locales') ? get_flag('locales') : 'en'; ?>";
		var mxtransactionid = "<?php echo get_flag('transid') ? get_flag('transid') : ''; ?>";

		// Track the actual slug name
		mixpanel.track(mxname, {
			'user_email': mxuserEmail,
			'user_plan': mxuserPlan,
			'rewardful_via': mxrewardfulVia,
			'currency': mxcurrency,
			'amount': mxamount,
			'keyword': mxkeyword,
			'email_id': mxemailid,
			'ad_id': mxadid,
			'ppg': mxppg,
			'payment': mxpmt,
			'how_do_i_plan_to_use': mxhowdoiplantouse,
			'remake_medl_option': mxremakemedloption,
			'theme': mxtheme,
			'locales': mxlocales,
			'transaction_id': mxtransactionid,
			'url': window.location.href,
			'referrer': document.referrer
		});
	</script>
<!---Mixpanel End----->

<!-- Google Tag Code Start-->
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-70TZ628CHH"></script>
	<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());
		gtag('config', 'G-70TZ628CHH');
		gtag('config', 'AW-532672904');
	</script>
	<?php
		$actual_link = "$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
		if (strpos($actual_link, 'thankyou') !== false) { ?>
			<script>
				setTimeout(function() {
					window.addEventListener('load', function(){
						const ty_url = window.location.href.toLowerCase();
						if (ty_url.includes("?plan=basic")) {
							gtag('event', 'conversion', {
							'send_to': 'AW-532672904/tXcgCK_JtpMYEIjj__0B',
							'value': "<?php echo get_flag('amount'); ?>",
							'currency': "<?php echo get_flag('currency'); ?>",
							'transaction_id': "<?php echo get_flag('transid'); ?>"
							});
						} else if (ty_url.includes("?plan=pro")) {
							gtag('event', 'conversion', {
							'send_to': 'AW-532672904/AUuSCMiqheoYEIjj__0B',
							'value': "<?php echo get_flag('amount'); ?>",
							'currency': "<?php echo get_flag('currency'); ?>",
							'transaction_id': "<?php echo get_flag('transid'); ?>"
							});

						} else {
							gtag('event', 'conversion', {
							'send_to': 'AW-532672904/LXT6CK-gpMQYEIjj__0B',
							'value': "<?php echo get_flag('amount'); ?>",
							'currency': "<?php echo get_flag('currency'); ?>",
							'transaction_id': "<?php echo get_flag('transid'); ?>"
							});
						}
					});
				}, 10000);
			</script>
	<?php } ?>
<!-- Google Tag Code End-->

<!-- Start of HubSpot Embed Code -->
	<script type="text/javascript" id="hs-script-loader" src="//js.hs-scripts.com/********.js"></script>
<!-- End of HubSpot Embed Code -->

<!-- VWO Start -->
	<?php 
	ob_start();
	if ((bool)btutilIsVwoOn() == true) {
		ob_end_clean();
	?>
	<!-- Start VWO Async SmartCode -->
	<script type='text/javascript' id='vwoCode'>
	window._vwo_code=window._vwo_code || (function() {
	var account_id=642749,
	version=1.4,
	settings_tolerance=2000,
	library_tolerance=2500,
	use_existing_jquery=false,
	is_spa=1,
	hide_element='body',
	/* DO NOT EDIT BELOW THIS LINE */
	f=false,d=document,vwoCodeEl=document.querySelector('#vwoCode'),code={use_existing_jquery:function(){return use_existing_jquery},library_tolerance:function(){return library_tolerance},finish:function(){if(!f){f=true;var e=d.getElementById('_vis_opt_path_hides');if(e)e.parentNode.removeChild(e)}},finished:function(){return f},load:function(e){var t=d.createElement('script');t.fetchPriority='high';t.src=e;t.type='text/javascript';t.innerText;t.onerror=function(){_vwo_code.finish()};d.getElementsByTagName('head')[0].appendChild(t)},getVersion:function(){return version},getMatchedCookies:function(e){var t=[];if(document.cookie){t=document.cookie.match(e)||[]}return t},getCombinationCookie:function(){var e=code.getMatchedCookies(/(?:^|;)\s?(_vis_opt_exp_\d+_combi=[^;$]*)/gi);e=e.map(function(e){try{var t=decodeURIComponent(e);if(!/_vis_opt_exp_\d+_combi=(?:\d+,?)+\s*$/.test(t)){return''}return t}catch(e){return''}});var i=[];e.forEach(function(e){var t=e.match(/([\d,]+)/g);t&&i.push(t.join('-'))});return i.join('|')},init:function(){window.settings_timer=setTimeout(function(){_vwo_code.finish()},settings_tolerance);var e=d.createElement('style'),t=hide_element?hide_element+'{opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;}':'',i=d.getElementsByTagName('head')[0];e.setAttribute('id','_vis_opt_path_hides');vwoCodeEl&&e.setAttribute('nonce',vwoCodeEl.nonce);e.setAttribute('type','text/css');if(e.styleSheet)e.styleSheet.cssText=t;else e.appendChild(d.createTextNode(t));i.appendChild(e);var n=this.getCombinationCookie();this.load('https://dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(d.URL)+'&f='+ +is_spa+'&vn='+version+(n?'&c='+n:''));return settings_timer}};window._vwo_settings_timer = code.init();return code;}());
	</script>
	<?php } else {
		ob_end_clean();
	} ?>
<!-- VWO End -->

<!-- Facebook Pixel Code -->
	<script>
	!function(f,b,e,v,n,t,s)
	{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
	n.callMethod.apply(n,arguments):n.queue.push(arguments)};
	if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
	n.queue=[];t=b.createElement(e);t.async=!0;
	t.src=v;s=b.getElementsByTagName(e)[0];
	s.parentNode.insertBefore(t,s)}(window, document,'script',
	'https://connect.facebook.net/en_US/fbevents.js');
	fbq('init', '***************');
	fbq('track', 'PageView');
	</script>
	<noscript><img height="1" width="1" style="display:none"
	src="https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1"
	/></noscript>
<!-- Facebook Pixel Code End -->

<!-- FullStory -->
	<script>
	window['_fs_host'] = 'fullstory.com';
	window['_fs_script'] = 'edge.fullstory.com/s/fs.js';
	window['_fs_org'] = '129M5P';
	window['_fs_namespace'] = 'FS';
	(function(m,n,e,t,l,o,g,y){
		if (e in m) {if(m.console && m.console.log) { m.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].');} return;}
		g=m[e]=function(a,b,s){g.q?g.q.push([a,b,s]):g._api(a,b,s);};g.q=[];
		o=n.createElement(t);o.async=1;o.crossOrigin='anonymous';o.src='https://'+_fs_script;
		y=n.getElementsByTagName(t)[0];y.parentNode.insertBefore(o,y);
		g.identify=function(i,v,s){g(l,{uid:i},s);if(v)g(l,v,s)};g.setUserVars=function(v,s){g(l,v,s)};g.event=function(i,v,s){g('event',{n:i,p:v},s)};
		g.anonymize=function(){g.identify(!!0)};
		g.shutdown=function(){g("rec",!1)};g.restart=function(){g("rec",!0)};
		g.log = function(a,b){g("log",[a,b])};
		g.consent=function(a){g("consent",!arguments.length||a)};
		g.identifyAccount=function(i,v){o='account';v=v||{};v.acctId=i;g(o,v)};
		g.clearUserCookie=function(){};
		g.setVars=function(n, p){g('setVars',[n,p]);};
		g._w={};y='XMLHttpRequest';g._w[y]=m[y];y='fetch';g._w[y]=m[y];
		if(m[y])m[y]=function(){return g._w[y].apply(this,arguments)};
		g._v="1.3.0";
	})(window,document,window['_fs_namespace'],'script','user');

	// IDENTIFY
	FS.identify("{{PAGE.include_fullstory.email}}", {email: "{{PAGE.include_fullstory.email}}"});
	</script>
<!-- FullStory -->

<!-- Rewardful -->
	<script defer>(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');</script>
	<script defer async src='https://r.wdfl.co/rw.js' data-rewardful='c8db04'></script>
<!-- Rewardful -->

<!-- Twitter -->
	<script>
	!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
	},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
	a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
	twq('config','oebtr');
	</script>
<!-- Twitter -->

<!-- Tiktok -->
	<script>
	!function (w, d, t) {
	w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};

	ttq.load('CMB3VLJC77UDE1VA04E0');
	ttq.page();
	}(window, document, 'ttq');
	</script>
<!-- Tiktok -->
