{% if PAGE.include_mixpanel.mixpanel_name in [
    'subscription-plan-basic-pro',
    'subscription-plan',
    'subscription-plan-pro-max',
    'pricing',
    'signup',
    'subscription-plan-toggle-vp-01',
    'subscription-plan-vp-01',
    'subscription-plan-vp-02',
    'subscription-plan-vp-03',
    'subscription-plan-2',
    'subscription-plan-toggle',
    'subscription-plan-swipe-02',
    'plans-and-pricing-b',
    'subscription-plan-swipe-03',
    'plans-and-pricing-x',
    'subscription-plan-swipe-04',
    'plans-and-pricing-f4',
    'plans-and-pricing-d',
    'subscription-plan-swipe-05',
    'aurora-pricing',
    'subscription-e',
    'subscription-plan-swipe-01',
    'subscription-plan-ar',
    'plans-and-pricing-c',
    'plans-and-pricing-enzo',
    'pricing-redirect'
] and PAGE.include_mixpanel.f_sle == 'on' %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    <script src="https://assets.ai-pro.org/assets/assets/js/Action_popup.js?v={{PAGE.include_mixpanel.time}}"></script>
    <style>
        body{
            font-family: "Inter", sans-serif;
            color: black;
        }
        .main-popup-modal-container {
            font-family: "Inter", sans-serif;
            display: none;
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 10000;
            transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
        }
        .modal-content {
            display: flex;
            flex-direction: column;
            width: 90%;
            height: auto;
            background: rgb(252,255,237);
            background: linear-gradient(169deg, rgba(252,255,237,1) 0%, rgba(255,255,255,1) 48%, rgba(228,238,255,1) 100%);
            padding: 20px;
            border-radius: 25px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            overflow-y: auto;
            padding: 5px 55px 25px 55px;;
            position: relative;
            opacity: 0;
            transform: translate(1000px, 500px) scale(0);
            transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
        }
        /* @keyframes bounceIn {
            0% {
                transform: translate(1000px, 500px) scale(0);
                opacity: 0;
            }
            60% {
                transform: translate(100px, 100px) scale(1.05);
                opacity: 1;
            }
            80% {
                transform: translate(100px, 100px) scale(0.98);
            }
            100% {
                transform: translate(100px, 100px) scale(1);
            }
        } */
        @keyframes bounceIn {
            0% {
                transform: translate(1000px, 500px) scale(0);
                opacity: 0;
            }
            50% {
                transform: translate(110px, 110px) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(100px, 100px) scale(1);
                opacity: 1;
            }
        }

        @keyframes bounceOut {
            0% {
                transform: translate(100px, 100px) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(110px, 110px) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(1000px, 500px) scale(0);
                opacity: 0;
            }
        }


        @media only screen and (max-width: 768px) {
            /* Subtle bounce-in animation */
            @keyframes bounceInMobile {
                0% { 
                    transform: translate(100px, 500px) scale(0); 
                    opacity: 0; 
                    bottom: 100px; 
                    right: 20px; 
                }
                60% { 
                    transform: translate(0px, 0px) scale(1.02); 
                    opacity: 0.9; 
                }
                100% { 
                    transform: translate(0px, 0px) scale(1); 
                    opacity: 1; 
                }
            }
            
            @keyframes bounceOutMobile {
                0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
                100% { transform: translate(100px, 500px) scale(0); opacity: 0; bottom: 100px; right: 20px; }
            }

            .modal-content.bounce-in {
                animation: bounceInMobile 0.4s ease-in-out forwards;
                /* animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275); */
                animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }
            
            .modal-content.bounce-out {
                animation: bounceOutMobile 0.3s forwards;
            }
        }
        @media only screen and (max-width: 320px) {
            /* Subtle bounce-in animation */
            @keyframes bounceInMobile {
                0% { 
                    transform: translate(100px, 500px) scale(0); 
                    opacity: 0; 
                    bottom: 100px; 
                    right: 20px; 
                }
                60% { 
                    transform: translate(0px, 0px) scale(1.01); 
                    opacity: 0; 
                }
                100% { 
                    transform: translate(0px, 0px) scale(1); 
                    opacity: 1; 
                }
            }
            
            @keyframes bounceOutMobile {
                0% { transform: translate(0px, 0px) scale(1); opacity: 1; }
                100% { transform: translate(100px, 500px) scale(0); opacity: 0; bottom: 100px; right: 20px; }
            }

            .modal-content.bounce-in {
                animation: bounceInMobile 0.4s ease-in-out forwards;
                /* animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275); */
                animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }
            
            .modal-content.bounce-out {
                animation: bounceOutMobile 0.3s forwards;
            }
        }




        .bounce-in {
            animation: bounceIn 0.5s forwards;
            animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .bounce-out {
            animation: bounceOut 0.3s forwards;
        }
        .cancel-icon{
            width: 15px;
            height: 15px;
        }
        @media only screen and (min-width: 2048px) {
            .AiOutline-button {
                display: none;
            }
        }
        @media only screen and (min-width: 1440px) {
            .modal-content {
                width: 70%;
                max-width: 70vh;
                max-height: 90%;
            }
            .white-blurry-bg{
                height: 168px;
                padding-top: 16px;
            }
            .plan-details {
                padding: 24px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            }
        }
        @media only screen and (max-width: 1440px) {
            .modal-descriptions{
                min-height: 64vh; 
            }
            .modal-content {
                width: 70%;
                max-width: 80vh;
                max-height: 96%;
            }
            .header-logo {
                width: 180px;
                height: 100%;
            }
            .white-blurry-bg{
                height: 160px;
                padding-top: 6px;
            }
            .plan-details {
                padding: 20px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            }
        }
        @media only screen and (min-width: 1367px) {
            .trial-details .trial-label{
                font-size: 32px !important;
            }
            .plan-details span {
                font-size: 30px;
                font-weight: 500;
            }
            .blurry-container{
                bottom: 28px;
            }
            .plan-row{
                padding: 0px 17px 0px 17px;
                margin-bottom: -23px;
            }
        }
        @media only screen and (max-width: 1366px) {
            .modal-content {
                max-width: 100vh;
                max-height: 98%;
            }
            .modal-descriptions{
                min-height: 58vh; 
            }
            .blurry-container {
                bottom: 12px;
            }
            .plan-details span {
                font-size: 24px;
            }
            .trial-details .trial-label{
                font-size: 24px !important;
            }
            .blurry-container{
                bottom: 16px;
            }
            .plan-details {
                padding: 12px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            }
            .white-blurry-bg{
                height: 124px;
                padding-top: 6px;
            }
            .plan-details {
                padding: 12px 20px 20px 20px;
                clip-path: polygon(0 0, 88% 0, 58% 100%, 0 100%);
            }
            .plan-row{
                padding: 0px 17px 0px 17px;
                margin-bottom: -32px;
            }
        }
        @media only screen and (min-width: 1281px) {
            .modal-header span{
                font-size: 18px;
            }
            .trial-details div, .price-dollar{
                font-size: 14px;
            }
            .plan-feature{
                font-size: 12px;
            }
            .new-con{
                font-size: 12px;
            }
            .modal-title-sub-container span, 
            .modal-title-sub-container-sticky span, 
            .modal-title-sub-container .title-pop, 
            .modal-title-sub-container-sticky .title-pop {
                font-size: 15px
            }
            .sub-desc-box{
                gap:20px;
            }
        }
        @media only screen and (max-width: 1280px) {
            .modal-content {
                max-width: 95vh !important;
                max-height: 95%;
                width: 70%;
            }
            .blurry-container {
                bottom: 12px !important;
            }
            .white-blurry-bg{
                height: 114px;
                padding-top: 8px;
            }
            .modal-descriptions{
                max-height: 53vh !important;
            }
            .plan-details {
                padding: 12px 20px 16px 20px !important; 
            }
            /* .plan-details span{
                font-size: 28px !important;
            } */
            .sub-desc-box{
                padding-bottom: 122px !important;
            }
            .trial-details .trial-label{
                font-size: 21px;
            }
            .header-logo {
                width: 138px !important;
            }
            .modal-header{
                gap: 0px !important;
            }
            .modal-header span{
                font-size: 14px;
            }
            .plan-details span {
                font-size: 22px;
                font-weight: 500;
            }
            .trial-details div, .price-dollar{
                font-size: 11px;
            }
            .plan-feature{
                font-size: 10px;
            }
            .new-con{
                font-size: 10px;
            }
            .modal-title-sub-container span, 
            .modal-title-sub-container-sticky span, 
            .modal-title-sub-container .title-pop, 
            .modal-title-sub-container-sticky .title-pop {
                font-size: 12px
            }
            .plan-row{
                padding: 0px 0px 0px 12px;
                margin-bottom: -32px;
            }
            /* .sub-desc-box >*:last-child{
                margin-top: -8px;
            } */
            .modal-title-container{
                margin-bottom: -10px;
            }
            .sub-desc-box{
                gap:28px;
            }
            .blurry-container{
                bottom: 28px;
            }
            /* .plan-details {
                padding: 24px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            } */
            .plan-details {
                padding: 0px 20px 20px 20px;
                clip-path: polygon(0 0, 88% 0, 58% 100%, 0 100%);
            }
        }
        
        @media only screen and (max-width: 768px) {
            .white-blurry-bg{
                height: 124px;
                padding-top: 6px;
            }
            .modal-descriptions{
                max-height: 66vh !important;
            }
            .modal-content {
                width: 100%;
                height: 98vh; 
                border-radius: 0; 
                padding: 20px 15px;
                gap: 14px;
                transform: translate(100px, 100px) scale(1);
            }
            .plan-details {
                padding: 0px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            }
            .trial-details .trial-label {
                font-size: 22px;
            }
            .new-con{
                padding: 0px 10px 0px 10px;
            }
            .blurry-container{
                width: 95% !important;
            }
            .plan-details span {
                font-size: 25px;
                font-weight: 500;
            }
            .sticky-title {
                width: 92.3% !important;
            }
            .banner-container{
                right: 35px;
                font-size: 20px;
                padding: 10px;
                bottom: 12px !important;
            }
            .free-three-trial-button{
                font-weight: 600;
            }
            .modal-header span{
                font-size: 18px;
            }
            .plan-feature{
                font-size: 12px;
            }
            .modal-title-sub-container span, 
            .modal-title-sub-container-sticky span, 
            .modal-title-sub-container .title-pop, 
            .modal-title-sub-container-sticky .title-pop {
                font-size: 15px
            }
            .plan-row{
                padding: 0px 17px 0px 17px;
                margin-bottom: -24px;
            }
            .modal-title-container {
                margin-top: 0px
            }
            .sub-desc-box{
                gap:20px;
            }
        
        }
        @media only screen and (min-width: 768px) {
            .new-con{
                padding: 0px 15px 0px 15px;
            }
        
            .banner-container{
                right: 75px;
                font-size: 20px;
                padding: 10px;
            }
            .free-three-trial-button{
                font-weight: 700;
            }
            /* .white-blurry-bg{
                height: 168px;
                padding-top: 16px;
            } */
        }
        @media only screen and (max-width: 375px) {
            .modal-content > * {
            margin-bottom: 20px; 
            }
            .modal-content > *:last-child {
            margin-bottom: 0;
            }
            .plan-details span {
                font-size: 24px;
                font-weight: 500;
            }
            .plan-details {
                padding: 28px 20px 20px 20px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
                -webkit-clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
                mask-image: linear-gradient(0deg, transparent 0%, #f0f0f0 0%);
            }
            .pricing-container {
                width: 98%;
            }
            .white-blurry-bg{
                padding-top: 48px;
            }
            .modal-title-container{
                margin-bottom: 0px;
            }
        }
        @media only screen and (min-width: 375px) {
            .modal-descriptions{
                max-height: 64vh;
            }
        }
        @media only screen and (min-width: 431px) {
            .modal-content{
                gap: 14px;
            }
            /* .white-blurry-bg{
                padding-top: 40px;
                height: 192px;
            } */
            .modal-descriptions{
                max-height: 56vh;
            }
            .plan-checkmarks{
                gap: 10px;
            }
            .pricing-container{
                width: 100%;
            }
            .title-trial-sub-con{
                gap: 4px;
            }
            .sticky-title{
                width: 94.3%;
                left: 16px;
            }
            .mini-logo-header{
                width: 100%;
            }
            .count-box span, .count-box p {
                font-size: 14px;
                margin-bottom: -12px;
            }
        }
        @media only screen and (max-width: 430px) {
            .modal-content{
                height: 105%;
                max-height: 100%;
                overflow-y: scroll;
                gap:0;
            }
            /* .modal-descriptions {
                max-height: 70%;
            }
             */
            .modal-content > *{
                margin-bottom: 8px; 
            }
            .modal-content > *:last-child{
                margin-top: 8px;
                margin-bottom: 8px;
            }
            .header-logo{
                width: 150px;
            }
            .modal-descriptions{
                max-height: 60.2vh;
            }
            .trial-details{
                padding: 2px 10px 2px 0px !important;
            }
            .trial-details div, .price-dollar {
                font-size: 12px;
            }
            .plan-details {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: start;
                padding-top: 18px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
                -webkit-clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
                mask-image: linear-gradient(0deg, transparent 0%, #f0f0f0 0%);
            }
            .white-blurry-bg{
                padding-top: 6px;
                height: 128px;
            }
            .plan-checkmarks{
                gap: 2px;
            }
            .pricing-container{
                width: 98%;
            }
            .modal-header span{
                font-size: 16px;
            }
            .modal-header{
                gap: 0px !important;
            }
            .modal-header > *{
                margin-top: 0px;
                margin-bottom: 10px; 
            }
            .modal-header > *:last-child{
                margin-bottom: 8px;
            }
            .title-trial-sub-con{
                gap: 0px;
            }
            .sticky-title {
                width: 92.3%;
                left: 16px;
            }
            .banner-container{
                right: 32px;
                font-size: 12px;
                padding: 4px;
            }
            .mini-logo-header{
                width: 74%;
            }
            .count-box span, .count-box p {
                font-size: 12px;
                margin-bottom: -9px;
            }
            .blurry-container{
                bottom: 15px;
            }
            .sub-desc-box{
                gap: 0px;
            }
            .sub-desc-box > *:last-child{
            
                margin-bottom: 0px;
            }
            .sub-desc-box > *{
    
                margin-bottom: 20px;
            }
            .plan-row{
                padding: 0px 17px 0px 17px;
                margin-bottom: -20px;
            }
            .plan-row-sub-con, .modal-title-sub-container-sticky div, .modal-title-sub-container div {
                gap: 0px !important;
            }
            .plan-row-sub-con > *, .modal-title-sub-container-sticky div > *, .modal-title-sub-container div > * {
                margin-right: 8px; 
            }
            .modal-title-sub-container div > * {
                margin-right: 12px; 
            }
            .plan-row-sub-con > *:last-child, .modal-title-sub-container-sticky > *:last-child, .modal-title-sub-container div > *:last-child {
                margin-right: 0;
            }    
            .three-dots-icon{
                gap: 0px !important;
            }
            .three-dots-icon > * {
            margin-bottom: 8px; 
            }
            .three-dots-icon > *:last-child {
            margin-bottom: 0; 
            }
            .plan-details span {
                font-size: 20px;
            }
            .plan-feature{
                font-size: 10px !important;
            }
            .countdown-container {
                gap: 0px !important;
            }
            .countdown-container > *{
                margin-right: 4px; 
                margin-left: 4px;
            }

            .countdown-container > *:last-child {
                margin-right: 0px;
                margin-left: 4px;
            }
            .sub-desc-box{
                padding-bottom: 125px !important;
            }
            .header-logo {
                width: 200px !important;
                height: 110px;
            }
        }
        @media only screen and (max-width: 320px) {
            .modal-content{
                padding-top: 20px;
                height: 105%;
                overflow-y: scroll;
            }
            .modal-content > *{
                margin-top: 10px;
                margin-bottom: 20px; 
            }
            .modal-content > *:last-child{
                margin-top: 10px;
                margin-bottom: 20px;
            }
    
            .modal-header {
                margin-top: 42px;
                margin-bottom: 40px; 
            }
            .modal-header span {
                font-size: 14px;
            }
            .modal-header > *{
                margin-top: 42px;
                margin-bottom: 40px; 
            }
    
            .modal-header > *:last-child{
                margin-top: 0px;
                margin-bottom: 20px;
            }
            .sub-desc-box > *:last-child{
            margin-bottom: 0px;
            }
            .sub-desc-box > *{
            margin-bottom: 4px;
            }
            .sub-desc-box{
                gap: 0;
                padding-bottom: 150px !important;
            }
            .modal-content
            {
                gap: 0px;
            }
            .modal-descriptions{
                min-height: 105%;
                margin-top: 60px;
            }
            .modal-descriptions > * {
            margin-bottom: 45px; 
            }

            .modal-descriptions > *:last-child {
            margin-bottom: 0;
            }
            .three-dots-icon{
                gap: 0px;
            }
            .three-dots-icon > * {
            margin-bottom: 8px; 
            }
            .three-dots-icon > *:last-child {
            margin-bottom: 0; 
            }

            .plan-row-sub-con > *, .modal-title-sub-container-sticky div > *{
            margin-right: 8px; 
            }
            .modal-title-sub-container div > * {
                margin-right: 13px; 
            }
            .plan-row-sub-con > *:last-child, .modal-title-sub-container-sticky > *:last-child, .modal-title-sub-container > *:last-child {
            margin-right: 0;
            }
            .plan-details span {
                font-size: 18px;
                font-weight: 500;
            }
            /* .modal-descriptions {
                max-height: 300vh;
            } */
            .modal-title-sub-container span, 
            .modal-title-sub-container-sticky span, 
            .modal-title-sub-container .title-pop, 
            .modal-title-sub-container-sticky .title-pop {
                font-size: 13px
            }
            .sticky-title{
                width: 90.3%;
                left: 14px;
            }
            .plan-feature{
                font-size: 10px;
            }
            .plan-row{
                margin-bottom: -20px;
                padding: 0px 7px 0px 17px;
            }
            .new-con{
                font-size: 10px;
            }
            .white-blurry-bg {
                padding-top: 32px;
                height: 104px;
            }
            .plan-details {
                padding: 22px 20px 22px 12px;
                clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
                -webkit-clip-path: polygon(0 0, 100% 0, 64% 100%, 0 100%);
            }
            .pricing-container {
                width: 97%;
            }
            .trial-details div, .price-dollar {
                font-size: 10px;
            }
            .trial-details .trial-label {
                font-size: 18px;
            }
            .blurry-container{
                bottom: 14px;
            }

        }
        @media only screen and (min-width: 321px) {
            /* .modal-content {
                max-height: 100%;
            } */
            .three-dots-icon {
                gap: 10px;
            }
            
        }
        /* @media only screen and (max-height: 932px) {
                .modal-descriptions {
                    max-height: 68%;
                }
            } */


        .cancel-icon{
            position: absolute;
            top: 32px;
            right: 24px;
            cursor: pointer;
        }
        .banner-container {
            position: fixed;
            bottom: 40px;
            background-color: #FAFAFA;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            cursor: pointer;
            z-index: 1001;
        }
        .hide-banner {
            opacity: 0;
            transform: translateY(-100%);
            pointer-events: none;
        }
        .modal-title-container{
            padding: 6px;
        }
        .modal-title-container, .sticky-title {
            display: flex;
            flex-direction: column;
        }
        .title-sticky {
            padding-top: 14px;
            color: #007bff;
            transition: transform 0.2s ease, opacity 0.2s ease;
            scroll-behavior: smooth;
        }
        .sticky-title{
            position: absolute;
            top: 3px;
            background: #ffffff;
            padding-bottom: 10px;
            z-index: 8;
            scroll-behavior: smooth;
        }
        .modal-title-sub-container-sticky div{
            background-color: white;
            padding-top: 15px;
        }
        .modal-title-sub-container,.modal-title-container div, .modal-title-sub-container-sticky div{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }
        .modal-title-sub-container .title-pop, .modal-title-sub-container-sticky .title-pop {
            color: #007bff;
            font-weight: bold;
        }
        .modal-title-sub-container span, .modal-title-sub-container-sticky span{
            font-weight: bold;
        }
        
        .modal-title-sub-container-sticky{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
        .underline {
            width: 100%;
            height: 1px;
            background-color: #007bff;
            margin-top: 5px; 
        }
        .modal-descriptions{
            display: flex;
            flex-direction: column;
            gap:30px;
            padding: 14px 10px;
            position: relative;
            box-shadow: 0 0 3px 1px rgba(40, 114, 249, 0.27);
            border-radius: 12px;
            background-color: white;
        }
        .sub-desc-box{
            display: flex;
            flex-direction: column;
            overflow-y: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
            padding-bottom: 154px;
            scroll-behavior: smooth;
        }
        .sub-desc-box::-webkit-scrollbar {
            display: none;
        }
        .plan-container {
            display: flex;
            flex-direction: column;
        }
        .plan-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
        .plan-row-sub-con{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
        }
        .mini-plan-row-sub-con{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        .checkmark {
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .plan-feature {
            text-align: start;
            font-weight: 500;
        }
        .ios-16 .plan-feature,  .ios-16 .price-dollar, .ios-16 .trial-details div, .ios-16 .ihc-level-item-content, .ios-16 .elementor-widget-container{
            -webkit-text-stroke-width: 0.8px;
        }
        .ios-16 .count-box p, .ios-16 .count-box span, .ios-16 .countdown-container {
            -webkit-text-stroke-width: 0.5px;
        }
        .plan-checkmarks{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
        .new-con{
            background-color: #2465DB;
            color: white;
            border-radius: 25px;
        }
        .button-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
        .free-three-trial-button {
            width: 100%;
            display: block;
            background: linear-gradient(177deg, #2872F9 50%, #184393 100%);
            color: white;
            padding: 6px;
            text-align: center;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .free-three-trial-button span{
            font-family: "Inter", sans-serif;
            font-optical-sizing: auto;
            font-style: normal;
            font-size: 15px;
        }
        .thunder-icon{
            font-family: "Inter", sans-serif;
            font-size: 16px;
            font-weight: 400;
            line-height: 19.36px;
            text-align: left;
        }

        .pricing-container {
            display: flex;
            justify-content: space-between;
            background-color: #fff;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 3px 1px rgba(40, 114, 249, 0.27);
        }

        .plan-details {
            background-color: #2465DB;
            color: white;
            border-radius: 10px 0 0 10px;
            width: 40%;
            text-align: start;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .trial-details {
            width: 60%;
            text-align: right;
            position: relative;
            padding: 5px 25px 0px 0px;
            z-index: 1;
        }

        .trial-label {
            font-size: 32px;
            font-weight: 600;
        }

        .price-dollar {
            font-weight: bold;
        }

        /* Slanted background design */
        .pricing-container::before {
            content: "";
            position: absolute;
            left: 40%;
            top: 0;
            width: 0;
            height: 100%;
            border-left: 60px solid transparent;
            border-bottom: 100% solid #2465DB;
            z-index: 0;
        }
        .blurry-container{
            position: absolute;
            display: flex;
            flex-direction: row;
            width: 96%;
            z-index: 5;
        }
        .white-blurry-bg {
            position: absolute;
            width: 96%;
            background: rgb(255,255,255);
            background: linear-gradient(180deg, rgba(255,255,255,0.165703781512605) 0%, rgba(255,255,255,1) 25%, rgba(255,255,255,1) 100%);
            bottom: 1px;
            display: flex;
            flex-direction: row;
            align-items: start;
            justify-content: center;
        }

        .modal-header{
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 4px;
            align-items: center;
        }
        .modal-header span{
            font-weight: 700;
        }
        .title-trial,  .trial-label,  .price-dollar{
            color: #2465DB;
        }
        .three-dots-icon{
            position: absolute;
            top: 76px;
            right: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 5;
        }
        .blue-dot{
            width: 5px;
            height: 5px;
            background-color: #2872F9;
            border-radius: 20px;
        }
        .default-dot{
            width: 5px;
            height: 5px;
            background: rgb(255,255,255);
            background: linear-gradient(180deg, rgba(255,255,255,0.165703781512605) 0%, rgba(0,0,0,0) 1%, rgba(40,114,249,0.36458333333333337) 2%);
            opacity: 1;
            border-radius: 20px;
        }
        .AiOutline-button{
            cursor: pointer;
        }
        .title-trial-sub-con{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
        .countdown-container{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: black;
        }
        .count-box{
            display: flex;;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 12px;
        }

        @keyframes bounceScroll {
            0% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px); 
            }
            60% {
                transform: translateY(5px); 
            }
            100% {
                transform: translateY(0);
            }
        }

        @keyframes bounceScrollUp {
            0% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(5px); 
            }
            60% {
                transform: translateY(-5px); 
            }
            100% {
                transform: translateY(0);
            }
        }

        .scroll-bounce-down {
            animation: bounceScroll 0.2s ease-out;
        }

        .scroll-bounce-up {
            animation: bounceScrollUp 0.2s ease-out;
        }

        #premium-access {
            transform: translateY(-45%);
            opacity: 0;
        }

        #subscription-features {
            transform: translateY(45%);
            opacity: 0;
        }

        /* Show states */
        #premium-access.show {
            transform: translateY(0);
            opacity: 1;
        }

        #subscription-features.show {
            transform: translateY(0);
            opacity: 1;
        }

        /* Optional hidden state */
        .hidden {
            display: none;
        }


        .cancel-banner{
            position: absolute;
            top: -20px;
            right: -23px;
            background-color: #e0e0e0;
            border-radius: 100%;
            height: 28px;
            width: 28px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            z-index: 1012;
        }
    </style>
</head>
<body>

<div class="main-popup-modal-container" id="popupModal">
    <div class="modal-content">
      
        <div class="cancel-icon" onclick="closeModal()">
            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Union.svg" width="20" height="20"/>
        </div>
        <div class="modal-header">
            <img class="header-logo" src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Flash_Sale_Icon.svg" width="200" height="200" />
            <div class="title-trial-sub-con">
                <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/right_spark.png" width="30" height="30"/>
                    <span><span class="title-trial">Free Trial!</span> Get All models & Features</span>
                <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/left_spark.png" width="30" height="30"/>
            </div>
        </div>
        <div class="modal-descriptions">
            <div class="sticky-title" id="sticky-title">
                <!-- <div class="modal-title-sub-container-sticky">
                        <span class="title-sticky show" id="premium-access">Get Premium Access</span>
                        <span class="title-sticky hidden" id="subscription-features">Subscription Features</span>
                    <div>
                        <span>PRO</span>
                        <span class="title-pop">PRO MAX</span>
                    </div>
                </div>
                <span class="underline"></span> -->
            </div>
            <div class="sub-desc-box" id="scroll-container">
            <div>
                <div class="modal-title-container">
                    <div class="modal-title-sub-container">
                        <span class="title-pop">Get Premium Access</span>
                        <div>
                            <span>PRO</span>
                            <span class="title-pop">PRO MAX</span>
                        </div>
                    </div>
                    <span class="underline"></span>
                </div>

                <div class="plan-container">
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiRocket.svg" width="20" height="20" />
                            <div class="mini-plan-row-sub-con">
                                <div class="plan-feature">GPT-4o mini & GPT-4o  <span class="new-con">New</span></div>
                            </div>
                        </div>
                        <div class="plan-checkmarks">
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiMessageSquareDetail.svg" width="20" height="20" />
                            <div class="plan-feature">LLama3-70B & Gemini Pro</div>
                        </div>
                        <div class="plan-checkmarks">
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiExtension.svg" width="20" height="20" />
                            <div class="plan-feature">Claude 3.5 Sonnet</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiPalette.svg" width="20" height="20" />
                            <div class="plan-feature">DALL-E 3</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiLayerPlus.svg" width="20" height="20" />
                            <div class="plan-feature">10+ other LLM Models</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/X.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div class="modal-title-container">
                    <div class="modal-title-sub-container">
                        <span class="title-pop">Subscription Features</span>
                        <div>
                            <span>PRO</span>
                            <span class="title-pop">PRO MAX</span>
                        </div>
                    </div>
                    <span class="underline"></span>
                </div>

                <div class="plan-container">
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                        <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiBrain.svg" width="20" height="20" />
                            <div class="plan-feature">Advanced Context Memory</div>
                        </div>
                        <div class="plan-checkmarks">
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/X.svg" width="30" height="30" /></div>
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiMailSend.svg" width="20" height="20" />
                            <div class="plan-feature">Advanced Dialogue Limit</div>
                        </div>
                        <div class="plan-checkmarks">
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/X.svg" width="30" height="30" /></div>
                            <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiInfinite.svg" width="20" height="20" />
                            <div class="plan-feature">Unlimited Chat History Saves</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiNews.svg" width="20" height="20" />
                            <div class="plan-feature">Prompt Templates</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiImageAdd.svg" width="20" height="20" />
                            <div class="plan-feature">Image File Upload</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                    <div class="plan-row">
                        <div class="plan-row-sub-con">
                            <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/BiCategory.svg" width="20" height="20" />
                            <div class="plan-feature">Advanced Tools Full Access</div>
                        </div>
                        <div class="plan-checkmarks">
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Check.svg" width="30" height="30" /></div>
                        <div class="checkmark"><img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Infinite.svg" width="24" height="24" /></div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <div class="three-dots-icon">
                <div class="dot blue-dot"></div>
                <div class="dot default-dot"></div>
            </div>
            <div class="white-blurry-bg">
                <img id="scroll-btns" class="AiOutline-button" src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/AiOutlineDownCircle.svg" width="25" height="25" />
            </div>
            <div class="blurry-container">
                <div class="pricing-container">
                    <div class="plan-details">
                        <span class="plan-name">Pro Max</span>
                    </div>
                    <div class="trial-details">
                        <span class="trial-label">Free Trial</span>
                        <div><span class="price-dollar">$97</span> Monthly billing after trial Period</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-container">
            <button onClick="Action.redirect('/pay/?lid=153');" class="free-three-trial-button">
                <span>Start 3 Days Free Trial <span class="thunder-icon">⚡</span></span>
            </button>
        </div>

    </div>
</div>

<div class="banner-container" id="bannerButton">
    <div class="cancel-banner" id="cancel-banner">
        <img src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Union_White_BG.svg" width="10" height="10" />
    </div>
    <img class="mini-logo-header" src="https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/Flash_Sale_Icon.svg" width="200" height="200" />
    <div class="countdown-container">
        <div class="count-box">
            <p id="days">2</p>
            <span>days</span>
        </div>
        :
        <div class="count-box">
            <p id="hours">19</p>
            <span>hours</span>
        </div>
        :
        <div class="count-box">
            <p id="minutes">30</p>
            <span>minutes</span>
        </div>
        :
        <div class="count-box">
            <p id="seconds">30</p>
            <span>secs</span>
        </div>
    </div>
</div>
<script>
		(function(f, b) {
			if (!b.__SV) {
				var e, g, i, h;
				window.mixpanel = b;
				b._i = [];
				b.init = function(e, f, c) {
					function g(a, d) {
						var b = d.split(".");
						2 == b.length && (a = a[b[0]], d = b[1]);
						a[d] = function() {
							a.push([d].concat(Array.prototype.slice.call(arguments, 0)))
						}
					}
					var a = b;
					"undefined" !== typeof c ? a = b[c] = [] : c = "mixpanel";
					a.people = a.people || [];
					a.toString = function(a) {
						var d = "mixpanel";
						"mixpanel" !== c && (d += "." + c);
						a || (d += " (stub)");
						return d
					};
					a.people.toString = function() {
						return a.toString(1) + ".people (stub)"
					};
					i = "disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");
					for (h = 0; h < i.length; h++) g(a, i[h]);
					var j = "set set_once union unset remove delete".split(" ");
					a.get_group = function() {
						function b(c) {
							d[c] = function() {
								call2_args = arguments;
								call2 = [c].concat(Array.prototype.slice.call(call2_args, 0));
								a.push([e, call2])
							}
						}
						for (var d = {}, e = ["get_group"].concat(Array.prototype.slice.call(arguments, 0)), c = 0; c < j.length; c++) b(j[c]);
						return d
					};
					b._i.push([e, f, c])
				};
				b.__SV = 1.2;
				e = f.createElement("script");
				e.type = "text/javascript";
				e.async = !0;
				e.src = "undefined" !== typeof MIXPANEL_CUSTOM_LIB_URL ?
					MIXPANEL_CUSTOM_LIB_URL : "file:" === f.location.protocol && "//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\/\//) ? "https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js" : "//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";
				g = f.getElementsByTagName("script")[0];
				g.parentNode.insertBefore(e, g)
			}
		})(document, window.mixpanel || []);
</script>
<script>
    function handleButtonClick(url) {
        if (typeof ihcBuyNewLevel === 'function') {
            ihcBuyNewLevel(url);
        } else {
            window.location.href = url; 
        }
    }
    function trackMixpanel() {
        mixpanel.init('510eae1e2d2a79bceee18c49bece1c6a', {
			debug: true
		});

        mixpanel.track("{{PAGE.include_mixpanel.user_plan}}", {
            'keyword': "{{PAGE.include_mixpanel.keyword}}",
            'emailid': "{{PAGE.include_mixpanel.email}}",
            'adid': "{{PAGE.include_mixpanel.adid}}",
            'ppg': "114",
            'pmt': "",
            'howdoiplantouse': "",
            'remakemedloption': "",
            '$email': "{{PAGE.include_mixpanel.email}}"
        });
    }
    document.addEventListener('DOMContentLoaded', function() {
        
        const popupModal = document.getElementById('popupModal');
        const modalContent = popupModal.querySelector('.modal-content');
        const whiteBlurryBg = popupModal.querySelector('.white-blurry-bg')
        const bannerButton = document.getElementById('bannerButton');
        const cancelBanner = document.getElementById('cancel-banner');
        let isModalOpen = false;
        let bannerButtonHiddenByCancel = false; 

        function closeModal() {
            document.body.style.overflow = ''; 

            modalContent.classList.remove('bounce-in');
            modalContent.classList.add('bounce-out'); 
            
            setTimeout(() => {
               
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'translate(1000px, 500px) scale(0)'; 
                popupModal.style.opacity = '0';
                if (window.innerWidth <= 768) {
                    modalContent.style.marginBottom = '0px'; 
                    modalContent.style.marginRight = '0px'; 
                    popupModal.style.display = 'none';
                } else {
                    modalContent.style.marginBottom = '200px';
                    modalContent.style.marginRight = '200px';
                }
               
                setTimeout(() => {
                    if (!bannerButtonHiddenByCancel) {
                        if (window.innerWidth >= 768) {
                            popupModal.style.display = 'none';
                        }
                        bannerButton.style.display = 'flex';
                        bannerButton.style.opacity = '1'; 
                        bannerButton.style.transition = 'opacity 0.4s ease-in forwards'; 
                        bannerButton.style.opacity = '1'; 
                        isModalOpen = false;
                    }
                }, 300); 
            }, 100);
        }


        function openModal() {
            document.body.style.overflow = 'hidden'; 

            modalContent.classList.remove('bounce-out');
            modalContent.classList.add('bounce-in');
            modalContent.style.transform = 'translate(1000px, 500px) scale(0)'; 
            modalContent.style.opacity = '0';
            if (window.innerWidth <= 768) {
                modalContent.style.marginBottom = '0px'; 
                modalContent.style.marginRight = '0px'; 
            } else {
                modalContent.style.transform = 'translate(1000px, 500px) scale(0)'; 
                modalContent.style.marginBottom = '200px';
                modalContent.style.marginRight = '200px';
            }
            popupModal.style.display = 'flex';  
            popupModal.style.opacity = '1';
            bannerButton.style.transition = 'opacity 0.1s ease-out';
            bannerButton.style.opacity = '0'; 
            
            setTimeout(() => {
                bannerButton.style.display = 'none'; 

                requestAnimationFrame(() => {
                    modalContent.style.opacity = '1';
        
                    if (window.innerWidth <= 768) {
                        modalContent.style.marginBottom = '0px'; 
                        modalContent.style.marginRight = '0px'; 
                        modalContent.style.transform = 'translate(0px, 0px) scale(1)';
                    } else {
                        modalContent.style.transform = 'translate(100px, 100px) scale(1)';
                        modalContent.style.marginBottom = '200px'; 
                        modalContent.style.marginRight = '200px'; 
                    }
                    isModalOpen = true;
                });
            }, 300); 
        }

        bannerButton.addEventListener('click', function() {
            openModal();
            trackMixpanel();
        });

        window.closeModal = closeModal;

        setTimeout(function() {
            if (!isModalOpen) {
                openModal();
            }
        }, 3000);

        function hasModalOpenedRecently() {
            const modalOpenedTime = localStorage.getItem('modalOpenedTime');
            
            if (!modalOpenedTime) {
                return false;
            }
            
            const currentTime = new Date().getTime();
            const oneDayInMs = 24 * 60 * 60 * 1000;
            
            return currentTime - modalOpenedTime < oneDayInMs;
        }

        function setModalOpened() {
            const currentTime = new Date().getTime();
            localStorage.setItem('modalOpenedTime', currentTime);
        }

        const targetDiv = document.body;
        if (targetDiv) {
            targetDiv.addEventListener('mouseleave', function(event) {
                const rect = targetDiv.getBoundingClientRect();
                const isTopExit = event.clientY <= rect.top;

                if (isTopExit && !isModalOpen && !hasModalOpenedRecently()) {
                    openModal();
                    setModalOpened();
                }
            });
        } else {
            console.error('Target div not found.');
        }

        popupModal.addEventListener('mouseenter', function() {
            if (isModalOpen) {
                popupModal.style.display = 'flex';
                bannerButton.style.display = 'none';
            }
        });

        cancelBanner.addEventListener('click', function(event) {
            event.stopPropagation();
            closeModal();
            bannerButton.style.display = 'none';
            bannerButtonHiddenByCancel = true; 
        });

        bannerButton.addEventListener('click', function(event) {
            if (event.target !== cancelBanner) {
                openModal();
                bannerButtonHiddenByCancel = false; 
            }
        });
        window.closeModal = closeModal;

        const scrollContainer = document.getElementById('scroll-container');
        const scrollButton = document.getElementById('scroll-btns');

        const scrollStep = 250;

        let isScrollDown = true;

        const dots = document.querySelectorAll('.dot');
        const subDescBox = document.querySelectorAll('.sub-desc-box');
        let currentScrollIndex = 0;

        function toggleScroll() {
            const style = document.createElement('style');
            scrollContainer.style.overflow = 'hidden';
            if (isScrollDown) {
                scrollContainer.style.overflow = 'auto'; 
                //Logic style for swipe on
                const validPaths = [
                    '/subscription-plan-swipe-02/', 
                    '/subscription-plan-toggle-vp-01/',
                    '/subscription-plan-vp-02/',
                    '/subscription-plan-vp-03/',
                    '/plans-and-pricing-c/',
                    '/subscription-plan-ar/',
                    '/aurora-pricing/',
                    '/subscription-plan-toggle/',
                    '/subscription/',
                    '/subscription-e/',
                    '/subscription-plan-basic-pro-max/',
                ];
                if (validPaths.includes(window.location.pathname)) {
                    if (window.innerWidth >= 1441) {
                        style.innerHTML = `
                            .sub-desc-box > *:last-child {
                                margin-top: 8px;
                            }
                        `;
                    } else if (window.innerWidth <= 430) {
                        style.innerHTML = `
                            .white-blurry-bg {
                                height: 110px;
                                padding-top: 20px;
                            }
                        `;
                    } else if (window.innerWidth <= 320) {
                        style.innerHTML = `
                             .white-blurry-bg {
                                height: 61px;
                                padding-top: 32px;
                            }
                        `;
                    }
                    document.head.appendChild(style);
                }else{
                    if (window.innerWidth >= 1368) {
                        whiteBlurryBg.style.height = '170px';
                        whiteBlurryBg.style.paddingTop = '20px';
                    }else if (window.innerWidth >= 1366) {
                        whiteBlurryBg.style.height = '120px';
                        whiteBlurryBg.style.paddingTop = '4px';
                        style.innerHTML += `
                        .sub-desc-box > *:last-child {
                            margin-top: 44px;
                        }
                    `;
                    }else if (window.innerWidth >= 1281) {
                        whiteBlurryBg.style.height = '148px';
                        whiteBlurryBg.style.paddingTop = '0px';
                    }else if (window.innerWidth >=768){
                        whiteBlurryBg.style.height = '106px';
                        whiteBlurryBg.style.paddingTop = '2px';
                            style.innerHTML += `
                            .sub-desc-box > *:last-child {
                                margin-top: 0px;
                            }
                        `;
                 
                    } else if (window.innerWidth <=768){
                        whiteBlurryBg.style.height = '132px';
                        whiteBlurryBg.style.paddingTop = '10px';
                    } else if (window.innerWidth <= 430) {
                        whiteBlurryBg.style.height = '140px';
                        whiteBlurryBg.style.paddingTop = '20px';
                    }
                    document.head.appendChild(style);
                }
                scrollContainer.scrollBy({
                    top: scrollStep,
                    behavior: 'smooth'
                });

                scrollButton.src = 'https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/AiOutlineUpCircle.svg';

                applyBounceEffect(scrollContainer, true);
                // toggleStickyTitle(true);

                changeDotsOnScroll(false);
            } else {
                 //Logic style for swipe on
                 scrollContainer.style.overflow = 'auto'; 
                 const validPaths = [
                    '/subscription-plan-swipe-02/', 
                    '/subscription-plan-toggle-vp-01/',
                    '/subscription-plan-vp-02/',
                    '/subscription-plan-vp-03/',
                    '/plans-and-pricing-c/',
                    '/subscription-plan-ar/',
                    '/aurora-pricing/',
                    '/subscription-plan-toggle/',
                    '/subscription/',
                    '/subscription-e/',
                    '/subscription-plan-basic-pro-max/',
                ];
                //Logic style for swipe on
                if (validPaths.includes(window.location.pathname)) {
                  
                    if (window.innerWidth >= 1441) {
                        style.innerHTML = `
                           .sub-desc-box > *:last-child {
                                margin-top: 8px;
                            }
                        `;
                    } else if (window.innerWidth <= 430) {
                        style.innerHTML = `
                            .white-blurry-bg {
                                height: 110px;
                                padding-top: 1px;
                            }
                        `;
                    } else if (window.innerWidth <= 320) {
                        style.innerHTML = `
                            .white-blurry-bg {
                                height: 61px;
                                padding-top: 25px;
                            }
                        `;
                    }
                    document.head.appendChild(style);
                }
                else{
                    if (window.innerWidth >= 1368) {
                        whiteBlurryBg.style.height = '168px';
                        whiteBlurryBg.style.paddingTop = '16px';
                    }else if (window.innerWidth >= 1366) {
                        whiteBlurryBg.style.height = '124px';
                        whiteBlurryBg.style.paddingTop = '10px';
                        style.innerHTML += `
                        .sub-desc-box > *:last-child {
                            margin-top: 0px;
                        }
                    `;
                    } else if (window.innerWidth >= 1281) {
                        whiteBlurryBg.style.height = '160px';
                        whiteBlurryBg.style.paddingTop = '6px';
                    } else if (window.innerWidth >=768){
                        whiteBlurryBg.style.height = '114px';
                        whiteBlurryBg.style.paddingTop = '8px';
                        style.innerHTML += `
                        .sub-desc-box > *:last-child {
                            margin-top: 0px;
                        }
                    `;
                    } else if (window.innerWidth <=768){
                        whiteBlurryBg.style.height = '128px';
                        whiteBlurryBg.style.paddingTop = '6px';
                    } else if (window.innerWidth <= 430) {
                        whiteBlurryBg.style.height = '148px';
                        whiteBlurryBg.style.paddingTop = '26px';
                    }
                       document.head.appendChild(style);
                }
                scrollContainer.scrollBy({
                    top: -scrollStep,
                    behavior: 'smooth'
                });
                
                scrollButton.src = 'https://assets.ai-pro.org/assets/wp-content/uploads/flash_images/AiOutlineDownCircle.svg';

                applyBounceEffect(scrollContainer, false);
                // toggleStickyTitle(false);

                changeDotsOnScroll(true);
            }
            setTimeout(() => {
                scrollContainer.style.overflow = 'hidden';
            }, 500);
            isScrollDown = !isScrollDown;
        }
        function applyBounceEffect(element, isScrollDown) {
            if (isScrollDown) {
                element.classList.add('scroll-bounce-down');
            } else {
                element.classList.add('scroll-bounce-up'); 
            }

            setTimeout(() => {
                element.classList.remove('scroll-bounce-down');
                element.classList.remove('scroll-bounce-up');
            }, 200); 
        }

        function toggleStickyTitle(isScrollDown) {
        const premiumAccessTitle = document.getElementById('premium-access');
        const subscriptionFeaturesTitle = document.getElementById('subscription-features');

        if (isScrollDown) {
         
            premiumAccessTitle.classList.remove('show');
            setTimeout(() => {
                premiumAccessTitle.classList.add('hidden');
                subscriptionFeaturesTitle.classList.remove('hidden');
                setTimeout(() => {
                    subscriptionFeaturesTitle.classList.add('show'); 
                }, 10); 
            }, 100); 
        } else {
       
            subscriptionFeaturesTitle.classList.remove('show');
            setTimeout(() => {
                subscriptionFeaturesTitle.classList.add('hidden');
                premiumAccessTitle.classList.remove('hidden');
                setTimeout(() => {
                    premiumAccessTitle.classList.add('show'); 
                }, 10); 
            }, 100); 
        }
    }



        function changeDotsOnScroll(scrollUp) {

            dots[currentScrollIndex].classList.remove('blue-dot');
            dots[currentScrollIndex].classList.add('default-dot');

            if (scrollUp) {
         
                currentScrollIndex = (currentScrollIndex - 1 + dots.length) % dots.length;
            } else {
            
                currentScrollIndex = (currentScrollIndex + 1) % dots.length;
            }

            dots[currentScrollIndex].classList.remove('default-dot');
            dots[currentScrollIndex].classList.add('blue-dot');
        }

        // scrollButton.addEventListener('click', toggleScroll);
        whiteBlurryBg.addEventListener('click', toggleScroll);

        const countdown = {
            days: 2,
            hours: 19,
            minutes: 30,
            seconds: 30
        };

        const daysEl = document.getElementById('days');
        const hoursEl = document.getElementById('hours');
        const minutesEl = document.getElementById('minutes');
        const secondsEl = document.getElementById('seconds');

        function updateCountdown() {
            if (countdown.seconds > 0) {
                countdown.seconds--;
            } else {
                countdown.seconds = 59;
                if (countdown.minutes > 0) {
                    countdown.minutes--;
                } else {
                    countdown.minutes = 59;
                    if (countdown.hours > 0) {
                        countdown.hours--;
                    } else {
                        countdown.hours = 23;
                        if (countdown.days > 0) {
                            countdown.days--;
                        }
                    }
                }
            }

            daysEl.textContent = countdown.days;
            hoursEl.textContent = countdown.hours < 10 ? `0${countdown.hours}` : countdown.hours;
            minutesEl.textContent = countdown.minutes < 10 ? `0${countdown.minutes}` : countdown.minutes;
            secondsEl.textContent = countdown.seconds < 10 ? `0${countdown.seconds}` : countdown.seconds;
        }

        setInterval(updateCountdown, 1000);
        
        // if (navigator.userAgent.toLowerCase().indexOf("android") > -1) {
        //     document.body.classList.add("android-device");
        // }
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && navigator.userAgent.includes('OS 16')) {
            document.body.classList.add('ios-16');
        }

        //Logic style for swipe on
        const validPaths = [
            '/subscription-plan-swipe-02/', 
            '/subscription-plan-toggle-vp-01/',
            '/subscription-plan-vp-02/',
            '/subscription-plan-vp-03/',
            '/plans-and-pricing-c/',
            '/subscription-plan-ar/',
            '/aurora-pricing/',
            '/subscription-plan-toggle/',
            '/subscription/',
            '/subscription-e/',
            '/subscription-plan-basic-pro-max/',
        ];
    
        if (validPaths.includes(window.location.pathname)) {
            const style = document.createElement('style');
            style.innerHTML = `
                @media only screen and (min-width: 2048px) {
                    .AiOutline-button {
                        display: none;
                    }
                }
                .header-logo {
                  width: 200px;
                  height: 100%;
                }
                .free-three-trial-button{
                    padding-top: 12px;
                    padding-bottom: 12px;
                }
                .sub-desc-box{
                    padding-bottom: 160px;
                }
            @media only screen and (min-width: 1681px) {
                .white-blurry-bg {
                    height: 140px;
                    padding-top: 8px;
                }
                .plan-row {
                    margin-bottom: -25px;
                }
                .modal-descriptions {
                    max-height: 59.6vh;
                }
            }
            @media only screen and (max-width: 1680px) {
                 .modal-descriptions {
                    min-height: 56vh !important;
                    max-height: 55.6vh;
                }
                .white-blurry-bg {
                    height: 140px;
                    padding-top: 8px;
                }
                .modal-content{
                    max-width: 66vh !important;
                }
            }
            @media only screen and (max-width: 1366px) {
                .header-logo {
                    width: 180px;
                }
                .modal-content {
                    max-width: 75vh !important;
                    max-height: 92%;
                }
                .white-blurry-bg {
                    height: 100px;
                    padding-top: 1px;
                }
                .plan-row{
                    margin-bottom: -25px;
                }
                .sub-desc-box{
                    gap: 20px;
                }
                .modal-descriptions {
                    max-height: 55.6vh !important;
                    min-height: 54vh !important;
                }
                .plan-details span {
                    font-size: 24px;
                }
                .blurry-container {
                    bottom: 18px;
                }
                .white-blurry-bg{
                    height: 116px;
                    padding-top: 1px;
                }
                .plan-details span, .trial-details .trial-label {
                    font-size: 22px !important;
                }
                .plan-details {
                    padding: 20px 20px 20px 20px;
                }
                .trial-details div, .price-dollar {
                    font-size: 12px;
                }
            
            }
            @media only screen and (max-width: 1280px) {
                .modal-content {
                    max-width: 75vh !important;
                }
                .white-blurry-bg {
                    height: 100px;
                    padding-top: 1px;
                }
                .plan-row{
                    margin-bottom: -32px;
                }
                .sub-desc-box{
                    gap: 20px;
                }
                .modal-descriptions {
                    max-height: 54.6vh !important;
                    min-height: 54vh !important;
                }
                .plan-details span {
                    font-size: 24px;
                }
                .plan-details {
                    padding: 16px 20px 20px 20px !important;
                }
            }
            @media only screen and (min-width: 768px) {
                .modal-content {
                    width: 70%;
                    max-width: 58vh;
                }
            }
            @media only screen and (min-width: 431px) {
                
                .mini-logo-header {
                    width: 100%;
                    height: 100%;
                }
                .count-box span, .count-box p {
                    font-size: 14px;
                    margin-bottom: -8px;
                }
            }
            @media only screen and (max-width: 430px) {
                .modal-content {
                    max-height: 95%;
                }
                .modal-descriptions {
                    max-height: 57.6vh !important;
                    min-height: 54vh !important;
                }
                .banner-container {
                    max-width: 190px;
                    max-height: 130px;
                }
                .count-box span, .count-box p {
                    margin-bottom: -5px;
                }
                .header-logo {
                    width: 200px;
                    height: 110px;
                }
                .modal-content {
                   height: 105%;
                    max-width: 100%;
                }
                .free-three-trial-button{
                    padding-top: 10px;
                    padding-bottom: 10px;
                }
                .white-blurry-bg {
                    height: 108px;
                }
                .sub-desc-box {
                    gap: 2px;
                }
                .plan-row{
                    margin-bottom: -20px;
                }
            }
            @media only screen and (max-height: 824px) {
                .modal-descriptions {
                    min-height: 59vh; 
                }
            }
            @media only screen and (max-height: 845px) and (min-height: 800px) {
                .modal-descriptions {
                    max-height: 50vh !important;
                    min-height: 67% !important;
                }
            }

            @media only screen and (max-width: 320px) {
                .modal-descriptions {
                    min-height: 100%;
                }
                .modal-content {
                    height: 92%;
                }
                .white-blurry-bg {
                    height: 68px;
                }
                .mini-logo-header{
                    height: 85px;
                }
                .sub-desc-box{
                    padding-bottom: 60px !important;
                }
                .sub-desc-box >* {
                    margin-bottom: 14px !important;
                }
                .sub-desc-box >*:last-child {
                    margin-bottm: 0;
                }
                .white-blurry-bg {
                    height: 62px;
                }
                    
            }
            
            `;
            document.head.appendChild(style);
        }

    });
</script>

</body>
</html>
{% endif %}