
	<!---Mixpanel Start----->
		<?php
		$actual_link = "$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
		$arr_link = explode('/',$actual_link);
		$slug1 = isset($arr_link[0]) ? $arr_link[0] : '';
		$slug2 = isset($arr_link[1]) ? $arr_link[1] : '';

		$mixpanel_name = '';
		if ($slug2==''){
			$mixpanel_name = 'home';
		}else if (strpos($slug2, '?') !== false){
			$mixpanel_name = 'home';
		}else{
			$mixpanel_name = $slug2;
		}

		if (strpos($slug2, 'ai-chat') !== false){
			$mixpanel_name = 'ai-chat';
		}

		$pos = strpos($actual_link, 'checkout-page');
		if ($pos !== false) {
			$mixpanel_name = 'checkout-page';
		}

		$ppg = get_flag('ppg');
		$pmt = get_flag('pmt');

		$ppg_with_toggle = [101, 109, 110];

		if (in_array($ppg, $ppg_with_toggle)) {
			if ($pmt!='pay3d'){
				setcookie('pmt', 'pay2', time() + (86400 * 30), "/", ".ai-pro.org");
				setcookie('pmt', 'pay2', time() + (86400 * 30), "/");
			}
		}
		?>
		<script>
			(function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");
			for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?
			MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\/\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);

			mixpanel.init('510eae1e2d2a79bceee18c49bece1c6a', {debug: true});

			<?php
			$user_email_analytics = get_flag('user_email');
			if ($user_email_analytics!=''){
			?>
			mixpanel.identify("<?php echo get_flag('user_email')?>", {email: "<?php echo get_flag('user_email')?>"});
			mixpanel.people.set_once({ "$email": '<?php echo get_flag('user_email')?>' });
			<?php }?>

			mixpanel.track("<?php echo $mixpanel_name;?>", {
			'keyword': "<?php echo get_flag('keyword')?>",
			'emailid': "<?php echo get_flag('emailid')?>",
			'adid': "<?php echo get_flag('adid')?>",
			'ppg': "<?php echo get_flag('ppg')?>",
			'pmt': "<?php echo get_flag('pmt')?>",
			'flow': "<?php echo get_flag('flow')?>",
			'$email': "<?php echo get_flag('user_email')?>",
			'remakemedloption': "<?php echo get_flag('remakemedloption')?>",
			'theme': "<?php echo get_flag('kt8typtb')?>",
			'locales': "<?php echo get_flag('locales')?>"
			});

			<?php
			$user_plan = get_flag('user_plan');
			if ($user_email_analytics!='' && $user_plan!=''){
			?>
			mixpanel.people.set_once({ "Plan": '<?php echo $user_plan;?>' });
			<?php }?>

			console.log("s1","<?php echo $slug1?>","s2","<?php echo $slug2?>","n:","<?php echo $mixpanel_name?>","url:","<?php echo $actual_link?>","version:","1");
			console.log("k:","<?php echo get_flag('keyword')?>","e:","<?php echo get_flag('emailid')?>","a:","<?php echo get_flag('adid')?>");
		</script>
	<!---Mixpanel End----->

	<!-- Start of HubSpot Embed Code -->
		<script type="text/javascript" id="hs-script-loader" async defer src="//js-na1.hs-scripts.com/44168812.js"></script>
	<!-- End of HubSpot Embed Code -->

	<!-- Facebook Pixel Code -->
	<script>
		!function(f,b,e,v,n,t,s)
		{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
		n.callMethod.apply(n,arguments):n.queue.push(arguments)};
		if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
		n.queue=[];t=b.createElement(e);t.async=!0;
		t.src=v;s=b.getElementsByTagName(e)[0];
		s.parentNode.insertBefore(t,s)}(window, document,'script',
		'https://connect.facebook.net/en_US/fbevents.js');
		fbq('init', '975784236794755');
		fbq('track', 'PageView');
		</script>
		<noscript><img height="1" width="1" style="display:none"
		src="https://www.facebook.com/tr?id=975784236794755&ev=PageView&noscript=1"
		/></noscript>
	<!-- Facebook Pixel Code -->
		
		<?php
		$actual_link = "$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
		$pos = strpos($actual_link, 'thank-you');
		if ($pos !== false) {
		?>
	<!-- Event snippet for Purchase ai-pro.org conversion page -->
		<script>
		fbq('track', 'Purchase', {currency: "<?php echo $currency?>", value: '<?php echo $price?>'});
		</script>
		<?php } ?>

		<?php
		$actual_link = "$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
		$pos = strpos($actual_link, 'checkout-page');
		if ($pos !== false) {
		?>
	<!-- Event snippet for Purchase ai-pro.org conversion page -->
		<script>
		fbq('track', 'PaymentPage', {currency: "<?php echo $plan_currency?>", value: '<?php echo $price_to_charge/100?>'});
		</script>
		<?php } ?>

	<!-- Google Tag Code Start-->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-70TZ628CHH"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());
		gtag('config', 'G-70TZ628CHH');
		gtag('config', 'AW-532672904');
		</script>
	<!-- Google Tag Code End-->

	<!-- Quora Pixel Code (JS Helper) -->
	<script>
	!function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
	qp('init', '96952f49aa584b478f10b3bce056e08c');
	qp('track', 'ViewContent');
	</script>
	<noscript><img height="1" width="1" style="display:none" src="https://q.quora.com/_/ad/96952f49aa584b478f10b3bce056e08c/pixel?tag=ViewContent&noscript=1"/></noscript>
	<!-- End of Quora Pixel Code -->