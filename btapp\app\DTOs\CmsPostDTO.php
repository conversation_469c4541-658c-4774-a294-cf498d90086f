<?php

namespace App\DTOs;

use App\DTOs\CmsDTO;

/**
 * CmsPostDTO is a Data Transfer Object for handling CMS post data.
 * It encapsulates the properties of a CMS post and provides methods
 * to convert it to an array or create it from Strapi result data.
 */
class CmsPostDTO extends CmsDTO
{
    public ?string $pid = '';
    public ?string $title = '';
    public ?string $meta_title = '';
    public ?string $description = '';
    public ?string $breadcrumb = '';
    public ?string $url = '';
    public ?string $date_create = '';
    public ?string $date_create_gmt = '';
    public ?string $date_update = '';
    public ?string $date_update_gmt = '';
    public ?string $content = '';
    public ?array  $prev_post = [];
    public ?array  $next_post = [];
    public ?string $og_image_type = '';
    public ?string $og_image_width = '';
    public ?string $og_image_height = '';
    public ?string $og_image_url = '';
    public ?string $slug1 = '';
    public ?string $canonical_url = '';

    // used in tag and category pages
    public ?string $category_slug = '';
    public ?string $category_name = '';
    public ?string $category_url = '';
    public ?string $category_id = '';
    public ?string $image = '';
    public ?string $excerpt = '';
    public ?string $robots = '';

    public ?string $updatedAt = '';

    public ?string $urlPath = ''; // the full URL path of the post, built from its slug and parent categories

    // view transformation properties
    public ?string $post_name = '';
    public ?string $post_title = '';
    public ?\stdClass $og_image_meta = null;

    // post_type=page template
    public ?string $template = '';

    /**
     * CmsPostDTO constructor.
     * Initializes properties from an array of data.
     *
     * @param array $data Associative array of properties to initialize.
     */
    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }

    public function toView(): self
    {
        $this->post_name = $this->url;
        $this->post_title = $this->title;
        $this->og_image_meta = (object) [
            'url' => $this->og_image_url ?? '',
            'alt' => $this->title ?? '',
        ];
        $this->updatedAt = !empty($this->date_update_gmt) ? date('c', strtotime($this->date_update_gmt)) : '';
        return $this;
    }

    private static function buildCategoryUrl(array $category): string
    {
        $slugs = [];

        while (!empty($category)) {
            if (!empty($category['slug'])) {
                array_unshift($slugs, trim($category['slug'], '/'));
            }

            // Go up to parent if it exists
            if (!empty($category['parent']) && is_array($category['parent'])) {
                $category = $category['parent'];
            } else {
                break;
            }
        }

        return '/' . implode('/', $slugs);
    }

    public static function buildUrlPath(array $item): string
    {
        $slugs = [];

        // Walk up through the parent tree
        while (!empty($item)) {
            if (!empty($item['slug'])) {
                array_unshift($slugs, $item['slug']); // Add to the beginning
            }
            $item = $item['parent'] ?? null;
        }

        return '/' . implode('/', $slugs);
    }

    /**
     * Create a CmsPostDTO from a Strapi result.
     *
     * @param array $result The result from Strapi.
     * @param string $uri The URI of the post.
     * @return CmsPostDTO
     */    
    public static function fromStrapiResult(array $result, string $uri = ''): self
    {   //print_r($result['data']['title']); die();
        $dto = new self();

        // guard, if data key does not exist or is not an array, return empty DTO
        if (empty($result['data']) || !is_array($result['data'])) {
            return $dto;
        }

        // store title from wordpress
        $result['data']['title_wordpress'] = $result['data']['title'] ?? '';

        $dto->pid = $result['data']['id'] ?? '';
        $dto->title = $result['data']['title'] ?? '';
        $dto->meta_title = !empty($result['data']['seo']['metaTitle'])
                                ? $result['data']['seo']['metaTitle']
                                : $dto->title . ' %%sep%% ' . '%%sitename%%';

        $dto->description = !empty($result['data']['seo']['metaDescription'])
                                ? $result['data']['seo']['metaDescription']
                                : ($result['data']['description'] ?? ($result['data']['excerpt'] ?? ''));

        $dto->breadcrumb = $dto->title;
        $dto->url = $result['data']['slug'] ?? '';
        $dto->content = $result['data']['content'] ?? '';
        if (!empty($dto->content)) {
            // $dto->content = $dto->addSandboxToIframes($dto->content); // not needed, iframes are no longer allowed in content
            // remove iframes from content and replace with a placeholder
            $dto->content = $dto->removeIframes($dto->content);
        }
        $dto->prev_post = isset($result['data']['prev_post']) && is_array($result['data']['prev_post'])
            ? $result['data']['prev_post']
            : [];

        $dto->next_post = isset($result['data']['next_post']) && is_array($result['data']['next_post'])
            ? $result['data']['next_post']
            : [];

        // extract facebook Open Graph image data if available
        foreach ($result['data']['seo']['metaSocial'] ?? [] as $social) {
            if (($social['socialNetwork'] ?? '') === 'Facebook') {
                $image = $social['image']['formats']['large'] 
                            ?? $social['image']['formats']['medium'] 
                            ?? $social['image']['formats']['small'] ?? null;
                if ($image) {
                    $dto->og_image_url = $image['url']
                                                ?? $social['image']['url']
                                                ?? '';
                    $dto->og_image_width = $image['width'] ?? null;
                    $dto->og_image_height = $image['height'] ?? null;
                    $dto->og_image_type = $image['mime'] ?? null;
                }
                break;
            }
        }

        // extract featured image if available
        if (!empty($result['data']['featured_image'])) {
            $featuredImage = $result['data']['featured_image'];
            $dto->image = $featuredImage['formats']['large']['url']
                            ?? $featuredImage['formats']['medium']['url']
                            ?? $featuredImage['formats']['small']['url']
                            ?? $featuredImage['url'] ?? '';
        } else {
            $dto->image = '';
        }

        if (!empty($result['data']['categories']) && is_array($result['data']['categories'])) {
            $category = $result['data']['categories'][0]; // pick the first category
        
            $dto->category_id = $category['id'] ?? '';
            $dto->category_name = $category['name'] ?? '';
            $dto->category_slug = $category['slug'] ?? '';
            $dto->category_url = self::buildCategoryUrl($category);
        }

        $dto->urlPath = self::buildUrlPath($result['data']);

        // build canonical url with
        if (isset($result['data']['seo']['canonicalURL']) && !empty($result['data']['seo']['canonicalURL'])) {
            $dto->canonical_url = $result['data']['seo']['canonicalURL'];
        } else {
            // fallback to base_url + category_url + urlPath
            $dto->canonical_url = rtrim(base_url(), '/') . $dto->category_url . '/' . $dto->urlPath;
        }

        // Normalize slashes
        $dto->canonical_url = preg_replace('#(?<!:)/+#', '/', $dto->canonical_url);

        // set robots meta tag, default to 'index, follow' if not provided
        //$dto->robots = $result['data']['seo']['metaRobots'] ?? 'index, follow'; //, max-image-preview:large, max-video-preview:-1, nosnippet
        $robotsValue = $result['data']['seo']['metaRobots'] ?? 'index, follow';
        // check if max-image-preview:large is missing and add it if needed
        if (strpos($robotsValue, 'max-image-preview:large') === false) {
            $robotsValue .= ', max-image-preview:large';
        }
        // check if max-video-preview:-1 is missing and add it if needed
        if (strpos($robotsValue, 'max-video-preview:-1') === false) {
            $robotsValue .= ', max-video-preview:-1';
        }
        $dto->robots = $robotsValue;

        $dto->site_name = $dto->getDomainForTitle($dto->canonical_url);
        
        $dto->excerpt = (isset($result['data']['excerpt']) && trim($result['data']['excerpt']) !== '')
                            ? $result['data']['excerpt']
                            : $dto->convertToExcerpt($dto->content);

        $dto->template = $result['data']['template'] ?? '';

        // get the 2nd to the last part of the URI as slug1
        // e.g. /learn-ai/articles/ai-articles, slug1 will be 'articles'
        $parts = explode('/', trim($uri, '/'));
        $partCount = count($parts);
        $dto->slug1 = $partCount >= 2 ? $parts[$partCount - 2] : ($parts[0] ?? '');

        // Parse and format for: published_time
        if (!empty($result['data']['post_date_gmt']) && $date = date_create($result['data']['post_date_gmt'])) {
            $dto->date_create = date_format($date, "F d Y");
            $dto->date_create_gmt = date_format($date, "c");
        } else {
            // there is always createdAt, so we can use it as a fallback
            $createdAt = $result['data']['createdAt'] ?? '';
            if (!empty($createdAt) && $date = date_create($createdAt)) {
                $dto->date_create = date_format($date, "F d Y");
                $dto->date_create_gmt = date_format($date, "c");
            } else {
                $dto->date_create = '';
                $dto->date_create_gmt = '';
            }
        }

        // Parse and format for: modified_time
        if (!empty($result['data']['post_modified_gmt']) && $date = date_create($result['data']['post_modified_gmt'])) {
            $dto->date_update = date_format($date, "F d Y");
            $dto->date_update_gmt = date_format($date, "c");
        } else {
            // there is always updatedAt, so we can use it as a fallback
            $updatedAt = $result['data']['updatedAt'] ?? '';
            if (!empty($updatedAt) && $date = date_create($updatedAt)) {
                $dto->date_update = date_format($date, "F d Y");
                $dto->date_update_gmt = date_format($date, "c");
            } else {
                $dto->date_update = '';
                $dto->date_update_gmt = '';
            }
        }

        //for title with %%
        if (strpos($dto->meta_title, '%%sep%%') !== false) {
            $dto->meta_title = str_replace('%%sep%%', '|', $dto->meta_title);
        }
        if (strpos($dto->meta_title, '%%sitename%%') !== false) {
            $dto->meta_title = str_replace('%%sitename%%', $dto->site_name, $dto->meta_title);
        }
        if (strpos($dto->meta_title, '%%title%%') !== false) {
            $dto->meta_title = str_replace('%%title%%', $result['data']['title_wordpress'], $dto->meta_title);
        }
        
        return $dto;
    }
}