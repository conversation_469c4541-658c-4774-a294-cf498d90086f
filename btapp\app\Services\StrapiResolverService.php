<?php

namespace App\Services;

use App\Libraries\CacheService;

/**
 * StrapiResolverService
 *
 * This service handles authentication and data retrieval from a Strapi CMS instance.
 * It caches the JWT token and API responses using Redis or file cache.
 */
class StrapiResolverService
{
    protected $strapiBaseUrl; // Base URL for the Strapi API
    private $strapiUsername; // Username for Strapi authentication
    private $strapiPassword; // Password for Strapi authentication
    protected $jwtToken = null; // JWT token for authenticated requests
    protected $cache; // Cache service instance for caching API responses
    protected $useCache = true; // Flag to determine if caching is enabled
    public $cacheKeyPrefix = 'cms_'; // Prefix for cache keys
    protected $current_page = 1; // Current page slug, used for loading content in the current page context
    protected $total_pages = 0; // Total number of pages, used for pagination

    /**
     * Constructor
     *
     * Initializes the Strapi base URL, username, password, and Redis connection parameters.
     */
    public function __construct()
    {
        $this->strapiBaseUrl = env('STRAPI_BASE_URL');
        $this->strapiUsername = env('STRAPI_USERNAME');
        $this->strapiPassword = env('STRAPI_PASSWORD');

        $this->cache = new CacheService(useAemo: true); // Use Aemo Redis client
        $this->useCache(); // Initialize cache usage based on GET parameters
    }

    public function useCache() : bool|string
    {
        if (isset($_GET['cms_cache'])) {
            switch ($_GET['cms_cache']) {
                case 'refresh':
                    $this->useCache = 'refresh';
                    break;
                case 'false':
                    $this->useCache = false;
                    break;
            }
        }

        // Check if the cache service is initialized and available
        return $this->useCache;
    }

    /**
     * Authenticate with Strapi and retrieve a JWT token.
     *
     * @param string $email
     * @param string $password
     * @return string|null JWT token or null on failure
     */
    private function strapiLogin($email, $password) {
        $url = $this->strapiBaseUrl . 'auth/local';
        $payload = [
            'identifier' => $email,
            'password' => $password
        ];

        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            $resp = curl_exec($ch);
 
            if (curl_errno($ch)) {
                throw new \Exception('Curl error: ' . curl_error($ch));
            }
 
            curl_close($ch);
            $data = json_decode($resp, true);

            if (!isset($data['jwt'])) {
                throw new \Exception('Strapi login error: ' . ($data['error']['message'] ?? 'Unknown error'));
            }
 
            return $data['jwt'];
        } catch (\Exception $e) {
            // Log the error
            error_log('Strapi login error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Ensure the service is authenticated and has a valid JWT token.
     *
     * This method checks if a cached JWT token exists in Redis or file cache.
     * If not, it attempts to log in to Strapi to retrieve a new token.
     */
    protected function ensureAuthenticated()
    {
        // create a cache key based on the username, and current date
        $key = $this->cacheKeyPrefix . md5($this->strapiUsername . '_' . date('Y-m-d'));

        $cached = $this->cache->get($key);
        if ($cached) {
            $this->jwtToken = $cached;
            return $cached;
        }

        $this->jwtToken = $this->strapiLogin($this->strapiUsername, $this->strapiPassword);
            
        // If login fails, throw exception to prevent further API calls
        if ($this->jwtToken === null) {
            throw new \Exception('Failed to authenticate with Strapi API');
        }

        $this->cache->set($key, $this->jwtToken);
        return $this->jwtToken;
    }

    public function getCached(string $key)
    {
        return $this->cache->get($key);
    }

    public function setCached(string $key, string $data)
    {
        return $this->cache->set($key, $data);
    }

    /**
     * Retrieve data from Strapi API with optional caching.
     *
     * @param string $endpoint The Strapi API endpoint to call.
     * @param array $params Optional parameters for the API request.
     * @param string $lastSlug Optional slug to use as a cache key field.
     * @param bool $cache Whether to cache the result (default: true).
     * @return array The API response data.
     */
    public function getStrapiData(string $endpoint, array $params = [], $lastSlug = "")
    {
        $this->ensureAuthenticated();

        $url = $this->strapiBaseUrl . $endpoint;
        $queryString = !empty($params) ? '?' . http_build_query($params) : '';

        // Create a cache key based on the endpoint and parameters
        $key = $this->cacheKeyPrefix . md5(
            $endpoint .
            '-' . $queryString .
            '_' . $lastSlug .
            $this->strapiUsername .
            '_' . date('Y-m-d')
        );

        $cached = $this->cache->get($key);

        // fetch data directly from strapi
        if ($this->useCache === false || $this->useCache === 'refresh' || !$cached) {
            // If caching is explicitly disabled, skip cache retrieval
            $client = \Config\Services::curlrequest();

            $headers = [
                'Authorization' => 'Bearer ' . $this->jwtToken,
                'Accept' => 'application/json',
            ];

            try {
                $response = $client->get($url . $queryString, [
                    'headers' => $headers,
                ]);
                $responseData = json_decode($response->getBody(), true);
            } catch (\Exception $e) {
                // Log the error and return an empty array
                log_message('error', 'Strapi API request failed: ' . $e->getMessage());
                return [];
            }

            if ($this->useCache === true || $this->useCache === 'refresh') {
                // If caching is enabled, store the response in cache
                $this->cache->set($key, $responseData);
            }
        } else {
            // If cached data exists, use it
            $responseData = $cached;
        }

        return $responseData ;
    }

    /*
        Try resolving in order:
        1. Check if $slug is a post (exact match by slug)
        2. If not found, check if $slug is a category
        3. If not found, check if $slug is a tag
        4. If still not found:
           a. If URI has one segment => check if it's a top-level page
           b. If URI has two segments => check if it's a page with a parent (parent/page_slug)
           c. Ignore deeper nesting for now
    */
    public function resolveSlug(string $uriPath): array
    {
        $segments = array_filter(explode('/', trim($uriPath, '/')));
        $segments = array_values($segments);

        $defaultLocale = 'en';
        $localesData = $this->getStrapiData('i18n/locales', [], '');
        $validLocales = array_column($localesData, 'code');

        $locale = $defaultLocale;

        // Locale detection
        if (!empty($segments) && in_array($segments[0], $validLocales)) {
            $locale = array_shift($segments);
        }

        if (empty($segments)) {
            return ['type' => 'home', 'locale' => $locale];
        }

        $localeParam = ['locale' => $locale];
        $resolved = [];
        $i = count($segments) - 1;

        while ($i >= 0) {
            $slug = $segments[$i];

            // check if it is a page slug
            if (isset($segments[$i - 1]) && $segments[$i - 1] === 'page') {
                $this->current_page = $slug; // store current page slug
                $i -= 2;
                continue; // skip this segment, as it's not a valid slug
            }

            // Check if it's a tag segment: /tag/{slug}
            if (isset($segments[$i - 1]) && $segments[$i - 1] === 'tag') {
                $tagSlug = $segments[$i];
                $tag = $this->getStrapiData('tags', array_merge([
                    'filters[slug][$eq]' => $tagSlug
                ], $localeParam));

                if (empty($tag['data'])) {
                    return ['type' => 'not_found', 'locale' => $locale];
                }

                $resolved[] = ['type' => 'tag', 'data' => $tag['data'][0]];
                $i -= 2; // skip both "tag" and the tag slug

                continue;
            }

            // Try resolving as post
            $post = $this->getStrapiData('posts', array_merge([
                'fields' => ['id','title','content','excerpt',
                                'post_date','post_date_gmt','post_modified','post_modified_gmt','createdAt','updatedAt',
                                'frontend_status', 'slug'],
                'filters[slug][$eq]' => $slug,
                'filters[frontend_status][$eq]' => 'published',
                'populate' => [
                    'categories' => [
                        'fields' => ['name', 'slug'],
                        'populate' => [
                            'parent' => [
                                'fields' => ['name', 'slug']
                            ],
                        ],
                    ],
                    'featured_image' => true,
                    'author' => true,
                    'seo' => [
                        'fields' => ['metaTitle', 'metaDescription', 'metaRobots', 'canonicalURL'],
                        'populate' => [
                            'metaImage' => true,
                            'metaSocial' => [
                                'populate' => '*'
                            ]
                        ]
                    ]
                ],
            ], $localeParam), $slug);

            if (!empty($post['data'])) {
                $post['data'][0]['next_post'] = $this->getNextOrPrevPost('posts', $post['data'][0]['id'], $slug, $post['data'][0]['createdAt'], 'next');
                $post['data'][0]['prev_post'] = $this->getNextOrPrevPost('posts', $post['data'][0]['id'], $slug, $post['data'][0]['createdAt'], 'prev');
                $resolved[] = ['type' => 'post', 'data' => $post['data'][0]];
                $i--;
                continue;
            }

            // Try resolving as page
            $page = $this->getStrapiData('pages', array_merge([
                'fields' => ['id','title','content', 'template',
                                'post_date','post_modified','createdAt','updatedAt',
                                'frontend_status', 'slug'],
                'filters[slug][$eq]' => $slug,
                'filters[frontend_status][$eq]' => 'published',
                'populate' => [
                    'featured_image' => true,
                    'seo' => [
                        'fields' => ['metaTitle', 'metaDescription', 'metaRobots', 'canonicalURL'],
                        'populate' => [
                            'metaImage' => true,
                            'metaSocial' => [
                                'populate' => '*'
                            ]
                        ]
                    ]
                ],
            ], $localeParam), $slug);

            if (!empty($page['data'])) {
                $resolved[] = ['type' => 'page', 'data' => $page['data'][0]];
                $i--;
                continue;
            }

            // Try resolving as category
            $cat = $this->getStrapiData('categories', array_merge([
                'filters[slug][$eq]' => $slug
            ], $localeParam), $slug);

            if (!empty($cat['data'])) {
                $resolved[] = ['type' => 'category', 'data' => $cat['data'][0]];
                $i--;
                continue;
            }

            // If none matched, fail immediately
            return ['type' => 'not_found', 'locale' => $locale];
        }

        // Return last resolved non-tag item as main data
        foreach ($resolved as $r) {
            return ['type' => $r['type'], 'data' => $r['data'], 'locale' => $locale];
        }

        // Fallback to not found if no valid data was resolved
        return ['type' => 'not_found', 'locale' => $locale];
    }

    private function getNextOrPrevPost($endpoint, $postId, $slug, $createdAt, $direction = 'next'): ?array
    {
        switch ($direction) {
            case 'prev':
                $direction = 'lt';
                $sort = 'desc';
                $key = '_prev';
                break;
            default: // default case is 'next'
                $direction = 'gt';
                $sort = 'asc';
                $key = '_next';
                break;
        }

        $result = $this->getStrapiData($endpoint, [
            'fields[0]' => 'id',
            'fields[1]' => 'documentId',
            'fields[2]' => 'title',
            'fields[3]' => 'slug',
            'filters[id][$ne]' => $postId,
            'filters[createdAt][$'.$direction.']' => $createdAt,
            'populate[seo][populate][metaSocial][populate]' => '*',
            'sort[0]' => 'createdAt:' . $sort,
            'pagination[limit]' => 1,
        ], $slug . $key);

        return !empty($result['data']) ? $result['data'][0] : null;
    }

    /**
     * Get a redirect response for a given URI.
     *
     * This method checks if a redirect exists for the provided URI in Strapi.
     * If found, it returns a redirect response with the target URL and status code.
     *
     * @param string $uri The URI to check for redirects.
     * @return \CodeIgniter\HTTP\RedirectResponse|null Redirect response or null if no redirect found.
     */
    public function checkRedirect(string $uri): ?\CodeIgniter\HTTP\RedirectResponse
    {
        // 1. Exact match on `url`
        $params = [
            'filters[$or][0][url]' => '/' . trim($uri),
            'filters[$or][1][match_url]' => '/' . trim($uri),
        ];

        $result = $this->getStrapiData('redirects', $params);

        if (!empty($result['data'])) {
            return $this->buildRedirectResponse($result['data'][0]);
        }

        return null;
    }

    private function buildRedirectResponse(array $redirect): ?\CodeIgniter\HTTP\RedirectResponse
    {
        $target = $redirect['action_data']['url'] ?? null;
        $code = $redirect['action_code'] ?? 301;

        if ($target) {
            // return redirect()->to($target, $code); // this does not work, better to use headers directly

            http_response_code($code);
            header('Location: ' . $target);
            exit(); // Make sure to stop execution after setting headers
        }

        return null;
    }

    public function buildRecursiveParentPopulate(int $depth, array $fields): array
    {
        if ($depth <= 0) {
            return [];
        }
    
        return [
            'fields' => $fields,
            'populate' => [
                'parent' => $this->buildRecursiveParentPopulate($depth - 1, $fields),
            ],
        ];
    }

    public function setTotalPages(int $total): void
    {
        $this->total_pages = $total;
    }

    public function getTotalPages(): int
    {
        return $this->total_pages;
    }

    public function setCurrentPage(string $slug): void
    {
        $this->current_page = $slug;
    }

    public function getCurrentPage(): string
    {
        return $this->current_page;
    }
}