import { AiOutlineEdit } from 'react-icons/ai';
import { BsChat, BsBoxArrowUp } from 'react-icons/bs';
import { BiCustomize } from 'react-icons/bi';
import { MdOutlineLightMode } from 'react-icons/md';
import { SlEarphones } from 'react-icons/sl';
import { VscFeedback } from 'react-icons/vsc';

import chatbotProImg from './images/access_tools/chatbot-pro.png';
import chatPdfImg from './images/access_tools/chat-pdf.png';
import grammarAiImg from './images/access_tools/grammar-ai.png';
import teacherAiImg from './images/access_tools/teacher-ai.png';
import recipeMakerAiImg from './images/access_tools/recipe-maker.png';
import tripsAiImg from './images/access_tools/trips-ai.png';
import translateNowAiImg from './images/access_tools/translate-now.png';
import searchAiImg from './images/access_tools/search-ai.png';
import multiChatAiImg from './images/access_tools/multi-chat.png';
import dreamPhotoAiImg from './images/access_tools/dream-photo.png';
import interiorAiImg from './images/access_tools/interior-ai.png';
import removeBackgroundAiImg from './images/access_tools/remove-background.png';
import avatarMakerAiImg from './images/access_tools/avatar-maker.png';
import storyBookAiImg from './images/access_tools/storybook.png';
import restorePhotoAiImg from './images/access_tools/restore-photo.png';

import { getCurrentDomain, getCurrentProtocol } from './functions';

export default function Pricing() {
    const handleGetEnterprise = () => {
        const curDom = getCurrentDomain();
        const pricingDom = getCurrentProtocol() + '//' + (curDom.includes('staging') && !curDom.includes('localhost') ? 'staging.' : '') + 'start.ai-pro.org';
        
        window.location.href = `${pricingDom}/pricing/?ppg=71&kt8typtb=arcana`;
    };

    return (
        <section className="py-12 md:py-16 lg:py-20">
          <div className="ep_container px-4 md:px-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <h2 className="font-bold max-w-640:text-[32px] md:text-[36px]">
                        Get more done at <span className="text-[#1559ED]">$160/month</span>
                    </h2>
                    <p className="text-gray-500 text-[16px]">Join thousands of businesses that trust our enterprise solutions</p>
                    <div className="flex max-w-640:flex-col gap-[20px] md:gap-[30px] items-center justify-center">
                        <div className="flex items-center rounded-lg p-[14px] text-sm border">
                            <div className="flex flex-row border-r px-[8px]">
                                <div className="flex">
                                    <span className="text-[#EAB308] tracking-[2.5px] font-bold">★★★★★</span>
                                    <span className="ml-2 font-semibold text-[#1559ED]">4.9</span>
                                </div>
                            </div>
                            <div>
                                <span className="text-gray-500 px-[8px]">1,000+ reviews</span>
                            </div>
                        </div>
                        <div className="flex items-center bg-[#F3FFFC] rounded-lg p-[14px] border">
                            <svg
                            className="w-5 h-5 text-[#10B981]"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            >
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                            <polyline points="22 4 12 14.01 9 11.01" />
                            </svg>
                            <span className="ml-2 text-sm text-[#10B981]">Trusted by thousands of users worldwide.</span>
                        </div>
                    </div>
                </div>

                {/* Mobile pricing card - only visible on mobile */}
                <div className="md:hidden flex flex-col mx-auto max-w-md py-8 gap-[24px]">
                    <div className="flex flex-col justify-between gap-[20px] border rounded-[30px] shadow-lg p-6 bg-[#F3F8FF]">
                        <div className="flex flex-col gap-[23px] pb-[20px] border-b">
                            <div className="flex flex-col gap-[8px]">
                                <div className="inline-block rounded-full bg-blue-100 text-sm text-[#1559ED] px-[20px] py-[5px] w-fit font-bold">
                                        Best Offer
                                </div>
                                <h3 className="text-2xl font-extrabold">ENTERPRISE PLAN</h3>
                                <p className="text-gray-500">
                                    Empower your business with the right subscription plan. Transform the way your team works and
                                    collaborates.
                                </p>
                            </div>

                            <div className="flex gap-[16px] items-center">
                                <span className="text-[40px] font-extrabold">$160</span>
                                <div className="flex flex-col">
                                    <div className="text-sm">per month</div>
                                    <div className="text-sm leading-[1.1]">minimum 10 users</div>
                                </div>
                            </div>
                        </div>
                        
                        <button className="gradient-button w-full px-6 max-w-400:px-5 max-w-380:px-4 py-3 rounded-[6px] text-white font-semibold text-[20px] max-w-380:text-[18px]" onClick={handleGetEnterprise}>
                            Get All-In Enterprise Access
                        </button>
                    </div>
                    <div className="flex flex-col justify-between gap-[20px] border rounded-[30px] shadow-lg p-6 bg-[#F3F8FF]">
                        <div className="flex flex-col gap-[10px]">
                            <div className="flex flex-col gap-[2px] pt-[10px] pb-[20px] border-b">
                                <h4 className="font-bold">FEATURES</h4>
                                <p className="text-sm text-gray-500">Everything in Pro, plus:</p>
                            </div>

                            <div className="py-[15px] ">
                                <div className="flex flex-col border-b gap-[6px] pb-[30px]">
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <BsChat className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Access to GPT 3.5 and GPT-4o</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <BsBoxArrowUp className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">3.7M Token Cap/Month (370K Tokens/per user)</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <VscFeedback className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Response models can provide scenario-based answers</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <AiOutlineEdit className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Save your chat history</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <MdOutlineLightMode className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Light or Dark Mode</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <BsBoxArrowUp className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Export conversations to image, text, csv or json files</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <SlEarphones className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Live support</span>
                                    </div>
                                    <div className="flex items-start gap-2 hidden">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5 self-center">
                                            <BiCustomize className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Customizable chatbot themes</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-col gap-[10px]">
                            <div className="flex flex-col gap-[2px]">
                                <h4 className="font-bold text-[18px]">Access to tools like:</h4>
                                <p className="text-sm text-gray-500">
                                    AI chat, writing assistants, image generators, coding tools — and new features added regularly!
                                </p>
                            </div>

                            <div className="flex flex-col gap-[10px]">
                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={chatbotProImg} alt="ChatBot Pro" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Chatbot Pro</h5>
                                        <p className="text-[14px] text-gray-500">Ask your questions.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={chatPdfImg} alt="ChatPdf App" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">ChatPDF</h5>
                                        <p className="text-[14px] text-gray-500">Ask your pdf documents questions.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={grammarAiImg} alt="Grammar AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Grammar AI</h5>
                                        <p className="text-[14px] text-gray-500">Confidently write flawless grammar.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={teacherAiImg} alt="Teacher AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Teacher AI</h5>
                                        <p className="text-[14px] text-gray-500">Easily learn and research concepts.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={recipeMakerAiImg} alt="Recipe Maker AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Recipe Maker</h5>
                                        <p className="text-[14px] text-gray-500">AI recipes from your ingredients.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={tripsAiImg} alt="Trips AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Trips AI</h5>
                                        <p className="text-[14px] text-gray-500">Create your perfect itinerary.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={translateNowAiImg} alt="Translate Now AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Translate Now</h5>
                                        <p className="text-[14px] text-gray-500">Translate texts anytime, anywhere.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={searchAiImg} alt="Search AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Search AI</h5>
                                        <p className="text-[14px] text-gray-500">AI-powered research tool.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={multiChatAiImg} alt="Multi Chat AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Multi-Chat</h5>
                                        <p className="text-[14px] text-gray-500">Four chatbots, one conversation.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={dreamPhotoAiImg} alt="Idea to art convertion" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">DreamPhoto</h5>
                                        <p className="text-[14px] text-gray-500">Transform ideas into art.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={interiorAiImg} alt="Interior AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Interior AI</h5>
                                        <p className="text-[14px] text-gray-500">Create new room designs with AI.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={removeBackgroundAiImg} alt="Remove Background AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Remove Background</h5>
                                        <p className="text-[14px] text-gray-500">Remove backgrounds effortlessly.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={avatarMakerAiImg} alt="Avatar Maker AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Avatar Maker</h5>
                                        <p className="text-[14px] text-gray-500">Create your own digital avatar.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={storyBookAiImg} alt="Storybook AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Storybook AI</h5>
                                        <p className="text-[14px] text-gray-500">Create your story with AI art panels.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={restorePhotoAiImg} alt="Bring back old photos App" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Restore Photo</h5>
                                        <p className="text-[14px] text-gray-500">Bring old photos back to life.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Desktop pricing card - only visible on desktop */}
                <div className="hidden md:flex md:flex-col mx-auto max-w-[1000px] py-8 gap-[24px]">
                    <div className="flex flex-col justify-between border rounded-[30px] px-[50px] py-[20px] h-[270px] shadow-lg" style={{ backgroundColor: 'rgba(243, 248, 255, 0.5)' }}>
                        <div className="flex justify-between h-[155px] border-b">
                            <div className="w-[635px]">
                                <div className="inline-block rounded-full bg-blue-100 text-sm text-[#1559ED] mb-4 px-[20px] py-[5px] font-bold">
                                    Best Offer
                                </div>
                                <h3 className="text-2xl font-extrabold mb-[7px]">ENTERPRISE PLAN</h3>
                                <p className="text-gray-500">
                                    Empower your business with the right subscription plan. Transform the way your team works and
                                    collaborates.
                                </p>
                            </div>
                            <div className="flex flex-col items-center justify-center gap-[5px]">
                                <div className="flex flex-col text-center">
                                    <h3 className="text-[40px] font-extrabold">$160</h3>
                                    <p className="text-sm self-end">per month</p>
                                </div>
                                <p className="text-sm">minimum 10 users</p>
                            </div>
                        </div>
                        <button className="gradient-button px-6 py-3 rounded-[6px] text-white font-semibold w-fit text-[20px]" onClick={handleGetEnterprise}>
                            Get All-In Enterprise Access
                        </button>
                    </div>
                    <div className="flex flex-col justify-between border rounded-[30px] px-[50px] py-[20px] shadow-lg" style={{ backgroundColor: 'rgba(243, 248, 255, 0.5)' }}>
                        <div className="flex flex-col gap-[10px]">
                            <div className="flex flex-col gap-[2px] pt-[10px] pb-[20px] border-b">
                                <h4 className="font-bold">FEATURES</h4>
                                <p className="text-sm text-gray-500">Everything in Pro, plus:</p>
                            </div>

                            <div className="flex py-[15px] border-b gap-[50px]">
                                <div className="flex flex-col gap-[12px] pb-[20px]">
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <BsChat className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Access to GPT 3.5 and GPT-4o</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <BsBoxArrowUp className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">3.7M Token Cap/Month (370K Tokens/per user)</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <VscFeedback className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Response models can provide scenario-based answers</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <AiOutlineEdit className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Save your chat history</span>
                                    </div>
                                </div>
                                
                                <div className="flex flex-col gap-[12px] pb-[20px]">
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <MdOutlineLightMode className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Light or Dark Mode</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <BsBoxArrowUp className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Export conversations to image, text, csv or json files</span>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <SlEarphones className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Live support</span>
                                    </div>
                                    <div className="flex items-start gap-2 hidden">
                                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                                            <BiCustomize className="w-3 h-3 text-blue-600" />
                                        </div>
                                        <span className="text-[16px] font-medium">Customizable chatbot themes</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex flex-col gap-[10px]">
                            <div className="flex flex-col py-[20px] gap-[2px]">
                                <h4 className="font-bold text-[18px]">Access to tools like:</h4>
                                <p className="text-sm text-gray-500">
                                    AI chat, writing assistants, image generators, coding tools — and new features added regularly!
                                </p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px]">
                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={chatbotProImg} alt="ChatBot Pro" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Chatbot Pro</h5>
                                        <p className="text-[14px] text-gray-500">Ask your questions.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={chatPdfImg} alt="ChatPdf App" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">ChatPDF</h5>
                                        <p className="text-[14px] text-gray-500">Ask your pdf documents questions.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={grammarAiImg} alt="Grammar AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Grammar AI</h5>
                                        <p className="text-[14px] text-gray-500">Confidently write flawless grammar.</p>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px]">
                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={teacherAiImg} alt="Teacher AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Teacher AI</h5>
                                        <p className="text-[14px] text-gray-500">Easily learn and research concepts.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={recipeMakerAiImg} alt="Recipe Maker AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Recipe Maker</h5>
                                        <p className="text-[14px] text-gray-500">AI recipes from your ingredients.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={tripsAiImg} alt="Trips AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Trips AI</h5>
                                        <p className="text-[14px] text-gray-500">Create your perfect itinerary.</p>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px]">
                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={translateNowAiImg} alt="Translate Now AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Translate Now</h5>
                                        <p className="text-[14px] text-gray-500">Translate texts anytime, anywhere.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={searchAiImg} alt="Search AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Search AI</h5>
                                        <p className="text-[14px] text-gray-500">AI-powered research tool.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={multiChatAiImg} alt="Multi Chat AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Multi-Chat</h5>
                                        <p className="text-[14px] text-gray-500">Four chatbots, one conversation.</p>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px]">
                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={dreamPhotoAiImg} alt="Idea to art convertion" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">DreamPhoto</h5>
                                        <p className="text-[14px] text-gray-500">Transform ideas into art.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={interiorAiImg} alt="Interior AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Interior AI</h5>
                                        <p className="text-[14px] text-gray-500">Create new room designs with AI.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={removeBackgroundAiImg} alt="Remove Background AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Remove Background</h5>
                                        <p className="text-[14px] text-gray-500">Remove backgrounds effortlessly.</p>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px]">
                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={avatarMakerAiImg} alt="Avatar Maker AI" />    
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Avatar Maker</h5>
                                        <p className="text-[14px] text-gray-500">Create your own digital avatar.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={storyBookAiImg} alt="Storybook AI" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Storybook AI</h5>
                                        <p className="text-[14px] text-gray-500">Create your story with AI art panels.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-[20px] p-[14px] min-w-[327px]">
                                    <div className="flex-shrink-0 w-[42px] h-[42px] rounded-md flex items-center justify-center">
                                        <img src={restorePhotoAiImg} alt="Bring back old photos App" />
                                    </div>
                                    <div className="flex flex-col gap-[2px]">
                                        <h5 className="font-semibold text-[14px]">Restore Photo</h5>
                                        <p className="text-[14px] text-gray-500">Bring old photos back to life.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>    
                </div>
            </div>
        </section>
    );
}