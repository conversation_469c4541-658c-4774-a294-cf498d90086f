<?php

/**
 * BTGeoIP
 *
 */

function getGeoIP($asIP)
{
	try {
	  $url = 'http://api.db-ip.com/v2/841e8570b8a6356b526d9060cfe20d7caeea6e74/' . $asIP . '/';

	  $httpOptions = [
	    "header" => [
	      "User-Agent: dbip-api-client",
	    ],
	  ];

	  if (!$jsonData = file_get_contents($url, false, stream_context_create(["http" => $httpOptions]))) {
	    return 'error';
	  } else if (!$data = json_decode($jsonData)) {
	    return 'error';
	  } else if (isset($data->error)) {
	    return 'error';
	  }
	  return $jsonData;
	} catch (Exception $e) {
    return 'error';
	}	
}

function getGeoCountryCode(){
  $ip = getClientIP();
  $location = getGeoIP($ip);
  $location = json_decode($location);

  $countryCode = '';

  foreach($location as $key => $value) {
      if ($countryCode==''){
        $countryCode =  isset($value->countryCode) ? $value->countryCode : '';
      }
  }

  if (trim($countryCode)==''){
    $countryCode = isset($location->countryCode) ? $location->countryCode : '';
  }

  if (trim(strtolower($countryCode))==='zz'){
    $countryCode = '';
  }

  return strtolower($countryCode);
}

function getClientIP()
{
  $ipaddress = '';
  if (isset($_SERVER['HTTP_CLIENT_IP']))
    $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
  else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
    $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
  else if (isset($_SERVER['HTTP_X_FORWARDED']))
    $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
  else if (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP']))
    $ipaddress = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
  else if (isset($_SERVER['HTTP_FORWARDED_FOR']))
    $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
  else if (isset($_SERVER['HTTP_FORWARDED']))
    $ipaddress = $_SERVER['HTTP_FORWARDED'];
  else if (isset($_SERVER['REMOTE_ADDR']))
    $ipaddress = $_SERVER['REMOTE_ADDR'];
  else
    $ipaddress = '*********';

  if ($ipaddress != '') {
      $arr_ip = explode(',', $ipaddress);
      $ipaddress = isset($arr_ip[0]) ? $arr_ip[0] : '';
  }

  return $ipaddress;
}

