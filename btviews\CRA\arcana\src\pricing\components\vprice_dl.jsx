import React, { Suspense, useState } from 'react';
import { motion } from "framer-motion";
import { GetCookie } from '../../core/utils/cookies';
import '../css/style.css';
import { useTranslation } from 'react-i18next';

const TpReviews = React.lazy(() => import('../../features/tpreviews'));

function VPriceDL(props) {
  const { t } = useTranslation();
  const data = props.data ? props.data : null;
  const setPricing = props.setPricing ? props.setPricing : ()=>{};
  var ppg = GetCookie("ppg");
  if (ppg==='') {
    ppg =  process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : '14';
  }
  // var pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "1559ED";
  const ppgArrayWithToggle = ['40','48','52','97','101','109','110','114'];
  const showToggle = (ppgArrayWithToggle.includes(ppg) && props.showToggle) ? props.showToggle : false;
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : "";
  var billedAnnualDisplay = false;
  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? "yearly" : "monthly");
  const [ desc_align ] = useState(GetCookie("desc_align"));
  const kt8typtb = GetCookie('kt8typtb') || '';
  
  const intervalChange = function() {
    if(planInterval === "monthly") {
      setPlanInterval("yearly");
    } else {
      setPlanInterval("monthly");
    }
  };

  const checkPlanInterval = function(plan) {
    if(!showToggle) return true;
    if(plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }


  return (
      <div className="v-pricingdl pricing bg-gray-100 md:min-h-[90vh] poppins">
        <div className="pricing_columns container mx-auto py-10">
          <div className="flex flex-col items-center pt-[60px]">
            <h1 className="text-4xl lg:text-4xl font-bold text-center mb-4 min-h-[40px]">
              Get Full Access
            </h1>
            { showToggle ? (
              <div className={`${kt8typtb === 'arcana_wp' ? 'px-4 pb-4' : 'p-4'}`}>
                <div className="text-1xl lg:text-1xl font-bold text-center mb-4">
                  <div>Choose between our monthly and yearly options below</div>
                </div>
                <div className="flex items-center justify-center w-full mb-8">
                  <label for="toggleB" className="flex items-center cursor-pointer">
                    <div className={`${planInterval === 'monthly' ? "text-blue-700 font-bold" : "text-black"} mr-3 uppercase`}>
                      Monthly
                    </div>
                    <div className="relative">
                      <input type="checkbox" id="toggleB" className="sr-only toggle" onChange={intervalChange} defaultChecked={billedAnnualDisplay}/>
                      <div className="block bg-[#9CA3AF] w-12 h-6 rounded-full"></div>
                      <div className="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                    </div>
                    <div className={`${planInterval === 'yearly' ? "text-blue-700 font-bold" : "text-black"} ml-3 uppercase`}>
                      Yearly
                    </div>
                  </label>
                </div>
              </div>
            ) : ""}

            { showToggle ? (
              <div className="pricing-toggle flex flex-col md:flex-row justify-center w-full">
                {data?.map((plan, index) => (
                  checkPlanInterval(plan) ? (
                    <div key={index} className={`price_col text-center sm:px-4 w-full mx-auto md:mx-[0] mb-8 relative`}>
                      {planInterval === 'monthly' && plan.label.toLowerCase() === 'pro' && kt8typtb === 'arcana_wp' && (
                        <div className="popu absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight z-10">
                          <span>{t('arcana.pricing.vprice.mostPopularText')}</span>
                        </div>
                      )}
                      <div className="bg-white rounded-lg shadow-lg overflow-hidden xl:w-[330px]">
                        <div className="px-2 sm:px-6 py-10 price-content">
                          <h3 className="text-xl font-bold mb-4">{plan.label}</h3>
                          <div className="mb-6" dangerouslySetInnerHTML={{__html: plan.display_txt4}}></div>
                          <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'}`}>
                            <ul className="text-sm text-gray-600">
                              { plan.display_txt2 ? <li className="mb-2" dangerouslySetInnerHTML={{__html: plan.display_txt2}}></li> : null }
                            </ul>
                          </div>
                          <motion.button
                            className="text-white font-bold py-3 px-6 rounded-lg bg-[#2872fa] hover:bg-[##4b8df9]"
                            onClick={() => setPricing(plan.plan_id)}
                          >
                            Subscribe
                          </motion.button>
                        </div>
                      </div>
                      { planInterval === 'yearly' && showToggle ? <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight">
                        <div>Up to <span className='font-bold underline-offset-1'>20% OFF</span> on an annual subscription</div>
                      </div> : null }
                    </div>
                  ) : ""
                ))}
              </div>
            ) : (
              <div className="flex flex-col md:flex-row justify-center w-full">
                {data?.map((plan, index) => (
                  <div key={index} className={`price_col text-center sm:px-4 mb-8 relative w-full md:w-auto mx-auto md:mx-[0]`}>
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden xl:w-[330px]">
                      <div className="px-2 sm:px-6 py-10 price-content">
                        <h3 className="text-xl font-bold mb-4">{plan.label}</h3>
                        <div className="mb-6" dangerouslySetInnerHTML={{__html: plan.display_txt4}}></div>
                        <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'}`}>
                          <ul className="text-sm text-gray-600">
                            { plan.display_txt2 ? <li className="mb-2" dangerouslySetInnerHTML={{__html: plan.display_txt2}}></li> : null }
                          </ul>
                        </div>
                        <motion.button
                          className="text-white font-bold py-3 px-6 rounded-lg btn-ent bg-[#2872fa] hover:bg-[##4b8df9]"
                          onClick={() => setPricing(plan.plan_id)}
                        >
                        {plan.label.toLowerCase()==="enterprise" ? "Build My Plan" : "Subscribe" }
                        </motion.button>
                      </div>
                    </div>
                    { index === 1 ? <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight">
                      <span>{t('arcana.pricing.vprice.mostPopularText')}</span>
                    </div> : null }
                  </div>
                ))}
              </div>
            )}

            <p className="text-xs max-w-md text-center leading-relaxed mb-10 lg:mb-12">
              *The pricing is exclusive of taxes and additional local tax may be collected.
            </p>
            
            { tpreviews === 'on' ?
              <> 
              <Suspense fallback={null}>
                <TpReviews/>
              </Suspense>
              </>
            : null }
          </div>
        </div>
      </div>
  )
}

export default VPriceDL;