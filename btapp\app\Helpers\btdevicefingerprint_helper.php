<?php


/**
 * btdevincefingerprint | Device Fingerprint
 *
 */


if (!function_exists('getDeviceFingerprint')) {
    function getDeviceFingerprint()
    {
        $output = [
            'build-version' => "v1.1.0 | Device Fingerprint",
            'is-fraudster' => "WIP: Development",
            'user-agent' => getUserAgent()['agent-string'],
            'browser' => getUserAgent()['browser'],
            'browser-version' => getUserAgent()['version'],
            'mobile' => getUserAgent()['mobile'],
            'platform' => getUserAgent()['platform'],
            'referrer' => getUserAgent()['referrer'],
            'ip-address' => getIpAddress(),
            'client-timezone' => getClientTimezone(),
            'fingerprint1-Canvas' => getFingerprintCanvas(),
            'fingerprint1-Canvas-Desc' => "ctx font=16px 'Arial' | txt=" . base_url(),
            'fingerprint2-Combo1' => getFingerprintCombo1(),
            'fingerprint2-Combo1-Desc' => "browser, browser-version, platform, client-timezone",
            // 'canvas-fingerprint2' => getCanvasFingerprint2(),
            // 'canvas-fingerprint3' => getCanvasFingerprint3(),
            // 'canvas-fingerprint4' => getCanvasFingerprint4(),
            // 'source2' => getFromNodeServer(),
            'http-headers-host' => getHttpHeaders()['Host'],
            'http-headers-cookie' => getHttpHeaders()['Cookie'],
            'http-headers-origin' => getHttpHeaders()['Origin'],
            'http-headers-accept_language' => getHttpHeaders()['Accept-Language'],
            'http-headers-accept_encoding' => getHttpHeaders()['Accept-Encoding'],
            'http-headers-referer' => getHttpHeaders()['Referer'],
        ];

        // fraudsterAlert($output);
        return $output; 
    }
}


// if (!function_exists('getFromNodeServer')) {
//     function getFromNodeServer()
//     {
//         try {
//             $url = 'http://localhost:5001/';
//             $data = file_get_contents($url);
//             return $data;
//         } catch(\Exception $e) {
//             return ['message' => 'load failed', 'e' => $e];
//         }
//     }
// }


/**
 * Not used
 */
if (!function_exists('getCanvasFingerprint4')) {
    function getCanvasFingerprint4()
    {
        $canvas = imagecreatetruecolor(200, 20);
        $txt = base_url();
        $text_color = imagecolorallocate($canvas, 0, 102, 0);
        $background_color = imagecolorallocate($canvas, 255, 102, 0);
        imagefilledrectangle($canvas, 0, 0, 199, 19, $background_color);
        imagettftext($canvas, 16, 0, 2, 15, $text_color, FCPATH.'fonts/Arial.ttf', $txt);
        imagestring($canvas, 4, 4, 17, $txt, $text_color);
        ob_start();
        imagepng($canvas);
        $strng = ob_get_clean();
        
        $hash = 0;
        if (strlen($strng) == 0) return 'nothing!';
        for ($i = 0; $i < strlen($strng); $i++) {
          $char = ord($strng[$i]);
          $hash = (($hash << 5) - $hash) + $char;
          $hash = $hash & $hash;
        }

        return $hash;
    }
}
/**
 * Not used
 */
if (!function_exists('getCanvasFingerprint2')) {
    function getCanvasFingerprint2() {
        // User-Agent string
        $userAgent = $_SERVER['HTTP_USER_AGENT'];
        
        // Additional attributes (you can add more if needed)
        $additionalAttributes = [
            'platform' => php_uname('s'),
            'language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'],
            'timezone' => date_default_timezone_get()
        ];
    
        // Serialize the attributes
        $serializedAttributes = serialize($additionalAttributes);
        
        // Generate a hash of the serialized attributes
        $hash = hash('sha256', $userAgent . $serializedAttributes);
        
        return $hash;
    }
}
/**
 * Not used
 */
if (!function_exists('getCanvasFingerprint3')) {
    function getCanvasFingerprint3() {
        $canvas = imagecreatetruecolor(16, 16);
    
        // Set a unique color to the pixel
        $color = imagecolorallocate($canvas, 255, 0, 0);
        imagesetpixel($canvas, 0, 0, $color);
        
        // Get the data URL of the canvas
        ob_start();
        imagepng($canvas);
        $data = ob_get_clean();
        
        // Generate a hash of the canvas data
        $hash = hash('sha256', $data);
        
        // Free up memory
        imagedestroy($canvas);
        
        return $hash;
    }
}

if (!function_exists('getFingerprintCanvas')) {
    function getFingerprintCanvas()
    {
        return btflag_cookie('__ctx__', "");
    }
}

if (!function_exists('getFingerprintCombo1')) {
    // browser, version, platform, timezone
    function getFingerprintCombo1()
    {
        $request = service('request');
        $hash = $request->getUserAgent()->getBrowser() . "|" . $request->getUserAgent()->getVersion() . "|" . $request->getUserAgent()->getPlatform() . btflag_cookie('user_tz', "");

        $hash = btutilEncrypt($hash, "B)mhY/t-C-{K7e*j*p");
        return $hash;
    }
}


if (!function_exists('getUserAgent')) {
    function getUserAgent()
    {
        $request = service('request');
        $userAgent = [
            'agent-string' => $request->getUserAgent()->getAgentString(),
            'browser' => $request->getUserAgent()->getBrowser(),
            'version' => $request->getUserAgent()->getVersion(),
            'mobile' => $request->getUserAgent()->getMobile(),
            'platform' => $request->getUserAgent()->getPlatform(),
            'referrer' => $request->getUserAgent()->getReferrer(),
        ];
        return $userAgent;
    }
}


if (!function_exists('getIpAddress')) {
    function getIpAddress()
    {
        return btflag_cookie('user_ip', "");
    }
}



if (!function_exists('getClientTimezone')) {
    function getClientTimezone()
    {
        return btflag_cookie('user_tz', "");
    }
}


if (!function_exists('getHttpHeaders')) {
    function getHttpHeaders()
    {
        $request = service('request');

        return [
            'Host' => $request->getHeaderLine('Host'), 
            'Cookie' => $request->getHeaderLine('Cookie'), 
            'Origin' => $request->getHeaderLine('Origin'), 
            'Accept-Language' => $request->getHeaderLine('Accept-Language'),
            'Accept-Encoding'=> $request->getHeaderLine('Accept-Encoding'),
            'Referer' => $request->getHeaderLine('Referer')
        ];
    }
}


if (!function_exists('fraudsterAlert')) {
    function fraudsterAlert()
    {
        $emailContent = [
            'subject' => "Fraudster Alert: Test Only",
            'message' => "Test only;"
        ];
        btemailSendToAlert($emailContent);
        return 1;
    }
}


// function getUserByPid($user_pid)
// {
//     $user = btdbFindBy('UserModel', 'user_pid', $user_pid);
//     if ($user['success'] && $user['res']) return ['success' => 1, 'data' => $user['res'][0]];
//     return ['success' => 0, 'data' => []];
// }