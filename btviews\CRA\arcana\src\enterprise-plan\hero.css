/* Overall form container */
#hubspotForm {
    /* width: 530px; */
    width: auto;
}

.hs-form {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem 2rem 2rem;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    /* max-width: 700px; */
    max-width: 620px;
    margin: auto;
  }
  
@media (max-width: 640px) {
    .hs-form {
        width: auto;
    }        
}  

@media (min-width: 385px) and (max-width: 400px) {
  #hubspotFormMobile .hs-form {
    padding-top: 30px;
  }
}

  /* Headings */
  .hs-form h1,
  .hs-form h2,
  .hs-form h3 {
    color: #1a1a1a;
    margin-bottom: 1.5rem;
  }
  
  /* Fieldset spacing */
  .hs-form fieldset {
    border: none;
    padding: 0;
    margin-bottom: 1.5rem;
  }
  
  /* Individual field containers */
  .hs-form-field {
    margin-bottom: 1rem;
  }
  
  /* Labels */
  .hs-form-field label {
    display: block;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .hs-form-field label span,
  .hs-form-field label span strong {
    font-weight: bold !important;  
  }
  
  /* Inputs, selects, textareas */
  .hs-form .hs-input {
    width: 100% !important;
    /* padding: 0.75rem; */
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 1rem;
    box-sizing: border-box;
    transition: border-color 0.3s;
  }
  
  .hs-form .hs-input:focus {
    outline: none;
    border-color: #3b82f6; /* Tailwind's blue-500 */
  }
  
  /* Dropdown placeholder text */
  .hs-form .hs-input.is-placeholder {
    color: #6b7280; /* Tailwind's gray-500 */
  }
  
  /* Required asterisk */
  .hs-form .hs-form-required {
    /* color: #ef4444; Tailwind's red-500 */
    margin-left: 4px;
  }
  
  /* Submit button */
  .hs-form .hs-button.primary {
    background: linear-gradient(90deg, #1a5eee 0%, #1a5eee 40%, #5ea3fa 100%);
    transition: all 0.3s ease;
    /* background-color: #3b82f6; Tailwind's blue-500; */
    /* transition: background-color 0.3s; */
    color: white;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 20px;
    width: 100%;
  }
  
  .hs-form .hs-button.primary:hover {
    background-color: #2563eb; /* Tailwind's blue-600 */
  }
  
  /* Legal consent and description */
  .hs-form .legal-consent-container,
  .hs-form .hs-richtext {
    font-size: 0.9rem;
    color: #4b5563; /* Tailwind's gray-700 */
    margin-top: 1rem;
  }

  .hs-form .hs-richtext em {
    font-size: 12px;
  }
  
  .hs-form .hs-richtext a {
    color: #0000ee; /* default blue link color */
    text-decoration: none; /* default underline */
    cursor: pointer;
  }

  .hs-form .hs-error-msg {
    font-size: 11px;
    color: red;
  }

  .hs-form .hs_error_rollup {
    margin-top: 20px;
  }

  .hs-form .hs_error_rollup ul li label {
    font-size: 14px;
    color: red;
  }

  fieldset {
    max-width: 100% !important;
  }

  .form-columns-0 {
    font-weight: bold;
  }