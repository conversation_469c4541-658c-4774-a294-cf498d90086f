import axios from 'axios';
import { useQuery } from "react-query";
import Cookies from 'universal-cookie';
import { SetCookie, RemoveCookie } from './cookies';
import CryptoJS from "crypto-js";
const cookies = new Cookies();

async function checkAuth() {
  try{
    const response = await axios.post(`${process.env.REACT_APP_API_URL}/check-auth`, { tk: cookies.get("access") }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });
    const output = response.data;
    if(output.success) {
      SetCookie('user_plan', output.user.plan, { domain: '.ai-pro.org', path: '/' });
      SetCookie('auth_version', output.user.authversion, { domain: '.ai-pro.org', path: '/' });
      const ciphertext = CryptoJS.AES.encrypt(output.user.plan, '.ai-pro.org').toString();
      SetCookie('h98n8deyn79w49', ciphertext);
      SetCookie('h98n8deyn79w49', ciphertext, { domain: '.ai-pro.org', path: '/' });
      SetCookie('user_ppg', output.user.user_ppg, { domain: '.ai-pro.org', path: '/' });
      SetCookie('user_email', output.user.email, { domain: '.ai-pro.org', path: '/' });
      SetCookie('chatpro_KJLF4XgSL8wjlGm', output.user.chatpro_hash, { domain: '.ai-pro.org', path: '/' });
      SetCookie('aiwp_logged_in', output.user.chatpro_hash, { domain: '.ai-pro.org', path: '/' });
      SetCookie('aiproStart', output.user.aiproStart, { domain: '.ai-pro.org', path: '/' });
      if(output?.tk) SetCookie('access', output.tk, { domain: '.ai-pro.org', path: '/' });

      return output.user;
    } else {
      return false;
    }
  }catch (error) {
    if (error.response && error.response.status >= 500) {
      RemoveCookie("access");
      RemoveCookie("ci_session");
      RemoveCookie("access", { domain: '.ai-pro.org', path: '/' });
      RemoveCookie("ci_session", { domain: '.ai-pro.org', path: '/' });

      axios.get(`${process.env.REACT_APP_API_URL}/logout`).then(function(){
        window.location.href = '/login';
      });

    } else {
      console.error("Auth check failed:", error.message);
    }
    return false;
  }
}

export function Auth(redirect = false) {
    const { data } = useQuery("auth", checkAuth);
    if(data === undefined) return data;
    if(data === false && redirect) window.location.href=redirect;
    return data;
}

export function isSubscriber(auth) {
  return auth.status !== 'pending' && auth.expired === 'no';
}


export function isPro(auth) {
  // return (auth.plan === 'pro' || auth.plan === 'enterprise') && auth.status !== 'pending' && auth.expired === 'no';
  return (auth.plan.includes('pro') || auth.plan === 'enterprise') && auth.status !== 'pending' && auth.expired === 'no';

}

export function isBasic(auth) {
  return auth.plan === 'basic' && auth.status !== 'pending' && auth.expired === 'no';
}

export function isAuthV1Paid(auth) {
  return auth.authversion === 'v1' && auth.status !== 'pending' && auth.expired === 'no';
}