<?php

namespace App\Controllers\FlowPages;

use App\Controllers\BaseController;

class LandingPage extends BaseController
{
    private $theme = FLAG_THEME_DEFAULT;
    private $themeSlug = ''; //not used
    private $themePageVersion = 'v1'; //this is used for twig filename

    private $pageSlug = '';

    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        $this->theme = btflag($this->encryptedFlag['theme'], $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        if ($this->pageSlug!='' && strpos($this->pageSlug, 'splash')===false){
          btflag_set('landingpage',$this->pageSlug);
        }

        $this->theme_data();
        $this->theme_pageVersion();

        $fullHostName = $_SERVER['HTTP_HOST'];
        $checkDomain = str_contains($fullHostName, "chatapp");
        $isChatDashApp = str_contains($fullHostName, "chat-app");

        if ($checkDomain) {
            $this->theme_cerebro();
            return;
        }
        $path = $uri->getPath();
        $lastPart = basename(parse_url($path, PHP_URL_PATH));
        if ($lastPart == 'register-auth' || $isChatDashApp) {
            $this->theme_echo();
            setcookie('kt8typtb', 'echo', time() + (86400 * 30), '/');
            setcookie('kt8typtb', 'echo', time() + (86400 * 30), '/','.ai-pro.org');
            setcookie('flow', 'register-auth', time() + (86400 * 30), '/');
            setcookie('flow', 'register-auth', time() + (86400 * 30), '/','.ai-pro.org');
            setcookie('ppg', btflag('ppg', 97), time() + (86400 * 30), '/');
            setcookie('ppg', btflag('ppg', 97), time() + (86400 * 30), '/','.ai-pro.org');
            return;
        }
        switch ($this->theme) {
            case 'basilisk-03':
            case 'basilisk-02':
            case 'basilisk':
                $this->theme_basilisk();
                break;
            case 'druig':
                $this->theme_druig();
                break;
            case 'echo':
                $this->theme_echo();
                break;
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }


    public function show_404()
    {
        header("Status: 404 Not Found");
        $this->index();
    }

    public function free_chat()
    {
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;
        $this->theme_data();
        $this->themePageData['page_title'] = 'AI-Pro | Chatbot';

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => [
                'include_twitter' => true,
                'chatbot_url' => getenv('CHATBOT_URL') ? getenv('CHATBOT_URL') : "https://app.ai-pro.org/start-chatbot"
            ],
            'FLAGS' => json_encode(getAllFlags())
        ];
        return $this->bttwig->view("/lp/chatbot.twig", $viewData);
    }

    public function free_chatbotpro()
    {
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;
        $this->theme_data();
        $this->themePageData['page_title'] = 'Chatbot Pro | AI-Pro.org';

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => [
                'include_twitter' => true,
                'chatbotpro_url' => getenv('CHATBOTPRO_URL') ? getenv('CHATBOTPRO_URL') : "https://chat.aipro.org"
            ],
            'FLAGS' => json_encode(getAllFlags())
        ];
        return $this->bttwig->view("/lp/chatbotpro.twig", $viewData);
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {

        switch($this->pageSlug) {
            case 'chat-app':
                $this->theme_cerebro();
                return;
            case 'start-chatgpt-v3':
            case 'start-chatgpt-v4':
                $this->theme_basilisk();
                return;
        }
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_basilisk()
    {
        // basilisk has no custom pages for the following slug
        switch($this->pageSlug) {
            case 'redirect-account-required':
            case 'start-chatgpt-go':
            case 'start-stablediffusion':
            case 'start-chatgpt-v2':
                $this->theme_arcana();
                return;
        }
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_basilisk/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_cerebro()
    {
        // cerebro has no custom pages for the following slug
        switch($this->pageSlug) {
            case 'redirect-account-required':
                $this->theme_arcana();
                return;
        }
        $ppgFlag = btflag('ppg');
        btflag_set('ppg', $ppgFlag ? $ppgFlag : THEME_CEREBRO_PPG);

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_cerebro/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_druig()
    {

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_druig/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_echo()
    {

        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_echo/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
                'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => false,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag('admin','0')=='1'){
            // unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);
        }

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}

