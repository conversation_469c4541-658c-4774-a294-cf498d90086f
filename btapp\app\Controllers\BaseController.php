<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Custom
 */
use BT<PERSON>ore\TemplateEngine as BTTemplateEngine;
use BTCore\TemplateEngine\TwigCustom as BTTwig;
/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = ['btflag','btwp'];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * Custom
     */
    protected $_debugMode = (ENVIRONMENT == 'development');

    protected $bttwig;
    protected $bttwig_data = [];

    protected $encryptedFlag = [
        'theme' => 'kt8typtb',
        'restrict_lp_redirection' => 'qW1eMlya',
        'dynamic_prompts' => 'WcvYPABR'
    ];
    protected $flagsData = [];

    protected $view_data = [];

    protected $sessionId;

    protected $themeBaseData = [];
    protected $themePageData = [];

    protected $res_error = ['success' => 0]; //Error/Failed
    protected $res_ok = ['success' => 1]; //Successful
    protected $res_ok2 = ['success' => 2]; //Successful but no changes to apply
    protected $res_ok3 = ['success' => 3]; //Successful but no records found

    public function __construct()
    {
    }


    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        $this->helpers = array_merge($this->helpers, ['setting']);

        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();
        $twig = new BTTwig(APPPATH . 'Views/twig/', WRITEPATH . 'cache/twig/');
        $this->bttwig = new BTTemplateEngine();
        $this->bttwig->_init($twig);

        $this->setThemeBaseData();
    }

    //---------------------------------------------------------------------------------------------
    //  private
    //---------------------------------------------------------------------------------------------
    private function setThemeBaseData()
    {
        $this->themeBaseData = [
            'assets_common' => base_url() . "assets/common/",
            'base_url' => base_url(),
            'build' => date('mdYHis'),
            'year' => date("Y"),
        ];
        btflag('mode', FLAG_MODE_LIVE);
        btflag('ppg', FLAG_PPG_DEFAULT);
        btflag('pp_ctaclr', FLAG_PP_CTACLR);
        btflag('pmt', FLAG_PMT_DEFAULT);
        btflag('force_gateway', '');
        btflag('ppage', "");
        btflag('vprice', ""); // pricing page
        btflag('locales', ""); // locales
        btflag('vpay', ""); //stripe-like page
        btflag('navmenu', "show"); // hide/show menu in /start-chat-gpt lp
        btflag('splash', "off"); // splash page between /start-chat-gpt and /register
        btflag($this->encryptedFlag['restrict_lp_redirection'], ""); // flag for registration page. if loggedin, will redirect to lp
        btflag('ailplogo', "off"); // /start-chat-gpt OpenAI Logo on/off
        btflag('lp_vid', "off"); // on/off start-chat-gpt video flag
        btflag('via', ""); //rewardful affiliat program
        btflag('tp_reviews', "off"); // trustpilot slideshow for pricing
        btflag('members', ""); //redirect users to members area after registration on/off
        btflag('reg', ""); //specific lps with arabic content (?reg=ar)
        btflag('admin', ""); //for admin logins
        btflag('reg_google', ""); //display google registration button on/off
        btflag('reg_apple', ""); //display apple registration button on/off
        btflag('enterprise', ""); //enable enterprise price on pricing page on/off
        btflag('pricing', ""); // pricing flow from chat-app
        btflag('flow', ""); //flow=chatapp flow for chatapp lp
        btflag('cta_pmt', ""); //change cta text on payment page
        btflag('mobDisplayPlan', ""); // flip displayed plan on mobile
        btflag('keyword', ""); // marketing request for hubspot tracking
        btflag('smooth_login', 'off'); // on/off for login smooth input animation
        btflag('mode_test_no_pcheck', ''); // 1/0 to test proxy checking in mode test, set to 1 to turn off
        btflag('chatpdf', ''); // 01 - chatpdf redirection after registration
        btflag('force_pmt', ''); // 01 - chatpdf redirection after registration
        btflag('vwo', ''); // on/off - VWO flag
        btflag('adid', '');
        btflag('rdct', '');
        btflag('emailopt', '');
        btflag('p_toggle', '');  // pricing toggle behavior: 01, 02, 03
        btflag('daily', '');  // show daily cost of price
        btflag('desc_align', ''); // desc_align left/right description
        btflag('ishoneypot', 'no'); // desc_align left/right description
        btflag('pp_echo', ''); // pp_echo new pricing design echo
        btflag('acwp_ux', ''); // acwp_ux 

        $dynamic_prompts = btflag($this->encryptedFlag['dynamic_prompts'], FLAG_DYNAMIC_PROMPT);

        $login_token = isset($_GET['uzuxt']) ? $_GET['uzuxt'] : '';
        if ($login_token!==''){
            $this->destroySession(false);
            btflag_set('access', $login_token); //for master dashboard to login into customer account
            btflag_set('admin', "1");
        }

        //
        $user_ip = getClientIP();
        $user_ip = explode(",", $user_ip);
        $user_ip = trim($user_ip[0]);
        btflag_set('user_ip', $user_ip, ['expires' => 0.5]);

        $flow = btflag('flow');
        $theme = btflag($this->encryptedFlag['theme'], FLAG_THEME_DEFAULT);

        if ($flow === '06') {
            $theme = 'arcana';
        }

        $theme_value = ($theme == 'arcana_wp' || $theme == 'arcana' ? $theme : FLAG_THEME_DEFAULT);

        btflag_set($this->encryptedFlag['theme'], $theme_value);
        btflag_set($this->encryptedFlag['dynamic_prompts'], $dynamic_prompts);

        $_SESSION['token_root'] = md5(uniqid(mt_rand(), true));

        $this->flagsData = [
            'aiwp_logged_in' => btflag('aiwp_logged_in'),
            'mode' => btflag('mode', FLAG_MODE_LIVE),
            'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
            'pp_ctaclr' => btflag('pp_ctaclr', FLAG_PP_CTACLR),
            'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
            'vpay' => btflag('vpay', ""),
            'navmenu' => btflag('navmenu', "show"),
            'splash' => btflag('splash', "off"),
            'ailplogo' => btflag('ailplogo', "off"),
            'flow' => btflag('flow', ""),
            'emailid' => btflag('emailid', ""),
            'theme_flag_name' => $this->encryptedFlag['theme'],
            'theme_flag_value' => btflag($this->encryptedFlag['theme'], FLAG_THEME_DEFAULT),
        ];

        // this is only for testing 35194
        // test_end_date sha-256 encryption 8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7
        // test_current_date sha-256 encryption 6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529
        array_key_exists('8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7', $_GET) ? btflag('8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7',$_GET['8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7']) : btflag('8b470e22d5e2d1e9f5de3937e45ae9cbf24a12b4b563554162ce30ff7a6126b7');
        array_key_exists('6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529', $_GET) ? btflag('6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529',$_GET['6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529']) : btflag('6f92c68cbe0bcb74ac51524ddf5286bd136d860f193e9e2b478c39a53fa77529');
    }

    //---------------------------------------------------------------------------------------------
    //  protected
    //---------------------------------------------------------------------------------------------
    protected function bcTwigRender($_twig_file)
    {
        $loader = new \Twig\Loader\FilesystemLoader(APPPATH . 'Views');
        $grp = explode('-', $_twig_file);

        $twig = new \Twig\Environment($loader, [
            'cache' => WRITEPATH . 'cache/twig',
            'auto_reload' => true,
        ]);
        $data = $this->view_data;

        echo $twig->render('twig/' . $_twig_file . '.twig', $data);
        return;
    }

    protected function destroySession($auto_logout = true)
    {
        if(btsessionIsUserLoggedIn()) {
            $event_data = [
                'user_pid' => btsessionGet('USER')->user_pid,
                'email' => btsessionGet('USER')->email,
                'first_name' => btsessionGet('USER')->first_name,
                'last_name' => btsessionGet('USER')->last_name,
            ];
            if($auto_logout) {
                logUserActivity('auto_logout', $event_data);
            } else {
                logUserActivity('logout', $event_data);
            }
        }

        btsessionDestroy();
        $this->unsetAuthCookies();
    }

    protected function checkIfSubscriptionIsPaddleTrial()
    {
      if(btsessionHas('ACCOUNT') && btsessionHas('PLAN')
      && strtolower(btsessionGet('ACCOUNT')->merchant) == 'paddle'
      && btsessionGet('ACCOUNT')->status == 'active'
      && btsessionGet('PLAN')->trial_days) {
        return true;
      }

      return false;
    }

    protected function getUserAuthVersion($created_at)
    {
        $basicProLogicStartDate = strtotime(BASICPRO_LOGIC_STARTDATE);
        $userCreatedDate = strtotime($created_at);
        if (intval($userCreatedDate) < intval($basicProLogicStartDate)) {
            return "v1";
        }
        return "v2";
    }

    protected function unsetAuthCookies()
    {
        btflag_remove('aiwp_logged_in', '', ['domain' => '.ai-pro.org']);
        btflag_remove('aiwp_logged_in', '', ['domain' => 'ai-pro.org']);
        btflag_remove('aiwp_logged_in', '', ['domain' => 'localhost']);
        btflag_remove('aiwp_logged_in', '');

        btflag_remove('aiproStart', '', ['domain' => '.ai-pro.org']);
        btflag_remove('aiproStart', '', ['domain' => 'ai-pro.org']);
        btflag_remove('aiproStart', '', ['domain' => 'localhost']);
        btflag_remove('aiproStart', '');

        btflag_remove('user_email', '', ['domain' => '.ai-pro.org']);
        btflag_remove('user_email', '', ['domain' => 'ai-pro.org']);
        btflag_remove('user_email', '', ['domain' => 'localhost']);
        btflag_remove('user_email', '');

        btflag_remove('access', '', ['domain' => '.ai-pro.org']);
        btflag_remove('access', '', ['domain' => 'ai-pro.org']);
        btflag_remove('access', '', ['domain' => 'localhost']);
        btflag_remove('access', '');

        btflag_remove('chatpro_KJLF4XgSL8wjlGm', '', ['domain' => '.ai-pro.org']);
        btflag_remove('chatpro_KJLF4XgSL8wjlGm', '', ['domain' => 'ai-pro.org']);
        btflag_remove('chatpro_KJLF4XgSL8wjlGm', '', ['domain' => 'localhost']);
        btflag_remove('chatpro_KJLF4XgSL8wjlGm', '' );

        btflag_remove('unpFlow', '', ['domain' => '.ai-pro.org']); // #28596
        btflag_remove('unpFlow', '', ['domain' => 'ai-pro.org']);
        btflag_remove('unpFlow', '', ['domain' => 'localhost']);
        btflag_remove('unpFlow', '');

        btflag_remove('admin', '', ['domain' => '.ai-pro.org']);
        btflag_remove('admin', '', ['domain' => 'ai-pro.org']);
        btflag_remove('admin', '', ['domain' => 'localhost']);
        btflag_remove('admin', '');
    }

    protected function unsetAppCookies()
    {
        btflag_remove('app', '', ['domain' => '.ai-pro.org']);
        btflag_remove('app', '', ['domain' => 'ai-pro.org']);
        btflag_remove('app', '', ['domain' => 'localhost']);
        btflag_remove('app', '' );

        btflag_remove('appurl', '', ['domain' => '.ai-pro.org']);
        btflag_remove('appurl', '', ['domain' => 'ai-pro.org']);
        btflag_remove('appurl', '', ['domain' => 'localhost']);
        btflag_remove('appurl', '' );
    }

    protected function getUserByToken($token)
    {
        $user = btdbFindBy('UserModel', 'login_token', $token);
        if($user['success'] && $user['res']) return [ 'success' => 1, 'data' => $user['res'][0], 'raw' => $user ];
        return [ 'success' => 0, 'data' => [] ];
    }

    protected function getPlanRestriction($plan_id = null)
    {
        if(is_null($plan_id)) return array();
        $restrict_app = array();

        $chatpdf_restrict_planid = getenv("CHATPDF_PLAN_RESTRICT") ? json_decode(getenv("CHATPDF_PLAN_RESTRICT"), true) : [];
        if( in_array( $plan_id, $chatpdf_restrict_planid ) ) {
            array_push( $restrict_app, "chatpdf" );
        }

        return $restrict_app;
    }

    protected function setAuthCookies($email, $userData = [], $tk = null)
    {
        //Remove First
        $this->unsetAuthCookies();

        $userAppData = btdbFindBy('UserAppModel', 'email', $email);
        $userAppId = '';
        if ($userAppData['success']==3 && !sizeof($userAppData['res'])) {
            $userAppId = btutilGenerateUniqueId($email);
            $userAppEmail = $email;
            if ($userData['res'][0]->migrated) {
                $userAppId = btwp_hash($email, 'nQ6P[3BcFtDJ?Z%b');
            }
            $insertUserAppData = [
                'email' => $userAppEmail,
                'userapp_pid' =>  $userAppId,
            ];
            btdbUpdate('UserAppModel', 'UserApp', $insertUserAppData, 'new');
        }
        elseif ($userAppData['success']==1 && isset($userAppData['res'])) {
            $userAppId = $userAppData['res'][0]->userapp_pid;
        }

        $flags = $userData['res'][0]->flags;
        $flags = json_decode($flags);

        if (isset($flags->emailid)){
            btflag_set('emailid', $flags->emailid );
        }

        //Set
        $isAdmin = btflag('admin','0');
        btflag_set('aiwp_logged_in', $email . "|".$userAppId."|".$isAdmin, array('domain'=>'ai-pro.org'));
        btflag_set('chatpro_KJLF4XgSL8wjlGm', $email . "|".$userAppId, array('domain'=>'ai-pro.org'));

        if($tk) {
            btflag_set('access', $tk );
        }

				$aiwp_logged_in = $email . "|" . $userAppId . "|" . $isAdmin;
				return array(
					'aiwp_logged_in' => $aiwp_logged_in
				);
    }

    protected function reInitCookies()
    {
        btflag_set('aiwp_logged_in', btflag('aiwp_logged_in', null), array('domain'=>'ai-pro.org'));
        btflag_set('aiproStart', btflag('aiproStart', null));
        btflag_set('user_email', btflag('user_email', null));
        btflag_set('chatpro_KJLF4XgSL8wjlGm', btflag('chatpro_KJLF4XgSL8wjlGm', null), array('domain'=>'ai-pro.org'));
    }
}
