<?php

namespace App\DTOs;

use App\DTOs\CmsDTO;

/**
 * CmsCollectionPageDTO is a Data Transfer Object for handling CMS tag data.
 */
class CmsCollectionPageDTO extends CmsDTO
{
    public ?string $id = '';
    public ?string $name = '';
    public ?string $slug = '';
    public ?string $description = '';
    public ?string $title = '';
    public ?string $breadcrumb = '';
    public ?string $url = '';

    public ?string $updatedAt = '';

    // View-related properties
    public ?string $tag_title = '';
    public ?string $tag_name = '';
    public ?\stdClass $meta = null;

    /**
     * CmsCollectionPageDTO constructor.
     * Initializes properties from an array of data.
     *
     * @param array $data Associative array of properties to initialize.
     */
    public function __construct(array $data = [])
    {
        parent::__construct($data);
    }

    /**
     * Convert to view model.
     *
     * @return self
     */
    public function toView(): self
    {
        $this->tag_title = $this->title ?: $this->name;
        $this->tag_name = $this->name;
        $this->meta = (object)[
            'title' => $this->title ?? $this->name,
            'description' => $this->description ?? '',
        ];
        $this->updatedAt = !empty($this->date_update_gmt) ? date('c', strtotime($this->date_update_gmt)) : '';
        return $this;
    }

    /**
     * Create a CmsCollectionPageDTO from a Strapi result.
     *
     * @param array $result The result from Strapi.
     * @param string $uri   The current request URI.
     * @return CmsCollectionPageDTO
     */
    public static function fromStrapiResult(array $result, string $uri = ''): self
    {
        $dto = new self();

        if (empty($result['data']) || !is_array($result['data'])) {
            return $dto;
        }

        $dto->id = $result['data']['id'] ?? '';
        $dto->name = $result['data']['name'] ?? '';
        $dto->slug = $result['data']['slug'] ?? '';
        $dto->description = $result['data']['description'] ?? '';
        $dto->title = !empty($result['data']['seo']['metaTitle']) ? $result['data']['seo']['metaTitle'] : $dto->name;
        $dto->breadcrumb = $dto->title;
        $dto->url = $uri;
        $dto->site_name = $dto->getDomainForTitle();
 
        // Parse and format for: modified_time
        if (!empty($result['data']['post_modified_gmt']) && $date = date_create($result['data']['post_modified_gmt'])) {
            $dto->date_update = date_format($date, "F d Y");
            $dto->date_update_gmt = date_format($date, "c");
        } else {
            // there is always updatedAt, so we can use it as a fallback
            $updatedAt = $result['data']['updatedAt'] ?? '';
            if (!empty($updatedAt) && $date = date_create($updatedAt)) {
                $dto->date_update = date_format($date, "F d Y");
                $dto->date_update_gmt = date_format($date, "c");
            } else {
                $dto->date_update = '';
                $dto->date_update_gmt = '';
            }
        }

        return $dto;
    }
}
