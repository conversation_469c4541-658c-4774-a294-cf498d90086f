<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class MoveDataSeeder extends Seeder
{
    // OPTIONAL Run: php spark db:seed MoveDataSeeder
    public function run()
    {
        $source_db = \Config\Database::connect();
        $destination_db = \Config\Database::connect('aiapp');

        $batchSize = 1000;

        $totalRows = $source_db->table('user_log')->countAllResults();

        $numBatches = ceil($totalRows / $batchSize);

        for ($i = 0; $i < $numBatches; $i++) {
            $offset = $i * $batchSize;

            $source_data = $source_db->table('user_log')->limit($batchSize, $offset)->get()->getResultArray();

            $destination_db->table('user_log')->insertBatch($source_data);

            $progress = min(($offset + $batchSize), $totalRows);
            echo "Processed $progress out of $totalRows rows.\n";
        }
    }
}
