import SocialLoginButton from "./SocialLoginButton";

import apple_icon from "../assets/images/google_icon.png";
import apple_icon_black from "../assets/images/apple_icon_black.png";
import google_icon_black from "../assets/images/google_icon_black.png";
import {  GetCookie } from '../core/utils/cookies';
import { useTranslation } from 'react-i18next';

const RegisterButtons = () => {
  const { t } = useTranslation();
  
  const reg_google = GetCookie("reg_google") ? GetCookie("reg_google") : "off";
  const reg_apple = GetCookie("reg_apple") ? GetCookie("reg_apple") : "off";

  return (
    <div className="space-y-4">
      {reg_google === "on" && (
        <SocialLoginButton
          provider="google"
          variant="classic"
          imgSrc={apple_icon}
          text={t('basilisk.register.index.register_via_google')}
          redirectUrl="/google/register"
        />
      )}

      {reg_google === "02" && (
        <SocialLoginButton
          provider="google"
          variant="styled"
          imgSrc={google_icon_black}
          text="Continue with Google"
          redirectUrl="/google/register"
        />
      )}

      {reg_apple === "on" && ( 
        <SocialLoginButton
          provider="apple"
          variant="classic"
          imgSrc={apple_icon_black}
          text="Register via Apple"
          redirectUrl="/apple/register/0"
        />
      )}

      {reg_apple === "02" && (
        <SocialLoginButton
          provider="apple"
          variant="styled"
          imgSrc={apple_icon_black}
          text="Continue with Apple"
          redirectUrl="/apple/register/0"
        />
      )}
    </div>
  );
  
};

export default RegisterButtons;
