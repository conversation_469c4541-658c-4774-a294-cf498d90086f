<?php

namespace App\Controllers\User;

use App\Controllers\BaseController;

class Settings extends BaseController
{
    private $theme = 'arcana';
    private $themeSlug = '';
    private $themePageVersion = 'v1';

    private $pageSlug = '';
    private $themeData = [];


    //-------------------------------------------------------------------
    //  public
    //-------------------------------------------------------------------

    public function __construct()
    {
        // parent::__construct();
    }

    public function index()
    {
        if(!btsessionIsUserLoggedIn()) {
            header("Location: " . base_url('login'));
            die;
        }

        $start_url = getenv("start.baseURL") ? getenv("start.baseURL") : "https://start.ai-pro.org";
        header("Location: " . $start_url . "/manage-account"); // Redirect to start
        die;

        $user = btdbFindBy('UserModel', 'user_id', btsessionGET("USER")->user_id);
        if( $user['success'] && $user['res'] && $user['res'][0]->login_token ) btsessionSetAccount($user['res'][0], 1);
        else $this->destroySession();
        $this->theme = btflag('theme', $this->theme);
        $uri = current_url(true);
        $this->pageSlug = (($uri->getSegment(2))) ? $uri->getSegment(2): PAGE_MIXPANELNAME_DEFAULT;

        $this->themeData = [
            'active_tab' => isset($_GET['tab']) && $_GET['tab'] === 'mem' ? $_GET['tab'] : ''
        ];
        $this->theme_data();
        $this->theme_pageVersion();

        switch ($this->theme) {
            case 'arcana':
            default:
                $this->theme_arcana();
        }
    }

    public function changeCard() {
        $pid = $this->request->getGet('pid');
        if(!$pid) {
            header("Location: " . base_url("404"));
            die;
        }

        if(isset($req['tk'])) $user = $this->getUserByToken($req['tk']);
        if(btsessionIsUserLoggedIn()) {
            $user_id = btsessionGet('USER')->user_id;
        } else if($user && $user['success']) {
            $user = $user['data'];
            $user_id = $user->user_id;
        } else {
            header("Location: " . base_url("login"));
            die;
        }
        $acct = btdbFindBy('AccountModel', ['user_id', 'account_pid'], [$user_id, $pid]);
        if(!$acct['success'] || !$acct['res']) {
            header("Location: " . base_url("404"));
            die;
        }

        $this->index();
    }

    //-------------------------------------------------------------------
    //  protected
    //-------------------------------------------------------------------


    //-------------------------------------------------------------------
    //  private
    //-------------------------------------------------------------------

    private function theme_arcana()
    {
        $viewData = [
            'BASE' => $this->themeBaseData,
            'PAGE' => $this->themePageData,
            'DATA' => json_encode($this->themeData),
            'FLAGS' => json_encode(getAllFlags())
        ];

        $twigFile = $this->themeSlug;
        // do not edit this
        $this->bttwig->view("/theme_arcana/index_{$this->themePageVersion}.twig", $viewData);
    }

    private function theme_data()
    {
        $this->themePageData = [
            'include_session' => [
                'ctx' => base_url(),
            ],
            'include_twitter' => true,
            'page_title' => 'AI-Pro',
            'meta_data' => [
                'description' => '',
                // 'robots' => 'noindex, follow', //uncomment and change as needed
            ],
            'include_vwo' => btutilIsVwoOn(),
            'include_fbmeta' => true,
            'include_tiktok' => true,
            'include_quora' => true,
            'include_mixpanel' => [
                'debug' => CONFIG_MIXPANEL_DEBUG,
                'mixpanel_name' => $this->pageSlug,
                'keyword' => btflag('keyword', ''),
'locales' => btflag('locales', ''),
                'emailid' => btflag('emailid', ''),
                'adid' => btflag('adid', ''),
                'flow' => btflag('flow', ''),
                'ppg' => btflag('ppg', FLAG_PPG_DEFAULT),
                'pmt' => btflag('pmt', FLAG_PMT_DEFAULT),
                'email' => btflag_cookie('user_email', ''),
                'user_plan' => btflag_cookie('user_plan', ''),
                'howdoiplantouse' => btflag_cookie('howdoiplantouse', ''),
                'remakemedloption' => btflag_cookie('remakemedloption', ''),
            ],
            'include_gtag_AW' => false,
            'include_gtag_GA4' => true,
            'include_bing' => true,
            'include_fullstory' => [
                'email' => btflag_cookie('user_email', ''),
            ],
        ];

        if (btflag_cookie('admin','0')=='1'){
            unset($this->themePageData['include_fullstory']);
            unset($this->themePageData['include_mixpanel']);        
        }

    }

    private function theme_pageVersion()
    {
        // not yet really used
        $this->themePageVersion = btflag_get('v', $this->themePageVersion);
    }
}
