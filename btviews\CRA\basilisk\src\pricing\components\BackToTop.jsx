import React, { useState, useEffect } from 'react';
import { FaArrowUp } from 'react-icons/fa';
import '../style.css'

const BackToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  const checkScrollPosition = () => {
    const scrollTop = window.scrollY;
    const windowHeight = window.innerHeight;
    const fullHeight = document.documentElement.scrollHeight;

    setIsVisible(scrollTop + windowHeight >= fullHeight * 0.9);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  useEffect(() => {
    window.addEventListener('scroll', checkScrollPosition);
    return () => window.removeEventListener('scroll', checkScrollPosition);
  }, []);

  return (
    isVisible && (
      <button
        onClick={scrollToTop}
        className="fixed bottom-6 w-[36px] h-[36px] right-6 z-50 bg-[#3a4f66] text-white p-[11px] rounded-full shadow-lg hover:bg-blue-600 transition"
        aria-label="Back to Top"
      >
        <FaArrowUp size={14} />
      </button>
    )
  );
};

export default BackToTop;
