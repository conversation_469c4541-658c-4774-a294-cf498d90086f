import { useEffect } from "react";

export default function Chatfieldmodal({
    isOpen,
    handleClose,
    handleEdit,
    handleSubmit,
}) {
    useEffect(() => {
        if (isOpen && window.hbspt) {
            window.hbspt.forms.create({
                region: "na1",
                portalId: "44168812",
                formId: "4a67c003-4266-4ceb-8ac8-9cde0ea72eaf",
                target: "#hubspotFormMobile",
                cssRequired: true,
                onFormReady: function($form) {
                        document.getElementsByName("firstname")[0].placeholder = "e.g. <PERSON>";
                        document.getElementsByName("lastname")[0].placeholder = "e.g. Doe";
                        document.getElementsByName("email")[0].placeholder = "e.g. <EMAIL>";
                        document.getElementsByName("company")[0].placeholder = "e.g. Acme Corp.";
                        document.getElementsByName("jobtitle")[0].placeholder = "e.g. Marketing Manager";
                        document.getElementsByName("phone")[0].placeholder = "e.g. ****** 567 8900";

                        const link = document.querySelector('.legal-consent-container a');
                        link.href = '/privacy-policy';
                },
            });
        }
    }, [isOpen]);

    if (!isOpen) return undefined;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="relative bg-white rounded-xl shadow-lg top-0 left-0 w-full h-full overflow-y-auto">
                {/* × Close Button (Top-Right) */}
                <button
                    onClick={handleClose}
                    className="absolute top-2 right-[1rem] text-[34px] opacity-40 text-gray-500 hover:text-gray-700"
                    aria-label="Close modal"
                >
                    &times;
                </button>

                <div id="hubspotFormMobile" />
                <div className="w-full hidden">
                    <div className="space-y-4 bg-white pt-[50px] pb-[18px] px-[38px]">
                        <div className="text-left pb-[20px]">
                            <h3 className="text-[26px] font-bold">Fill out our contact form and we'll get in touch with you.</h3>
                        </div>
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <label htmlFor="mobile-first-name" className="text-base font-semibold block">
                                    First Name*
                                </label>
                                <input
                                    id="mobile-first-name"
                                    placeholder="e.g. John"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-last-name" className="text-base font-semibold block">
                                    Last Name*
                                </label>
                                <input
                                    id="mobile-last-name"
                                    placeholder="e.g. Doe"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-company-name" className="text-base font-semibold block">
                                    Company Name*
                                </label>
                                <input
                                    id="mobile-company-name"
                                    placeholder="e.g. Acme Corp."
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-job-title" className="text-base font-semibold block">
                                    Job Title*
                                </label>
                                <input
                                    id="mobile-job-title"
                                    placeholder="e.g. Marketing Manager"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-email" className="text-base font-semibold block">
                                    Email Address*
                                </label>
                                <input
                                    id="mobile-email"
                                    type="email"
                                    placeholder="e.g. <EMAIL>"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-phone" className="text-base font-semibold block">
                                    Phone Number*
                                </label>
                                <input
                                    id="mobile-phone"
                                    type="tel"
                                    placeholder="e.g. ****** 567 8900"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-company-size" className="text-base font-semibold block">
                                Company Size
                                </label>
                                <select
                                    id="mobile-company-size"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 appearance-none bg-white"
                                    onChange={(e) => handleEdit(e)}
                                >
                                    <option value="">Please Select</option>
                                    <option value="self-employed">Self-employed</option>
                                    <option value="1-10">1-10 employees</option>
                                    <option value="11-50">11-50 employees</option>
                                    <option value="51-200">51-200 employees</option>
                                    <option value="201-500">201-500 employees</option>
                                    <option value="501-1000">501-1,000 employees</option>
                                    <option value="1001-5000">1,001-5,000 employees</option>
                                    <option value="5001-10000">5,001-10,000 employees</option>
                                    <option value="10001+">10,001+ employees</option>
                                </select>
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-reason" className="text-base font-semibold block">
                                    Reason for completing the form*
                                </label>
                                <select
                                    id="mobile-reason"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2 appearance-none bg-white"
                                    onChange={(e) => handleEdit(e)}
                                >
                                    <option value="">Please Select</option>
                                    <option value="support">I need help from your support agents.</option>
                                    <option value="custom">I have a question about your AI solutions.</option>
                                    <option value="enterprise">I have a question about your Enterprise plan.</option>
                                </select>
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="mobile-comments" className="text-base font-semibold block">
                                    Phone Number*
                                </label>
                                <textarea
                                    id="mobile-comments"
                                    className="h-12 w-full rounded-md border border-gray-300 px-3 py-2"
                                    onChange={(e) => handleEdit(e)}
                                />
                            </div>
                            <button className="gradient-button w-full h-12 rounded-md text-white text-base" onClick={handleSubmit}>Submit Form</button>
                            <p className="text-xs text-gray-500 text-center">
                                By clicking "Submit" I agree to receive email or phone communications about your services, offers,
                                and promotions. We use your information as described in our{" "}
                                <a href="/privacy-policy" target="_blank" rel="noreferrer" className="text-blue-600 hover:underline">
                                Privacy Notice
                                </a>
                                .
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};