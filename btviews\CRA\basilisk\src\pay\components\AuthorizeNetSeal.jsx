const AuthorizeNetSeal = () => {
    const customer_id = 'fa6c373e-7150-4c4e-9211-109465bb15b1'
    const domain = 'https://ai-pro.org'

    const handleClick = (e) => {
        e.preventDefault();
        
        window.open(
            `https://verify.authorize.net/anetseal/?pid=${customer_id}&rurl=${domain}`,
            'AuthorizeNetSeal',
            'width=400,height=430,toolbar=no,scrollbars=no,resizable=no'
        )
    }

    return (
        <a
            href={`https://verify.authorize.net/anetseal/?pid=${customer_id}&rurl=${domain}`}
            onClick={handleClick}
            rel="noopener noreferrer">
            <img
                src="https://verify.authorize.net/anetseal/images/secure90x72.gif"
                alt="Authorize.Net Merchant - Click to Verify"
                width="90"
                height="72"
                style={{ border: 'none' }}
            />
        </a>
    )
}

export default AuthorizeNetSeal
