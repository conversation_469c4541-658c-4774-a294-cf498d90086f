.pricing {
  /* background-color: #fff; */
  /* border: 1px solid #ccc;*/
  padding: 20px;
}
.flow04-bg{
  background: url('../assets/images/ai-pro-bg.jpg') no-repeat center center;
  background-size: cover;
  background-attachment: fixed;
  background-position: top;
}
@media screen and (max-width: 767px) {
   .flow04-bg{
    overflow: hidden;
  }
}
.price_col {
  /* width: 280px; */
  text-align: center;
}
.price_col i.fas,
.price_col i.fa {
  font-size: 1rem;
}
.price_col ul li > div::before {
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900);
  content: "\f00c";
  margin-right: 5px;
  color: #3b82f6;
  display: none;
}
.price_col ul li > div {
  /* margin-bottom: 4px; */
}

.price_col > div {
  height: 100%;
  position: relative;
  background: #eaedf0;
  box-shadow: 2px 0 30px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 0 9px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 2px 0 30px rgba(0, 0, 0, 0.1);
}

.price_col > div .price-content {
  margin-bottom: 30px;
  background: #eaedf0 !important;
}

.price_col > div button {
  position: absolute;
  bottom: 20px;
  left: calc(50% - 100px);
}

.span-highlight {
  height: auto !important;
  position: absolute !important;
}

.p_main {
  font-weight: bold;
}

@media (max-width: 500px) {
  .price_col {
    width: 100%;
  }
}

.pricing-toggle .price_col {
  /* width: 400px; */
  text-align: center;
  position: relative;
}
.pricing-toggle .price_col i.fas,
.price_col i.fa {
  font-size: 1rem;
}
.pricing-toggle.price_col ul li > div::before {
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900);
  content: "\f00c";
  margin-right: 5px;
  color: #3b82f6;
  position: absolute;
  left: 30px;
}

.pricing-toggle .price_col ul li > div {
  /* margin-bottom: 10px; */
}

.pricing-toggle .price_col > div {
  height: 100%;
  position: relative;
}

.pricing-toggle .price_col > div .price-content {
  margin-bottom: 50px;
}

.pricing-toggle .span-highlight {
  height: auto !important;
  position: absolute !important;
  width: calc(100% - 2rem);
  padding: 10px;
}

.pricing-toggle .plan-description {
  text-align: left;
  margin-left: 25px;
}
.price-col:hover{
  transform: translateY(-5deg)
}

.price-content .p_main {
  font-weight: bold;
}

.price-content .p_sub {
  font-style: italic;
  text-align: center;
}

@media (max-width: 500px) {
  .pricing-toggle .price_col {
    width: 100% !important;
  }
}

.description-tooltip {
  border: 3px solid #2872fa;
  position: absolute;
  bottom: 30px;
  background-color: #fff;
  color: #333;
  padding: 5px;
  border-radius: 5px;
  width: auto;
  max-width: 320px;
  font-size: 11px;
  margin-top: -35px;
  box-shadow: 0 3px 15px -13px #000;
}

.description-tooltip:after,
.description-tooltip:before {
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  position: absolute;
  left: calc(50% - 10px);
  border-top: 0;
  content: "";
  display: block;
  height: 0;
  transform: rotate(-180deg);
}

.description-tooltip:before {
  border-bottom: 10px solid #2872fa;
  z-index: 1;
  bottom: -10px;
}

.description-tooltip:after {
  border-bottom: 10px solid #fff;
  z-index: 2;
  bottom: -6px;
}

.description-tooltip {
  display: none;
}

.hover-target {
  position: relative;
  cursor: pointer;
}

.hover-target:hover .description-tooltip {
  display: block;
  line-height: 15px;
}
.indent {
  padding-left: 20px;
}
.discount-text {
  font-size: 15px;
  color: #fff;
}
.discount-bubble {
  background-color: #fff;
  color: blue;
  border-radius: 12px;
  padding: 3px 7px 3px 7px;
  font-weight: bold;
  font-size: 15px;
}
.poppins-font{
  font-family: Poppins, 'Segoe UI', Roboto, Oswald, arial, sans-serif !important;  
}
.system{
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.back-to-top {
  position: fixed;
  bottom: 24px;
  right: 24px;
  background-color: #3a4f66;
  color: white;
  padding: 12px;
  border-radius: 9999px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: background-color 0.3s ease;
}

.back-to-top:hover {
  background-color: #1d5fd1;
}

.advanced-models::before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f00c";
  margin-right: 3px;
  color: #2872fa;
  float: inline-start;
}

