<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* /theme_arcana/index_v1.twig */
class __TwigTemplate_7ffb6bfd6e221bb7cbdb38e65d3f2c84 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<!doctypehtml><html lang=\"en\"><meta charset=\"utf-8\"/><link rel=\"icon\"href=\"/themes/arcana/favicon.ico\"/><meta name=\"viewport\"content=\"width=device-width,initial-scale=1\"/><meta name=\"theme-color\"content=\"#000000\"/><link rel=\"apple-touch-icon\"href=\"/themes/arcana/logo192.png\"/><link rel=\"manifest\"href=\"/themes/arcana/manifest.json\"/><title>";
        echo twig_escape_filter($this->env, twig_get_attribute($this->env, $this->source, ($context["PAGE"] ?? null), "page_title", [], "any", false, false, false, 1), "html", null, true);
        echo "</title><link rel=\"preconnect\"href=\"https://fonts.gstatic.com\"crossorigin><link rel=\"preconnect\"href=\"https://fonts.googleapis.com\"><script id=\"cookieyes\"type=\"text/javascript\"src=\"https://cdn-cookieyes.com/client_data/8376674a75f51d7de2129067/script.js\"></script><style>#modal-container{font-family:'Alegreya Sans',sans-serif!important;z-index:9999999}button.close{float:right;font-size:25px}#modal-container-enterprise{display:none;position:fixed;z-index:9999;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,.5)}header.openchat-relative{position:relative!important;left:0!important;top:0!important;width:100%!important;transform:unset!important}header.openchat-p-2{position:relative!important;left:0!important;top:0!important;width:100%!important;transform:unset!important}header.openchat-py-4{position:relative!important;left:0!important;top:0!important;width:100%!important;transform:unset!important}.openchat-bg-accent2{background-color:gray!important}.appbox{--tw-border-opacity:1!important;border-color:rgb(229 231 235 / var(--tw-border-opacity))!important}.openchat-text-base.openchat-font-semibold{max-width:75%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.fade-in.openchat-px-2.openchat-py-1.openchat-font-bold.openchat-text-center.openchat-text-lg.openchat-overflow-hidden.openchat-truncate.openchat-absolute{max-width:75%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.openchat-w-full.openchat-bg-white.openchat-flex.openchat-flex-col.openchat-gap-2.openchat-p-2.openchat-text-base.openchat-leading-6.openchat-mt-5.openchat-rounded-lg.openchat-shadow{display:none}.openchat-fixed.openchat-bottom-5.openchat-right-5.z-50{z-index:8889!important}</style>";
        $this->loadTemplate("includes/head_session.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_mixpanel.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_vwo.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_quora.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_fbmeta.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_tiktok.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_twitter.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_analytics.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_bing.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_rewardful.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_fullstory.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_hubspot.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo " ";
        $this->loadTemplate("includes/head_gtag.twig", "/theme_arcana/index_v1.twig", 1)->display($context);
        echo "<meta http-equiv=\"Cache-Control\"content=\"public, max-age=604800\"><script defer=\"defer\"src=\"/themes/arcana/static/js/8651.7c5b1aac.js\"></script><script defer=\"defer\"src=\"/themes/arcana/static/js/main.7f36c65f.js\"></script><body><noscript>You need to enable JavaScript to run this app.</noscript><div id=\"root\"></div><form data-rewardful></form><div id=\"modal-container\"class=\"no-subtitle\"style=\"display:none\"><div id=\"modal-content\"><button type=\"button\"class=\"close\"data-dismiss=\"modal\"aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button><h2 id=\"modal-title\">Switch to <span class=\"plan-name\">PRO</span> to continue using <span class=\"app-name\">ChatGPTPro</span></h2><p id=\"modal-desc\">Switch now to <span class=\"plan-name\">PRO</span> and get access to <span class=\"app-total\">14</span> different creativity and productivity AI tools.</p><a id=\"modal-cta\"href=\"#\"target=\"_parent\">Continue</a></div></div><div id=\"modal-container-enterprise\"class=\"no-subtitle\"style=\"display:none\"><div id=\"modal-content\"><button type=\"button\"class=\"close\"data-dismiss=\"modal\"aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button><h2 id=\"modal-title\">Active Subscription Required</h2><p id=\"modal-desc\">Your subscription is either expired or currently inactive.<br>For continuous access, kindly reach out to our support team.</p><a id=\"modal-cta\"href=\"https://ai-pro.org/contact-us/\"target=\"_blank\">Contact Support</a></div></div><script defer=\"defer\">var view_data = []; ";
        if (array_key_exists("DATA", $context)) {
            echo " view_data = ";
            echo ($context["DATA"] ?? null);
            echo "; ";
        }
        echo " var user_data = []; ";
        if (array_key_exists("USER", $context)) {
            echo " user_data = ";
            echo ($context["USER"] ?? null);
            echo "; ";
        }
        echo " var baseURL = \"http://localhost:9001/\"; var start_URL = \"http://localhost:9001/\";</script><script defer=\"defer\">let ver=(new Date).getTime(),btutilAssetCSS=document.createElement(\"link\");btutilAssetCSS.setAttribute(\"rel\",\"stylesheet\"),btutilAssetCSS.setAttribute(\"href\",\"https://dev.api.ai-pro.org/ext-app/css/btutil-regUpgradeModal-v1.min.css?ver=\"+ver),document.head.appendChild(btutilAssetCSS)</script><script defer=\"defer\">document.querySelector(\"#modal-container\").addEventListener(\"click\",(function(e){e.target&&\"modal-container\"==e.target.id&&(document.getElementById(\"modal-container\").style.display=\"none\")})),document.querySelector(\"#modal-container .close\").addEventListener(\"click\",(function(){document.getElementById(\"modal-container\").style.display=\"none\"})),document.querySelector(\"#modal-container-enterprise\").addEventListener(\"click\",(function(e){e.target&&\"modal-container-enterprise\"==e.target.id&&(document.getElementById(\"modal-container-enterprise\").style.display=\"none\")})),document.querySelector(\"#modal-container-enterprise .close\").addEventListener(\"click\",(function(){document.getElementById(\"modal-container-enterprise\").style.display=\"none\"})),document.addEventListener(\"DOMContentLoaded\",(function(){\"/checkout-page-rec\"===window.location.pathname&&(document.body.style.backgroundColor=\"white\")}))</script>";
    }

    public function getTemplateName()
    {
        return "/theme_arcana/index_v1.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "/theme_arcana/index_v1.twig", "C:\\Users\\<USER>\\Documents\\git.baytech.ph\\ai-pro.org\\btapp\\app\\Views\\twig\\theme_arcana\\index_v1.twig");
    }
}
